package com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class DouyinLiveInfo implements Serializable {


    @Schema(title = "用户昵称")
    @JsonAlias("tkw_nickname")
    private String nickname;

    @Schema(title = "kolId")
    @JsonAlias("kw_kolId")
    private String kolId;

    @Schema(title = "性别")
    @JsonAlias("kw_gender")
    private String gender;

    @Schema(title = "认证信息")
    private String verifiedType;

    @Schema(title = "是否入驻直播广场")
    private Boolean isLivePlaza;

    @Schema(title = "粉丝数")
    private Long followersCount;
    private String mcnName;                 // MCN名称
    private List<String> userTags;           // 用户标签
    private List<String> industryTags;       // 行业标签


    @Schema(title = "品牌推广专场(按小时)价格")
    @JsonAlias("long_priceBrandPromotionByHour")
    private Long priceBrandPromotionByHour;

    @Schema(title = "品牌推广专场(按天)价格")
    @JsonAlias("long_priceBrandPromotionByDay")
    private Long priceBrandPromotionByDay;


    @Schema(title = "近期直播场次")
    @JsonAlias("long_liveCnt")
    private Long liveCnt;

    @Schema(title = "平均观看人次")
    @JsonAlias("long_watchUCnt")
    private Long watchUCnt;

    @Schema(title = "平均观看人数")
    @JsonAlias("long_watchCnt")
    private Long watchCnt;

    @Schema(title = "小手柄场均CTR")
    @JsonAlias("double_handleCtr")
    private Double handleCtr;

    @Schema(title = "小手柄场均CVR")
    @JsonAlias("double_handleCrv")
    private Double handleCrv;

    @Schema(title = "小雪花场均CTR")
    @JsonAlias("double_snowflakeCtr")
    private Double snowflakeCtr;

    @Schema(title = "小风车场均CTR")
    @JsonAlias("double_windmillCtr")
    private Double windmillCtr;


    @Schema(title = "ACU平均同时在线用户数")
    @JsonAlias("long_acu")
    private Long acu;

    @Schema(title = "PCU最高同时在线用户数")
    @JsonAlias("long_pcu")
    private Long pcu;

    @Schema(title = "ACU排名")
    @JsonAlias("double_acuPercent")
    private Double acuPercent;

    @Schema(title = "场均CTR")
    @JsonAlias("double_ctr")
    private Double ctr;


    @Schema(title = "场均评论率")
    @JsonAlias("double_commentRate")
    private Double commentRate;

    @Schema(title = "场均点赞率")
    @JsonAlias("double_likeRate")
    private Double likeRate;


    @Schema(title = "平均看播时长")
    @JsonAlias("long_avgWatchDuration")
    private Long avgWatchDuration;

    @Schema(title = "粉丝观看占比")
    @JsonAlias("double_fansWatchRate")
    private Double fansWatchRate;


    private String updateTime;      // 更新时间


    @Schema(title = "直播间列表")
    @JsonAlias("object_latestLiveRooms")
    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    private List<ObjectLatestLiveRooms> objectLatestLiveRooms;


    @Data
    public static class ObjectLatestLiveRooms implements Serializable {

        @Schema(title = "标题")
        @JsonAlias("tx_title")
        private String title;


        @JsonAlias("long_acu")
        private Long acu;

        @JsonAlias("long_pcu")
        private Long pcu;

        //直播评论率
        @JsonAlias("double_commentRate")
        private Double commentRate;

        //直播点赞率
        @JsonAlias("double_likeRate")
        private Double likeRate;

        @Schema(title = "时长")
        @JsonAlias("long_duration")
        private Long duration;

        //直播日期
        private String publishedAt;

        //封面图
        @JsonAlias("kw_coverUrl")
        private String coverUrl;


        //直播ID
        @JsonAlias("room_id")
        private String roomId;


        //观看人数
        @JsonAlias("watch_ucnt")
        private Long watchUCount;

        //观看人次
        @JsonAlias("watch_cnt")
        private Long watchCount;

        //直播CTR
        @JsonAlias("ctr")
        private Double ctr;


    }

}
