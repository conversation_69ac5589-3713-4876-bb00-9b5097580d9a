package com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DouyinKolInfo {

    private String nickname;      // 用户昵称
    private String platformUserId;                 // 抖音号
    private String userId;           // 用户UID
    private String verifiedType;            // 认证类型
    private Boolean isSettled;    // 是否入驻
    private Long followersCount;       // 粉丝数
    private String mcnId;                   // MCN ID
    private String mcnName;                 // MCN名称
    private List<String> userTags;           // 用户标签
    private List<String> industryTags;       // 行业标签
    private Long price1To20s;         // 1-20s报价
    private Long price21To60s;        // 21-60s报价
    private Long price60sPlus;        // 60s以上报价
    private Long expectedPlayNum;   //预期播放量


    private Double cpm;    //预期CPM
    private Double cpm_1_20;    //预期CPM_1_20
    private Double cpm_21_60;    //预期CPM_20_60
    private Double cpm_60;    //预期CPM_60
    private Double cpe;    //预期CPE
    private Double cpe_1_20;    //预期CPE_1_20
    private Double cpe_21_60;    //预期CPE_20_60
    private Double cpe_60;    //预期CPE_60


    private Long readMedian; //商单视频播放中位数
    private Double playOverRate; //商单视频完播率
    private Double playOverRateBeyondRate;   //商单视频完播率在所有达人中的比例
    private Double interactionRate;  //商单视频互动率
    private Double interactionBeyondRate;    //商单视频互动率在所有达人中的比例


    private Long n30dPlayMedian; //近30天视频播放量
    private Double n30dPlayOverRate; //近30天视频完播率
    private Double n30dInteractionRate;  //近30天视频互动率
    private Long n90dPlayMedian; //近90天视频播放量
    private Double n90dPlayOverRate; //近90天视频完播率
    private Double n90dInteractionRate;  //近90天视频互动率
    private String updateTime;      // 更新时间

    private List<PaidPostInfo> postList;

    @Data
    public static class PaidPostInfo implements Serializable {
        private Boolean isPaid; // 是否商单
        private String postId;     // 内容ID
        private Long readCount;       // 阅读/播放数
        private String coverUrl;      // 封面URL
        private String title;         // 标题     实际为正文   tx_content
        private String url;           // 内容URL
        private Long duration;     // 视频时长（秒）
        private String publishTime;     // 发帖时间
        private Long repostsCount;      // 转发数
        private Long commentCount;    // 评论数
        private Long likeCount;        // 点赞数
        private Long collectCount;     // 收藏数
        private Long interaction; // 互动量    转发+评论+点赞+收藏
    }

}
