package com.tarsocial.bigital.kol.common.domain.entity;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class QuarkUserInfo implements Serializable {

    @Schema(title = "用户ID")
    @JsonAlias("kw_userId")
    private String userId;

    @Schema(title = "平台")
    @JsonAlias("kw_platform")
    private String platform;

    @Schema(title = "kolId")
    @JsonAlias("kw_kolId")
    private String kolId;

    @Schema(title = "用户昵称")
    @JsonAlias("tkw_nickname")
    private String nickname;

    @Schema(title = "粉丝数")
    @JsonAlias("long_followersCount")
    private Long followersCount;

    @Schema(title = "粉丝数")
    @JsonAlias("kw_verifiedType")
    private String verifiedType;

    @Schema(title = "性别")
    @JsonAlias("kw_gender")
    private String gender;

    @Schema(title = "星图信息")
    @JsonAlias("object_star")
    private ObjectStar objectStar;

    @Schema(title = "用户标签")
    @JsonAlias("kw_tags")
    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    private List<String> tags;

    @Schema(title = "行业标签")
    @JsonAlias("kw_industryTags")
    private List<String> industryTags;

    @Schema(title = "直播达人信息")
    @JsonAlias("object_liveStar")
    private ObjectLiveStar objectLiveStar;


    @Data
    public static class ObjectStar implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "mcn名称")
        @JsonAlias("kw_mcnName")
        private String mcnName;
    }

    @Data
    public static class ObjectLiveStar implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "带货类目信息")
        @JsonAlias("object_category")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<ObjectCategory> objectCategory;

        @Schema(title = "带货品牌信息")
        @JsonAlias("object_brand")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<ObjectBrand> objectBrand;

        @Schema(title = "周均直播场次")
        @JsonAlias("double_weeklyLiveCnt")
        private Double weeklyLiveCnt;

        @Schema(title = "指数信息")
        @JsonAlias("object_index")
        private ObjectIndex objectIndex;

        @Schema(title = "预期CPM")
        @JsonAlias("double_expectancyCPM")
        private Double expectancyCPM;

        @Schema(title = "预期观看人数")
        @JsonAlias("long_expectancyPlayCnt")
        private Long expectancyPlayCnt;

        @Schema(title = "最高在线人数")
        @JsonAlias("long_expectancyPcuCnt")
        private Long expectancyPcuCnt;

        @Schema(title = "预期最高在线人数")
        @JsonAlias("long_expectancyMaxOnlineCnt")
        private Long expectancyMaxOnlineCnt;

        @Schema(title = "历史合作行业")
        @JsonAlias("kw_industries")
        private List<String> industries;

        @Schema(title = "直播广场")
        @JsonAlias("object_priceInfos")
        private ObjectPriceInfos objectPriceInfos;

        @Schema(title = "直播粉丝画像")
        @JsonAlias("object_liveFansDistribution")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<ObjectLiveFansDistribution> objectLiveFansDistribution;

        @Schema(title = "直播观众画像")
        @JsonAlias("object_liveViewerDistribution")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<ObjectLiveViewerDistribution> liveViewerDistribution;

        @Schema(title = "直播数据概览")
        @JsonAlias("object_spreadInfo")
        private ObjectSpreadInfo objectSpreadInfo;

        @Schema(title = "直播间列表")
        @JsonAlias("object_latestLiveRooms")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<ObjectLatestLiveRooms> objectLatestLiveRooms;

    }

    @Data
    public static class ObjectIndex implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(title = "粉丝指数")
        @JsonAlias("object_addFans")
        private ObjectAddFans objectAddFans;

    }

    @Data
    public static class ObjectAddFans implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(title = "指数值")
        @JsonAlias("double_value")
        private Double value;

        @Schema(title = "指数均值")
        @JsonAlias("double_avgValue")
        private Double avgValue;

        @Schema(title = "指数排名")
        @JsonAlias("long_rank")
        private Long rank;

        @Schema(title = "指数百分位")
        @JsonAlias("double_percent")
        private Double percent;

        @Schema(title = "指数排名(同级)")
        @JsonAlias("long_fansRank")
        private Long fansRank;

        @Schema(title = "指数百分位(同级)")
        @JsonAlias("double_fansPercent")
        private Double fansPercent;
    }

    @Data
    public static class ObjectCategory implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(title = "带货类目名称")
        @JsonAlias("kw_name")
        private String name;

        @Schema(title = "带货类目占比")
        @JsonAlias("double_rate")
        private Double rate;

        @Schema(title = "带货类目排名")
        @JsonAlias("long_rank")
        private Long rank;
    }

    @Data
    public static class ObjectBrand implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(title = "带货品牌名称")
        @JsonAlias("kw_name")
        private String name;

        @Schema(title = "带货品牌排名")
        @JsonAlias("long_rank")
        private Long rank;
    }

    @Data
    public static class ObjectPriceInfos implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "品牌推广专场(按小时)价格")
        @JsonAlias("long_priceBrandPromotionByHour")
        private Long priceBrandPromotionByHour;

        @Schema(title = "品牌推广专场(按天)价格")
        @JsonAlias("long_priceBrandPromotionByDay")
        private Long priceBrandPromotionByDay;

        @Schema(title = "电商带货专场价格")
        @JsonAlias("long_priceECommerceSpecial")
        private Long priceECommerceSpecial;

        @Schema(title = "电商带货拼场价格")
        @JsonAlias("long_priceECommerceConsolidation")
        private Long priceECommerceConsolidation;

        @Schema(title = "明星电商直播价格")
        @JsonAlias("long_priceECommerceCelebrity")
        private Long priceECommerceCelebrity;

        @Schema(title = "品牌推广专场(按商品)价格")
        @JsonAlias("long_priceBrandPromotionByGoods")
        private Long priceBrandPromotionByGoods;
    }


    @Data
    public static class ObjectLiveFansDistribution implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(title = "画像维度")
        @JsonAlias("kw_display")
        private String display;

        @Schema(title = "画像总结")
        @JsonAlias("kw_description")
        private String description;

        @Schema(title = "画像详情")
        @JsonAlias("object_distribution")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<ObjectDistribution> objectDistribution;
    }

    @Data
    public static class ObjectDistribution implements Serializable {

        private static final long serialVersionUID = -21328915040840523L;

        @Schema(title = "key")
        @JsonAlias("kw_key")
        private String key;

        @Schema(title = "count")
        @JsonAlias("double_value")
        private Double value;
    }

    @Data
    public static class ObjectLiveViewerDistribution implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "画像维度")
        @JsonAlias("kw_display")
        private String display;

        @Schema(title = "画像总结")
        @JsonAlias("kw_description")
        private String description;

        @Schema(title = "画像详情")
        @JsonAlias("object_distribution")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<ObjectDistribution> objectDistribution;
    }

    @Data
    public static class ObjectSpreadInfo implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "近30d直播表现")
        @JsonAlias("object_n30d")
        private ObjectN30d objectN30d;
    }

    @Data
    public static class ObjectLatestLiveRooms implements Serializable {

        @JsonAlias("tx_title")
        private String title;

        @JsonAlias("long_watchUCount")
        private Long watchUCount;

        @JsonAlias("long_acu")
        private Long acu;

        @JsonAlias("long_pcu")
        private Long pcu;

        @JsonAlias("date_endTime")
        private String endTime;

        @JsonAlias("long_readCount")
        private Long readCount;

        @JsonAlias("double_commentRate")
        private Double commentRate;

        @JsonAlias("date_publishedAt")
        private String publishedAt;

        @JsonAlias("double_likeRate")
        private Double likeRate;

        @JsonAlias("long_duration")
        private Long duration;

//
//        @JsonAlias("long_watchDurationAvg")
//        private String watchDurationAvg;
//
//
//        @JsonAlias("kw_coverUrl")
//        private String coverUrl;
//
//        @JsonAlias("kw_gmv")
//        private String gmv;
//
//
//
////        @JsonAlias("bool_isPaid")
////        private String isPaid;
//
//
//        @JsonAlias("double_fansWatchRate")
//        private String fansWatchRate;
//
//
//
//
//        @JsonAlias("kw_gameType")
//        private String gameType;
//
//        @JsonAlias("kw_id")
//        private String id;
//
//        @JsonAlias("kw_gameName")
//        private String gameName;
//
//        @JsonAlias("double_acuRate")
//        private String acuRate;
//
//        @JsonAlias("long_followUCount")
//        private String followUCount;
//
//

    }

    @Data
    public static class ObjectN30d implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "场均CTR")
        @JsonAlias("double_ctr")
        private Double ctr;

        @Schema(title = "小手柄场均CTR")
        @JsonAlias("double_handleCtr")
        private Double handleCtr;

        @Schema(title = "小手柄场均CVR")
        @JsonAlias("double_handleCrv")
        private Double handleCrv;

        @Schema(title = "小雪花场均CTR")
        @JsonAlias("double_snowflakeCtr")
        private Double snowflakeCtr;

        @Schema(title = "小风车场均CTR")
        @JsonAlias("double_windmillCtr")
        private Double windmillCtr;

        @Schema(title = "近期直播场次")
        @JsonAlias("long_liveCnt")
        private Long liveCnt;

        @Schema(title = "平均观看人次")
        @JsonAlias("long_watchUCnt")
        private Long watchUCnt;

        @Schema(title = "平均观看人数")
        @JsonAlias("long_watchCnt")
        private Long watchCnt;

        @Schema(title = "ACU平均同时在线用户数")
        @JsonAlias("long_acu")
        private Long acu;

        @Schema(title = "PCU最高同时在线用户数")
        @JsonAlias("long_pcu")
        private Long pcu;

        @Schema(title = "场均评论率")
        @JsonAlias("double_commentRate")
        private Double commentRate;

        @Schema(title = "场均点赞率")
        @JsonAlias("double_likeRate")
        private Double likeRate;

        @Schema(title = "场均互动率")
        @JsonAlias("double_interactRate")
        private Double interactRate;

        @Schema(title = "平均看播时长")
        @JsonAlias("long_avgWatchDuration")
        private Long avgWatchDuration;

        @Schema(title = "ACU排名")
        @JsonAlias("double_acuPercent")
        private Double acuPercent;

        @Schema(title = "粉丝观看占比")
        @JsonAlias("double_fansWatchRate")
        private Double fansWatchRate;

        @Schema(title = "场均涨粉数")
        @JsonAlias("long_followUcnt")
        private Long followUcnt;

        @Schema(title = "完成订单数")
        @JsonAlias("long_finishOrderCnt")
        private Long finishOrderCnt;

        @Schema(title = "进行订单数")
        @JsonAlias("long_processOrderCnt")
        private Long processOrderCntu;

        @Schema(title = "带货商品数")
        @JsonAlias("long_product_num")
        private Long productNum;

        @Schema(title = "场均GMV")
        @JsonAlias("kw_gmvRange")
        private String gmvRange;

        @Schema(title = "带货商品客单价")
        @JsonAlias("kw_priceRange")
        private String priceRange;

        @Schema(title = "GPM")
        @JsonAlias("kw_gpmRange")
        private String gpmRange;

        @Schema(title = "带货商品信息")
        @JsonAlias("object_priceInfo")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<ObjectPriceInfo> objectPriceInfo;
    }

    @Data
    public static class ObjectPriceInfo implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "带货商品价格范围")
        @JsonAlias("kw_priceRange")
        private String priceRange;

        @Schema(title = "带货商品数量")
        @JsonAlias("long_product_num")
        private Long productNum;
    }
}
