package com.tarsocial.bigital.kol.common.domain.entity.tencent;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tencent_post_log")
public class TencentPostLog implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String topicKey;

    private String postId;

    private String platform;

    private String postPublished;

    private String postUpdated;

    private Date createTime;

    private Date updateTime;

}
