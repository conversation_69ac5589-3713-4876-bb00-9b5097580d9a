package com.tarsocial.bigital.kol.common.domain.response.ieg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StarUserWbResponse {

    private String user_id;
    private Boolean is_star;
    private String nickname;
    private Integer account_type;
    private String influence;
    private Integer post_expected_active;
    private String forward_expected_active;
    private String wrw_index;
    private String audience_age;
    private String audience_sex;
    private String audience_area;
    private Integer task_num;
    private Integer comment_num;
    private Double user_cpm;
    private Integer like_num;
    private Integer read_num;
    private Double user_cpe;
    private ArticleTendDTO article_tend;
    private Object fans_tend;
    private FansPortraitDTO fans_portrait;
    private Integer user_forward_price;
    private Integer forward_price;
    private String update_time;
    private Integer user_originaltprice_org;
    private Integer user_originaloprice_org;
    private String sex;

    @NoArgsConstructor
    @Data
    public static class ArticleTendDTO {
        private PostDTO comment;
        private PostDTO like;
        private PostDTO repost;

        @NoArgsConstructor
        @Data
        public static class PostDTO {
            private List<CommonStringDTO> forward;
            private List<CommonStringDTO> post;
        }
    }

    @NoArgsConstructor
    @Data
    public static class FansPortraitDTO {
        private ActiveFansGenderDTO active_fans_gender;
        private List<CommonDoubleDTO> active_fans_age;
        private List<CommonDoubleDTO> active_fans_area;
        private List<CommonDoubleDTO> interest;

        @NoArgsConstructor
        @Data
        public static class ActiveFansGenderDTO {
            private Double female;
            private Double male;
        }
    }

    @NoArgsConstructor
    @Data
    public static class CommonStringDTO {
        private String name;
        private String value;
    }

    @NoArgsConstructor
    @Data
    public static class CommonDoubleDTO {
        private String name;
        private Double value;
    }
}
