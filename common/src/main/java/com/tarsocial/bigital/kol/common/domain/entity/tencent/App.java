package com.tarsocial.bigital.kol.common.domain.entity.tencent;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_app")
public class App implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 应用名
     */
    private String name;

    /**
     * 应用标识
     */
    private String appKey;

    /**
     * 密钥
     */
    private String secret;

    private Date createTime;
}
