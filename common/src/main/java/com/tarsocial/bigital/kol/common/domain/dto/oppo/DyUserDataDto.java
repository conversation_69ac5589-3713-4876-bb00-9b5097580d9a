package com.tarsocial.bigital.kol.common.domain.dto.oppo;

import com.tarsocial.bigital.kol.common.domain.entity.oppo.DyUserData;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
* Created by Mybatis Generator 2024/11/21
*/
@Data
public class DyUserDataDto {

    public static DyUserDataDto form(DyUserData data) {
        DyUserDataDto dto = new DyUserDataDto();
        BeanUtils.copyProperties(data, dto);
        return dto;
    }

    /**
	* 用户昵称
	*/
    private String userNickname;

    /**
	* user_id
	*/
    private String userId;

    /**
	* 粉丝数
	*/
    private Long followersCount;

    /**
	* 用户头像
	*/
    private String userAvatar;

    /**
	* CPM
	*/
    private Double cpm;

    /**
	* 预期播放量
	*/
    private Long expectedPlayCount;

    /**
	* 短视频价格
	*/
    private Long shortVideoPrice;

    /**
	* 长视频价格
	*/
    private Long longVideoPrice;

    /**
	* 特长视频价格
	*/
    private Long specialVideoPrice;

    /**
	* 非商单播放中位数
	*/
    private Long nonCommercialPlayMedian;

    /**
	* 非商单完播率
	*/
    private Double nonCommercialCompletionRate;

    /**
	* 非商单互动率
	*/
    private Double nonCommercialEngagementRate;

    /**
	* 非商单互动量
	*/
    private Long nonCommercialEngagementCount;

    /**
	* 商单播放中位数
	*/
    private Long commercialPlayMedian;

    /**
	* 商单完播率
	*/
    private Double commercialCompletionRate;

    /**
	* 商单互动率
	*/
    private Double commercialEngagementRate;

    /**
	* 商单互动量
	*/
    private Long commercialEngagementCount;

    /**
	* 粉丝设备分布
	*/
    private String followersDeviceDistribution;

    /**
	* 粉丝地域分布
	*/
    private String followersRegionDistribution;

    /**
	* 粉丝城市分布
	*/
    private String followersCityDistribution;

    /**
	* 粉丝性别分布
	*/
    private String followersGenderDistribution;

    /**
	* 粉丝年龄分布
	*/
    private String followersAgeDistribution;

    /**
	* 粉丝城市等级
	*/
    private String followersCityLevel;

    /**
	* MCN
	*/
    private String mcn;

    /**
	* 粉丝八大人群
	*/
    private String followersGroups;

    /**
	* 行业标签
	*/
    private String industryTags;

    /**
	* 行业标签1
	*/
    private String industrySubTags;

    /**
	* 内容标签
	*/
    private String contentTags;

    /**
	* 内容标签1
	*/
    private String contentSubTags;

    /**
	* 转化指数
	*/
    private Double conversionIndex;

    /**
	* 星图传播指数
	*/
    private Double starMapCommunicationIndex;

    /**
	* 星图种草指数
	*/
    private Double starMapGrassrootsIndex;

    /**
	* 星图性价比指数
	*/
    private Double starMapCostEffectivenessIndex;

    /**
	* 星图合作指数
	*/
    private Double starMapCooperationIndex;

    /**
	* 月连接用户数
	*/
    private Long monthlyConnectedUsers;

    /**
	* 月深度用户数
	*/
    private Long monthlyDepthUsers;

    /**
	* 30_商单播放中位数
	*/
    private Long commercialPlayMedian30d;

    /**
	* 30_商单完播率
	*/
    private Double commercialCompletionRate30d;

    /**
	* 30_商单互动率
	*/
    private Double commercialEngagementRate30d;

    /**
	* 30_商单互动量
	*/
    private Long commercialEngagementCount30d;

    /**
	* 30_非商单播放中位数
	*/
    private Long nonCommercialPlayMedian30d;

    /**
	* 30_非商单完播率
	*/
    private Double nonCommercialCompletionRate30d;

    /**
	* 30_非商单互动率
	*/
    private Double nonCommercialEngagementRate30d;

    /**
	* 30_非商单互动量
	*/
    private Long nonCommercialEngagementCount30d;

    /**
	* 星图链接
	*/
    private String starUrl;

    /**
	* 主页链接
	*/
    private String userUrl;

    /**
	* 15天增粉率
	*/
    private Double followerGrowthRate15d;

    /**
	* 30天增粉率
	*/
    private Double followerGrowthRate30d;

    /**
	* 近30天粉丝增长数
	*/
    private Long followerGrowth30d;

    /**
	* 观众性别分布
	*/
    private String audienceGenderDistribution;

    /**
	* 观众年龄分布
	*/
    private String audienceAgeDistribution;

    /**
	* 观众地域分布
	*/
    private String audienceRegionDistribution;

    /**
	* 观众城市分布
	*/
    private String audienceCityDistribution;

    /**
	* 观众城市等级分布
	*/
    private String audienceCityLevelDistribution;

    /**
	* 观众设备分布
	*/
    private String audienceDeviceDistribution;

    /**
	* 观众八大人群分布
	*/
    private String audienceMajorGroupsDistribution;

    /**
	* 达人描述
	*/
    private String description;

    /**
	* 非商单平均转发-90天
	*/
    private Long nonCommercialAvgShares90d;

    /**
	* 非商单平均评论-90天
	*/
    private Long nonCommercialAvgComments90d;

    /**
	* 非商单平均点赞-90天
	*/
    private Long nonCommercialAvgLikes90d;

    /**
	* 商单平均转发-90天
	*/
    private Long commercialAvgShares90d;

    /**
	* 商单平均评论-90天
	*/
    private Long commercialAvgComments90d;

    /**
	* 商单平均点赞-90天
	*/
    private Long commercialAvgLikes90d;

    /**
	* 非商单平均转发-30天
	*/
    private Long nonCommercialAvgShares30d;

    /**
	* 非商单平均评论-30天
	*/
    private Long nonCommercialAvgComments30d;

    /**
	* 非商单平均点赞-30天
	*/
    private Long nonCommercialAvgLikes30d;

    /**
	* 商单平均转发-30天
	*/
    private Long commercialAvgShares30d;

    /**
	* 商单平均评论-30天
	*/
    private Long commercialAvgComments30d;

    /**
	* 商单平均点赞-30天
	*/
    private Long commercialAvgLikes30d;

    /**
	* 涨粉黑马榜
	*/
    private Long followerGrowthRank;

    /**
     * 商业合作品牌
     */
    private String collaborationBrands;

    /**
     * 商业合作品牌 - 美妆
     */
    private String collaborationBrandsBeauty;

    /**
	* 图文价格
	*/
    private Long imageTextPrice;

    /**
	* 达人身份标签
	*/
    private String identityTags;

    /**
	* 达人社会身份标签
	*/
    private String socialIdentityTags;

    /**
	* 看后搜指数
	*/
    private Double searchIndex;

    /**
	* 预期CPA3等级
	*/
    private Long expectedCpaLevel3;


    /**
     * 曝光率
     */
    private Double explosiveArticleRate;


}