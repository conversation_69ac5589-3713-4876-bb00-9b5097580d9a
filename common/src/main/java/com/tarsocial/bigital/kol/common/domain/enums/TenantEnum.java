package com.tarsocial.bigital.kol.common.domain.enums;

import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * 租户配置
 *
 * <AUTHOR> GoodLuck
 * @since 2023/11/29$ 16:03
 */

@Getter
public enum TenantEnum {
    /**
     * tenant
     */
    BEAUTY_DEV("KOL 测试 美妆", "kol-dev.beauty1", "public", "public"),

    PROD_VIP_DEV("KOL 生产 VIP", "kol-prd.vip2", "public", "public");

    private final String tenantName;
    private final String tenantKey;
    private final String appTenantName;
    private final String schema;

    TenantEnum(String tenantName, String tenantKey, String appTenantName, String schema) {
        this.tenantName = tenantName;
        this.tenantKey = tenantKey;
        this.appTenantName = appTenantName;
        this.schema = schema;
    }

    public static String getTenantName(String tenantKey) {
        for (TenantEnum tenant : TenantEnum.values()) {
            if (tenant.getTenantKey().equals(tenantKey)) {
                return tenant.getAppTenantName();
            }
        }
        return null;
    }


    public static String getSchema(String tenantAppName) {
        for (TenantEnum tenant : TenantEnum.values()) {
            if (tenant.getAppTenantName().equals(tenantAppName)) {
                return tenant.getSchema();
            }
        }
        return null;
    }

    public static Set<String> getSchemas() {
        Set<String> schemas = new HashSet<>();
        for (TenantEnum value : TenantEnum.values()) {
            schemas.add(value.getSchema());
        }

        return schemas;
    }

}

