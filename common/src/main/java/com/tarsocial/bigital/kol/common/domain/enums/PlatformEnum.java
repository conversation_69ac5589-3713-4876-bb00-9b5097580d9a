package com.tarsocial.bigital.kol.common.domain.enums;


import lombok.Getter;


/**
 * 平台枚举
 *
 * <AUTHOR>
 * @since 2024/3/14
 */
@Getter
public enum PlatformEnum {

    /**
     * douyin
     */
    DOUYIN("douyin", "抖音", 1),
    XIAOHONGSHU("xiaohongshu", "小红书", 4),
    WEIBO("weibo", "微博", 5),
    BILIBILI("bilibili", "B站", 3),
    ZHIHU("zhihu", "知乎", null),
    XINWEN("web", "新闻", null),
    BBS("bbs", "论坛", null),
    KUAISHOU("kuaishou", "快手", 2),
    WEIXIN("weixin", "微信", null),
    shipihao("shipihao", "视频号", null),
    toutiao("toutiao", "头条", null),
    ;

    private final String code;
    private final String name;
    private final Integer no;


    PlatformEnum(String code, String name, Integer no) {
        this.code = code;
        this.name = name;
        this.no = no;
    }

    public static String getName(String code) {
        final PlatformEnum[] values = PlatformEnum.values();
        for (PlatformEnum value : values) {
            if (value.code.equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        final PlatformEnum[] values = PlatformEnum.values();
        for (PlatformEnum value : values) {
            if (value.name.equals(name)) {
                return value.getCode();
            }
        }
        return null;
    }

    public static String getCodeByNo(Integer no) {
        if (no == null) {
            return null;
        }
        final PlatformEnum[] values = PlatformEnum.values();
        for (PlatformEnum value : values) {
            if (value.no.equals(no)) {
                return value.getCode();
            }
        }
        return null;
    }

}
