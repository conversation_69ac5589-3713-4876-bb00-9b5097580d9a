package com.tarsocial.bigital.kol.common.domain.enums;


import lombok.Getter;


@Getter
public enum UserTaskStatusEnum {

    RUNNING("RUNNING", "进行中"),
    FINISH("FINISH", "已完成"),
    FAIL("FAIL", "失败"),
    ;

    private final String code;
    private final String name;


    UserTaskStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        final UserTaskStatusEnum[] values = UserTaskStatusEnum.values();
        for (UserTaskStatusEnum value : values) {
            if (value.code.equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public static String getCode(String name) {
        final UserTaskStatusEnum[] values = UserTaskStatusEnum.values();
        for (UserTaskStatusEnum value : values) {
            if (value.name.equals(name)) {
                return value.getCode();
            }
        }
        return null;
    }

}
