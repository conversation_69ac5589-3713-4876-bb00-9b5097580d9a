package com.tarsocial.bigital.kol.common.domain.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 * @title HairBaseResponseV1
 * @date 2025/1/17 16:15
 * @description 「HairBaseResponseV1」
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HairBaseResponseV1<T> {
    private static final int CODE_ERROR = -1;

    private int errCode;
    private String errMsg;
    private String traceId;
    private T taskId;

    public HairBaseResponseV1(T taskId) {
        this.errCode = 0;
        this.errMsg = "success";
        this.traceId = MDC.get("traceId");
        this.taskId = taskId;
    }

    public HairBaseResponseV1(Integer code, String message) {
        this.errCode = code;
        this.errMsg = message;
        this.traceId = MDC.get("traceId");
        this.taskId = null;
    }


    public boolean success() {
        return this.errCode == 200;
    }

    public static <T> HairBaseResponse<T> error(String message) {
        return new HairBaseResponse<>(CODE_ERROR, message);
    }
}
