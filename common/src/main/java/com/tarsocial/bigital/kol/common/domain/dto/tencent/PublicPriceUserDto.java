package com.tarsocial.bigital.kol.common.domain.dto.tencent;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.tarsocial.bigital.kol.common.domain.entity.QuarkUserInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class PublicPriceUserDto implements Serializable {


    @Schema(title = "用户ID")
    @JsonAlias("kw_userId")
    private String userId;

    @Schema(title = "平台")
    @JsonAlias("kw_platform")
    private String platform;

    @Schema(title = "kolId")
    @JsonAlias("kw_kolId")
    private String kolId;

    @Schema(title = "用户昵称")
    @JsonAlias("tkw_nickname")
    private String nickname;

    @Schema(title = "粉丝数")
    @JsonAlias("long_followersCount")
    private Long followersCount;

    @Schema(title = "性别")
    @JsonAlias("kw_gender")
    private String gender;

    //是否入驻商家平台  根据kolId 判断
    private Boolean registeredOnPlatform;

    private String updateTime;

    private String status;

    private QuarkUserInfo.ObjectPriceInfos objectPriceInfos;


}
