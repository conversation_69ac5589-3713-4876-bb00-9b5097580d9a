package com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime;

import lombok.Data;

@Data
public class QuarkPostDto {
    private String userId;
    private String publishedAt;
    private String id;
    private String platform;
    private Boolean isPaid;
    private Long readCount;
    private String coverUrl;
    private String content;
    private String url;
    private Long duration;
    private Long repostsCount;
    private Long commentsCount;
    private Long likeCount;
    private Long collectCount;
    private String title;
    private Long coinCount;
    private Long danmuCount;
}
