package com.tarsocial.bigital.kol.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator 2024/11/21
*/
@Data
@TableName(value = "interface_data_count")
public class InterfaceDataCount {
    /**
     * 主键，自增
     */
     @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户端
     */
    @TableField(value = "client_id")
    private String clientId;

    /**
     * 接口名称
     */
    @TableField(value = "interface_name")
    private String interfaceName;

    /**
     * 目标表
     */
    @TableField(value = "target")
    private String target;

    /**
     * 批次号
     */
    @TableField(value = "batch_no")
    private String batchNo;

    /**
     * 唯一业务标记
     */
    @TableField(value = "trace_id")
    private String traceId;

    /**
     * 查询语句
     */
    @TableField(value = "query")
    private String query;

    /**
     * 总数据量
     */
    @TableField(value = "total_count")
    private Long totalCount;

    /**
     * 已滚动数据量
     */
    @TableField(value = "scroll_count")
    private Long scrollCount;

    /**
     * 查询数据量
     */
    @TableField(value = "query_count")
    private Long queryCount;

    /**
     * 返回数据量
     */
    @TableField(value = "data_count")
    private Long dataCount;

    /**
     * 日期 (格式: YYYYMMDD)
     */
    @TableField(value = "query_date")
    private String queryDate;

    /**
     * 请求时间
     */
    @TableField(value = "query_time")
    private Date queryTime;

    /**
     * 耗时
     */
    @TableField(value = "usage_time")
    private Long usageTime;

    public static final String COL_ID = "id";

    public static final String COL_CLIENT_ID = "client_id";

    public static final String COL_INTERFACE_NAME = "interface_name";

    public static final String COL_TARGET = "target";

    public static final String COL_BATCH_NO = "batch_no";

    public static final String COL_TRACE_ID = "trace_id";

    public static final String COL_QUERY = "query";

    public static final String COL_TOTAL_COUNT = "total_count";

    public static final String COL_SCROLL_COUNT = "scroll_count";

    public static final String COL_QUERY_COUNT = "query_count";

    public static final String COL_DATA_COUNT = "data_count";

    public static final String COL_QUERY_DATE = "query_date";

    public static final String COL_QUERY_TIME = "query_time";

    public static final String COL_USAGE_TIME = "usage_time";
}