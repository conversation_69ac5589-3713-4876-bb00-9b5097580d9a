package com.tarsocial.bigital.kol.common.domain.request.tencent.ieg;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class PublicPriceTaskCreateRequest {

    @NotEmpty(message = "id is not null")
    private String user_ids;

    //1：抖音 2：快手 3：b站 4：小红书 5：微博
    @NotNull
    private Integer platform;

    private String token;
}
