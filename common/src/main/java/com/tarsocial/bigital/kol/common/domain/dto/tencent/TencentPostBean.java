package com.tarsocial.bigital.kol.common.domain.dto.tencent;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TencentPostBean
 * @date 2024年05月21日
 */
@NoArgsConstructor
@Data
public class TencentPostBean {

    private String dataRange;
    private String publishTime;
    private String platform;
    private String title;
    private Long playCount;
    private Long likeCount;
    private Long commentCount;
    private Long forwardCount;
    private Long favorateCount;
    private String url;
    private String contentId;
    private Long videoLength;
    private Long wordCount;
    private Boolean isOriginal;
    private String coverUrl;
    private String coverOCR;
    private List<String> topics;
    private Long topicCount;
    private String contentType;
    private Boolean isCommercialOrder;
    private Long orderPrice;
    private List<String> videoTags;
    private String musicId;
    private Boolean hasProduct;
    private List<ProductInfoDTO> productInfo;
    private String createTime;
    private String updateTime;
    private String userName;
    private String userUID;
    private String userAccount;
    private Long followerCount;
    private String userBirthday;
    private String userDescription;
    private String userHomepageUrl;
    private String userAvatarUrl;
    private String gender;
    private String verifiedName;
    private Boolean isDelete;
    private String grabTime;

    @NoArgsConstructor
    @Data
    public static class ProductInfoDTO {
        private String productUrl;
        private List<String> productImage;
        private String productId;
    }
}
