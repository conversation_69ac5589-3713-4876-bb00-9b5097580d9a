package com.tarsocial.bigital.kol.common.domain.enums;

import lombok.Getter;
import org.apache.commons.compress.utils.Lists;

import java.util.List;


/**
 * <AUTHOR>
 */

@Getter
public enum QuarkPlatform {

    /**
     * PlatformType
     */
    WEIBO(43L, "weibo", "微博"),
    DOUYIN(44L, "douyin", "抖音"),
    XIAOHONGSHU(45L, "xiaohongshu", "小红书"),
    BBS(46L, "bbs", "论坛"),
    WEB(47L, "web", "网页"),
    TOUTIAO(48L, "toutiao", "头条"),
    WEIXIN(49L, "weixin", "微信"),
    SHIPINHAO(50L, "shipinhao", "视频号"),
    BILIBILI(51L, "bilibili", "bilibili"),
    ZHIHU(52L, "zhihu", "知乎"),
    KUAISHOU(53L, "kua<PERSON><PERSON>", "快手"),
    ;

    Long code;
    String value;
    String display;

    QuarkPlatform(Long code, String value, String display) {
        this.code = code;
        this.value = value;
        this.display = display;
    }

    public Long code() {
        return this.code;
    }

    public String value() {
        return this.value;
    }

    public String display() {
        return this.display;
    }

    public static List<Long> getAllPlatformCode() {
        List<Long> list = Lists.newArrayList();
        for (QuarkPlatform platformType : QuarkPlatform.values()) {
            list.add(platformType.code());
        }
        return list;
    }

    public static List<String> getAllPlatformValue() {
        List<String> list = Lists.newArrayList();
        for (QuarkPlatform platformType : QuarkPlatform.values()) {
            list.add(platformType.getValue());
        }
        return list;
    }

    public static Long getPlatformCodeByValue(String value) {
        for (QuarkPlatform platformType : QuarkPlatform.values()) {
            if (platformType.value().equalsIgnoreCase(value)) {
                return platformType.code();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        Long douyin = QuarkPlatform.getPlatformCodeByValue("douyin");
        System.out.println(douyin);
    }

}
