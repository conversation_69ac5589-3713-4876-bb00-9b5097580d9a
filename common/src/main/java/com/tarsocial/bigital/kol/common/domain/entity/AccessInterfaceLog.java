package com.tarsocial.bigital.kol.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 接口访问日志
 */
@Data
@ToString
@EqualsAndHashCode
@TableName(value = "access_interface_log")
public class AccessInterfaceLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private String path;

    private String url;

    private String params;

    private String sourceIp;

    private Long times;

    private Date accessTime;

    private Date createTime;

    private String statusType;

    private String body;

    private String user;


    public static AccessInterfaceLog log(String clientApp, String scope, String path, String url, String ip) {
        AccessInterfaceLog access = new AccessInterfaceLog();
        access.setSourceIp(ip);
        access.setUrl(url);
        access.setPath(path);
        return access;
    }

    public AccessInterfaceLog accessResultType(String statusType) {
        this.statusType = statusType;
        return this;
    }

    public AccessInterfaceLog accessTime(Long accessTime) {
        if (null == accessTime) {
            return this;
        }
        this.accessTime = new Date(accessTime);
        return this;
    }

    public AccessInterfaceLog times() {
        if (null == this.accessTime) {
            return this;
        }
        this.times = System.currentTimeMillis() - this.accessTime.getTime();
        return this;
    }

}
