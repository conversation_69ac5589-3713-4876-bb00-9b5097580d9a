package com.tarsocial.bigital.kol.common.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CompareFilterRequest implements Serializable {

    @ApiModelProperty(value = "日期类型 day/week/month", required = true)
    @NotEmpty(message = "dateType is not null")
    private String dateType;

    @ApiModelProperty(value = "开始时间", required = true)
    @NotEmpty(message = "startDate is not null")
    private String startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    @NotEmpty(message = "endDate is not null")
    private String endDate;

    @ApiModelProperty(value = "平台")
    private List<String> platformList;

    @ApiModelProperty(value = "声量类型")
    private List<String> volumeTypes;

    @ApiModelProperty(value = "品类")
    private String category;


    @ApiModelProperty(value = "品牌")
    private List<String> brands;

    @ApiModelProperty(value = "内容类型 PGC UGC")
    private List<String> contentTypes;


    @ApiModelProperty(value = "情感 正 负 中")
    private List<String> emotions;


    @ApiModelProperty(value = "评论 是否")
    private Boolean commentType;

    @ApiModelProperty(value = "排序字段：volume, engagement, volumeSov, engagementSov, nsr")
    private String orderField = "volume";

    @ApiModelProperty(value = "Asc  / Desc")
    private String order = "Desc";

    @ApiModelProperty(value = "统计值：声量、互动量、花费、CPE、评论数")
    private String countBy;

    @ApiModelProperty(value = "size")
    private Integer size = 100;

    @ApiModelProperty(value = "同比: 1 环比: 2")
    private Integer rateStatus = 1;

}
