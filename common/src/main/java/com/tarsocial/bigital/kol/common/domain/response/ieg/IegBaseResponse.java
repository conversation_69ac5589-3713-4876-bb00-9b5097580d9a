package com.tarsocial.bigital.kol.common.domain.response.ieg;

import lombok.Data;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 */
@Data
public class IegBaseResponse<T> {

    private Integer errcode;

    private String traceId;

    private String errmsg;

    private T data;

    public IegBaseResponse(T data) {
        this.errcode = 0;
        this.errmsg = "success";
        this.traceId = MDC.get("traceId");
        this.data = data;
    }

    public IegBaseResponse(Integer code, String message) {
        this.errcode = code;
        this.errmsg = message;
        this.traceId = MDC.get("traceId");
        this.data = null;
    }
}
