package com.tarsocial.bigital.kol.common.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * ABI 传输字段
 *
 * <AUTHOR>
 */
@Data
public class PostDetailsDto {

    //    @JsonProperty("发帖时间")
    @JsonProperty("发表时间")
    private Object publishAt;

    //    @JsonProperty("平台")
    @JsonProperty("数据类型")
    private Object platform;

    //    @JsonProperty("域名")
    @JsonProperty("站点名称")
    private Object domain;

    //    @JsonProperty("互动数")
    @JsonProperty("总互动量")
    private Object interaction;

    @JsonProperty("转发数")
    private Object repostsCount;

    @JsonProperty("在看数")
    private Object zaikan;

    @JsonProperty("粉丝数")
    private Object followersCount;

    @JsonProperty("弹幕数")
    private Object danmuCount;

    @JsonProperty("投币数")
    private Object coinCount;

    @JsonProperty("分享数")
    private Object shareCount;

    @JsonProperty("评论数")
    private Object commentsCount;

    @JsonProperty("点赞数")
    private Object likeCount;

    @JsonProperty("收藏数")
    private Object collectCount;

    @JsonProperty("用户UID")
    private Object userId;

    //    @JsonProperty("用户昵称")
    @JsonProperty("作者")
    private Object nickname;

    //    @JsonProperty("用户所在省")
    @JsonProperty("省份")
    private Object province;

    //    @JsonProperty("用户所在城市")
    @JsonProperty("城市")
    private Object city;

    @JsonProperty("用户所在国家")
    private Object country;

    @JsonProperty("IP省")
    private Object ipProvince;

    //    @JsonProperty("内容ID")
    @JsonProperty("id")
    private Object id;

    @JsonProperty("url")
    private Object url;

    //    @JsonProperty("正文")
    @JsonProperty("内容")
    private Object content;

    //    @JsonProperty("视频时长(秒)")
    @JsonProperty("视频时长")
    private Object duration;

    @JsonProperty("认证类型")
    private Object verifiedType;

    @JsonProperty("周均发帖量")
    private Object pbw;

    @JsonProperty("微信mid")
    private Object mid;

    @JsonProperty("是否原创")
    private Object isOriginal;

    @JsonProperty("标题")
    private Object title;

    @JsonProperty("啤酒品牌")
    private Object brand;

    @JsonProperty("微信BIZ")
    private Object biz;

    @JsonProperty("封面URL")
    private Object coverUrl;

    //    @JsonProperty("封面OCR")
    @JsonProperty("视频封面内容识别")
    private Object ocrCover;

    @JsonProperty("视频URL")
    private Object videoUrl;

    //    @JsonProperty("语音识别内容")
    @JsonProperty("音频内容识别")
    private Object asr;

    //    @JsonProperty("内容OCR")
    @JsonProperty("视频文本识别")
    private Object ocr;

    @JsonProperty("图片URL")
    private Object picUrl;

    @JsonProperty("图片数量")
    private Object picCount;


    @JsonProperty("提及用户")
    private Object mentions;

    //    @JsonProperty("提及话题")
    @JsonProperty("话题")
    private Object hashtags;

    @JsonProperty("提及话题数量")
    private Object hashtagsCount;

    @JsonProperty("帖子类型")
    private Object postType;

    @JsonProperty("原帖创建时间")
    private Object originPublishedAt;

    @JsonProperty("原帖ID")
    private Object originId;

    @JsonProperty("原帖内容")
    private Object originContent;

    @JsonProperty("原帖阅读数")
    private Object originReadCount;

    @JsonProperty("原帖转发数")
    private Object originRepostsCount;

    @JsonProperty("原帖评论数")
    private Object originCommentsCount;

    @JsonProperty("原帖点赞数")
    private Object originLikeCount;

    @JsonProperty("原帖互动数")
    private Object originInteraction;

    @JsonProperty("原文作者")
    private Object originAuthorName;

    @JsonProperty("原帖用户UID")
    private Object originAuthorId;

    @JsonProperty("原帖用户昵称")
    private Object originAuthorNickname;

    @JsonProperty("原帖关注数")
    private Object originFriendsCount;

    @JsonProperty("原帖微博数")
    private Object originStatusesCount;

    @JsonProperty("原帖收藏数")
    private Object originFavouritesCount;

    @JsonProperty("原帖用户是否认证")
    private Object originVerified;

    @JsonProperty("原帖用户的互粉数")
    private Object originBiFollowersCount;

    //    @JsonProperty("用户主页地址")
    @JsonProperty("用户主页url")
    private Object userUrl;

    //    @JsonProperty("是否认证")
    @JsonProperty("是否认证用户")
    private Object verified;

    @JsonProperty("性别")
    private Object gender;

    //    @JsonProperty("用户生日")
    @JsonProperty("出生年份")
    private Object birthday;

    //    @JsonProperty("认证原因")
    @JsonProperty("用户认证原因")
    private Object verifiedReason;

    //    @JsonProperty("用户个人描述")
    @JsonProperty("用户简介")
    private Object description;

    @JsonProperty("唯一ID")
    private Object onlyId;

    @JsonProperty("创建时间")
    private Object createTime;

    @JsonProperty("更新时间")
    private Object updateTime;


}
