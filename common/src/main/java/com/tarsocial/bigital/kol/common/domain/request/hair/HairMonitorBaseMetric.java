package com.tarsocial.bigital.kol.common.domain.request.hair;

import java.util.Date;
import java.util.List;
import lombok.Data;


/**
 * <AUTHOR>
 * @title HairMonitorResponseV2
 * @date 2025年01月14日13:55:59
 * @description 「HairMonitorResponseV2」
 */
@Data
public class HairMonitorBaseMetric {


    /**
     * 是否投流
     */
    private Boolean isHeated;



    /**
     * 2日回搜次数
     */
    private Long backSearchCount2d;
    /**
     * 2日回搜人数
     */
    private Long backSearchUserCount2d;

    /**
     * 2日回搜率
     */
    private Double backSearchRate2d;


    /**
     * 2日回搜次数
     */
    private Long backSearchCount7d;

    /**
     * 2日回搜人数
     */
    private Long backSearchUserCount7d;

    /**
     * 2日回搜率
     */
    private Double backSearchRate7d;

    /**
     * 2日回搜次数
     */
    private Long backSearchCount15d;

    /**
     * 2日回搜人数
     */
    private Long backSearchUserCount15d;

    /**
     * 2日回搜率
     */
    private Double backSearchRate15d;

}
