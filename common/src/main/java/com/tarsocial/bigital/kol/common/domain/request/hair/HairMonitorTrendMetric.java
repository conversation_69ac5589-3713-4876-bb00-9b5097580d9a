package com.tarsocial.bigital.kol.common.domain.request.hair;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.models.auth.In;
import java.util.Date;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title HairMonitorResponseV2
 * @date 2025年01月14日13:55:59
 * @description 「HairMonitorResponseV2」
 */
@Data
public class HairMonitorTrendMetric {

    private List<Long> seq;

    private Long id;

    private Double cpm;

    private Double cpe;

    private Double cps;

    private Double cpa3;
    /**
     * 新增A3
     */
    private Integer newlyA3;

    /**
     * 新增自然流量曝光
     */
    private Integer naturalExposureCount;


    /**
     * 自动流量/播放数
     */
    private Integer naturalPlayReadCount;

    private Integer naturalInteractionCount;
    /**
     * 推广曝光
     */
    private Integer promotionExposureCount;

    /**
     * 推广阅读
     */
    private Integer promotionReadCount;

    /**
     * 加热曝光
     */
    private Integer heatedExposureCount;
    /**
     * 加热阅读
     */
    private Integer heatedReadCount;
    /**
     * 加热互动
     */
    private Integer heatedInteractionCount;

    /**
     * 是否投流
     */
    private Boolean isHeated;

    private Long postSearchCount;


    private Long postSearchUserCount;


    private Double postSearchRate;

    private List<Comp> comp;

    /**
     * 帖子id
     */
    private String postId;
    /**
     * 平台
     */
    private String platform;
    /**
     * 采集时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 监控频次
     */
    private String frequencyType;

    /**
     * 作品发布时间
     */
    private String publishedTime;
    /**
     * 正文
     */
    private String content;
    /**
     * 播放/阅读数
     */
    private Integer playCount;

    /**
     * 互动数
     */
    private Integer interactionCount;
    /**
     * 转发/分享
     */
    private Integer forwardCount;
    /**
     * 评论
     */
    private Integer commentCount;

    /**
     * 点赞
     */
    private Integer likeCount;

    /**
     * 收藏
     */
    private Integer collectCount;
    /**
     * 弹幕
     */
    private Integer bulletCount;
    /**
     * 投币 -b战
     */
    private Integer coinCount;
    /**
     * 在看-公众号
     */
    private Integer viewCount;

    /**
     * 赞同
     */
    private Integer agreeCount;

    /**
     * 曝光
     */
    private Integer exposureCount;


    /**
     * 话题标签
     */
    private List<String> topicTags;

    /**
     * 组建数据
     */
    @Data
    public static class Comp {

        private Integer compType;
        private Integer compImpNum;
        private Integer compClickPvNum;
        private Integer compClickUvNum;
        private Integer clickPvRate;
    }
}
