package com.tarsocial.bigital.kol.common.domain.response.amp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AMPPostResponse implements Serializable {

    private Long long_interaction;

    private Long long_likeCount;

    private Long long_repostsCount;

    private Long long_commentsCount;

    private Long long_collectCount;

    private String kw_coverUrl;

    private String kw_url;

    private String date_publishedAt;

    private String kw_id;

    private List<String> array_picUrl;

    private String tx_title;

    private String kw_platform;

    private List<String> array_videoUrl;

    private String tx_ocr;

    private String tx_asr;

    private String tx_content;

    private String kw_avatar;

    private String kw_nickname;

    private String kw_platformUserId;

    private String kw_userId;

    private Long long_30_no_paid_interaction;

    private Long long_30_no_paid_play;

    private Long long_30_paid_play;

    private Long long_30_paid_interaction;

}
