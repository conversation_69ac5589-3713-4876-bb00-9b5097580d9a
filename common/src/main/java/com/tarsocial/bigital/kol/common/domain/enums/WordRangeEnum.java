package com.tarsocial.bigital.kol.common.domain.enums;

import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum WordRangeEnum {
    TITLE("title", "tx_title"),
    CONTENT("content", "tx_content"),
    OCRCOVER("ocrCover", "tx_ocrCover"),
    OCR("ocr", "tx_ocr"),
    ASR("asr", "tx_asr");


    private String value;
    private String esField;

    WordRangeEnum(String value, String esField) {

        this.value = value;
        this.esField = esField;
    }

    public static String getEsField(String value) {
        for (WordRangeEnum e : values()) {
            if (Objects.equals(e.getValue(), value)) {
                return e.getEsField();
            }
        }
        return null;
    }

    public static boolean correctBu(String value) {
        return Arrays.stream(WordRangeEnum.values()).map(bu -> bu.value).collect(Collectors.toList()).contains(value);
    }

    public String getValue() {
        return this.value;
    }

    public String getEsField() {
        return this.esField;
    }

    public static List<String> getAllEsField(List<String> values) {
        if (CollectionUtils.isEmpty(values))
            return Arrays.stream(WordRangeEnum.values()).map(field -> field.esField).collect(Collectors.toList());
        return values.stream().map(WordRangeEnum::getEsField).collect(Collectors.toList());
    }

    public static List<String> getAllEsField() {
        return getAllEsField(null);
    }
}
