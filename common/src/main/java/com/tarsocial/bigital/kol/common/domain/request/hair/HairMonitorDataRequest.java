package com.tarsocial.bigital.kol.common.domain.request.hair;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @title HairMonitorDataRequest
 * @date 2024/12/27 18:18
 * @description 「HairMonitorDataRequest」
 */
@Data
public class HairMonitorDataRequest {
    private Long  taskId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * todo 转换成枚举
     */
    private String frequencyType;



}
