package com.tarsocial.bigital.kol.common.domain.dto;

import com.tarsocial.bigital.kol.common.domain.request.NerFlexibleCalculateRequest;
import lombok.Data;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class NerFlexibleDto {

    private List<String> postIdList;

    //post_*v0    comment_*v0
    private String indexName = "post_*v0";

    //查询24年之前的数据
    private Boolean ago = false;

//    private Map<String, String> resultMap;

    private boolean hasPostId = true;

    private Map<String, Map<String, Set<String>>> result;

    private long currentTimeMillis = System.currentTimeMillis();

    private Set<String> writeHeaderList = new HashSet<>();

    private NerFlexibleCalculateRequest request;

    private Map<String, NerFlexiblePostDto> postMap;

    //      key:实体类型  k原始实体    v标准实体
    private Map<String, Map<String, String>> entityMap = new HashMap<>();

}
