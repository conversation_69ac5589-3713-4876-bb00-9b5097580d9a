package com.tarsocial.bigital.kol.common.domain.enums;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> GoodLuck
 * @date 2023/12/7$ 15:13
 */


import lombok.Getter;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public interface Constants {


    String[] PLATFORM = new String[]{"douyin", "xiaohongshu", "weibo", "bilibili"};

    /**
     * TGI最小计算值
     */
    Integer TGI_MIN_COUNT = 150;

    final static String POST_INDEX = "post";

    /**
     * 夸克词包 匹配字段
     */
    HashMap<String, Float> TAG_FIELDS = new HashMap<String, Float>() {{
        put("tx_title", 1f);
        put("tx_content", 1f);
        put("tx_ocrCover", 1f);
        put("tx_asr", 1f);
        put("tx_ocr", 1f);
        put("object_promotions.tx_title", 1f);
    }};


    enum AccountType {
        WILD_TALENT(1, "wildTalent", "野生达人"),
        ORGANIZATION_TALENT(2, "organizationTalent", "机构达人"),
        NON_PLATFORM_TALENT(3, "nonPlatformTalent", "非平台达人");

        private final int code;
        private final String description;

        private final String value;

        AccountType(int code, String description, String value) {
            this.code = code;
            this.description = description;
            this.value = value;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public String getValue() {
            return value;
        }

        // 根据code获取对应的AccountType枚举的description
        public static String fromCode(int code) {
            for (AccountType type : values()) {
                if (type.code == code) {
                    return type.description;
                }
            }
            return null;
        }

        public static String fromValue(String description) {
            for (AccountType type : values()) {
                if (type.description.equals(description)) {
                    return type.value;
                }
            }
            return null;
        }
    }


    @Getter
    enum IEGPlatformType {

        /**
         * PlatformType
         */
        DY(1, "douyin", "抖音", 44L),

        KS(2, "kuaishou", "快手", 53L),

        BILI(3, "bilibili", "B站", 51L),

        XHS(4, "xiaohongshu", "小红书", 45L),

        WB(5, "weibo", "微博", 43L),

        ;

        private final Integer code;
        private final String value;
        private final String display;
        private final Long platformId;

        IEGPlatformType(Integer code, String value, String display, Long platformId) {
            this.code = code;
            this.value = value;
            this.display = display;
            this.platformId = platformId;
        }

        /**
         * 通过value获取对应的PlatformType枚举的platformId
         *
         * @param code code
         * @return platformId
         */
        public static IEGPlatformType getPlatformIdByValue(Integer code) {
            for (IEGPlatformType platform : values()) {
                if (platform.getCode().equals(code)) {
                    return platform;
                }
            }
            return null;
        }

    }

    enum QuarkConditionField {

        PROVINCE(226L, "province"),
        GENDER(239L, "gender"),
        FOLLOWERSCOUNT(395L, "followersCount"),
        COMMENT(312L, "评论"),
        AGE(396L, "age");

        Long fieldId;  // 夸克3.0 生产环境
        String label;

        QuarkConditionField(Long fieldId, String label) {
            this.fieldId = fieldId;
            this.label = label;
        }

        public Long getFieldId() {
            return this.fieldId;
        }
    }


    enum ConditionOperator {
        equals(0, "等于"),
        not_equal(1, "不等于"),

        exist(2, "存在"),
        not_exist(3, "不存在"),

        greater_and_equal(4, "大于等于"),
        greater(5, "大于"),
        less_and_equal(6, "小于等于"),
        less(7, "小于"),
        contain(8, "包含"),
        exclude(9, "排除");

        @Getter
        final
        Integer code;
        final String label;

        ConditionOperator(Integer code, String label) {
            this.code = code;
            this.label = label;
        }

    }

    @Getter
    enum CollectionKolType {
        PERSONAL("个人"),
        ORGANIZATION("机构"),
        NOT_FOUND("暂无");

        final String label;

        CollectionKolType(String label) {
            this.label = label;
        }

    }


    @Getter
    enum CompareCountByType {
        VOLUME("volume"),
        INTERACTION("interaction"),
        COST("cost");

        final String label;

        CompareCountByType(String label) {
            this.label = label;
        }

    }

    enum emotionEnum {
        POSITIVE(1, "正向"),
        NEUTRAL(2, "中性"),
        NEGATIVE(3, "负向");

        private final Integer code;
        private final String description;

        emotionEnum(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        // 根据code获取对应的emotionEnum枚举的description
        public static String fromCode(Integer code) {
            for (emotionEnum emotion : values()) {
                if (emotion.code.equals(code)) {
                    return emotion.description;
                }
            }
            return null;
        }

        public static Integer fromCode(String description) {
            for (emotionEnum emotion : values()) {
                if (emotion.description.equals(description)) {
                    return emotion.code;
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    enum KolTierEnum {
        DOUYIN_TIER1("douyin", "T1", "超头部", 10000000L, Long.max(10000000L, Long.MAX_VALUE)),
        DOUYIN_TIER2("douyin", "T2", "头部", 5000000L, 10000000L),
        DOUYIN_TIER3("douyin", "T3", "腰部", 1000000L, 5000000L),
        DOUYIN_TIER4("douyin", "T4", "尾部", 100000L, 1000000L),
        DOUYIN_TIER5("douyin", "T5", "素人", 0L, 100000L),
        XIAOHONGSHU_TIER1("xiaohongshu", "T1", "超头部", 1000000L, Long.max(10000000L, Long.MAX_VALUE)),
        XIAOHONGSHU_TIER2("xiaohongshu", "T2", "头部", 500000L, 1000000L),
        XIAOHONGSHU_TIER3("xiaohongshu", "T3", "腰部", 100000L, 500000L),
        XIAOHONGSHU_TIER4("xiaohongshu", "T4", "尾部", 10000L, 100000L),
        XIAOHONGSHU_TIER5("xiaohongshu", "T5", "素人", 0L, 10000L),
        WEIBO_TIER1("weibo", "T1", "超头部", 10000000L, Long.max(10000000L, Long.MAX_VALUE)),
        WEIBO_TIER2("weibo", "T2", "头部", 5000000L, 10000000L),
        WEIBO_TIER3("weibo", "T3", "腰部", 1000000L, 5000000L),
        WEIBO_TIER4("weibo", "T4", "尾部", 100000L, 1000000L),
        WEIBO_TIER5("weibo", "T5", "素人", 0L, 100000L),
        BILIBILI_TIER1("bilibili", "T1", "超头部", 1000000L, Long.max(10000000L, Long.MAX_VALUE)),
        BILIBILI_TIER2("bilibili", "T2", "头部", 500000L, 1000000L),
        BILIBILI_TIER3("bilibili", "T3", "腰部", 100000L, 500000L),
        BILIBILI_TIER4("bilibili", "T4", "尾部", 10000L, 100000L),
        BILIBILI_TIER5("bilibili", "T5", "素人", 0L, 10000L),
        WEIXIN_TIER1("weixin", "T1", "超头部", 5000000L, Long.max(5000000L, Long.MAX_VALUE)),
        WEIXIN_TIER2("weixin", "T2", "头部", 1000000L, 5000000L),
        WEIXIN_TIER3("weixin", "T3", "腰部", 100000L, 1000000L),
        WEIXIN_TIER4("weixin", "T4", "尾部", 10000L, 100000L),
        WEIXIN_TIER5("weixin", "T5", "素人", 0L, 10000L),

        ;

        private String platform;
        private String name;
        private String tier;
        private Long followerMin;
        private Long followerMax;

        KolTierEnum(String platform, String tier, String name, Long followerMin, Long followerMax) {
            this.platform = platform;
            this.tier = tier;
            this.name = name;
            this.followerMin = followerMin;
            this.followerMax = followerMax;
        }

        // 根据平台名和粉丝量获取对应的层级
        public static String getTierByPlatformAndFollowers(String platform, Long followers) {
            if (Objects.isNull(followers)) {
                return null;
            }
            for (KolTierEnum kolTier : KolTierEnum.values()) {
                if (kolTier.platform.equals(platform) && followers >= kolTier.followerMin && followers <= kolTier.followerMax) {
                    return kolTier.name;
                }
            }
            return null; // 如果未找到匹配的层级，则返回null或其他默认值
        }

        // 根据平台名和层级获取对应的粉丝量范围
        public static String[] getFollowersRangeByPlatformAndTier(String platform, String tier) {
            for (KolTierEnum kolTier : KolTierEnum.values()) {
                if (kolTier.platform.equals(platform) && kolTier.name.equals(tier)) {
                    return new String[]{String.valueOf(kolTier.followerMin), String.valueOf(kolTier.followerMax)};
                }
            }
            return null; // 如果未找到匹配的粉丝量范围，则返回null或其他默认值
        }
    }


}
