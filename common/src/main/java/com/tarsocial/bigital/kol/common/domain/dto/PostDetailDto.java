package com.tarsocial.bigital.kol.common.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 百事传输字段
 * <AUTHOR>
 */
@Data
public class PostDetailDto {

    @JsonProperty("name")
    private Object name;

    @JsonProperty("publishAt")
    private Object publishAt;


    @JsonProperty("readCount")
    private Object readCount;

    @JsonProperty("platform")
    private Object platform;

//    @JsonProperty("domain")
//    private Object domain;

    @JsonProperty("interaction")
    private Object interaction;

    @JsonProperty("repostsCount")
    private Object repostsCount;

    @JsonProperty("followersCount")
    private Object followersCount;

    @JsonProperty("shareCount")
    private Object shareCount;

    @JsonProperty("commentsCount")
    private Object commentsCount;

    @JsonProperty("userId")
    private Object userId;
//
//    @JsonProperty("zaikan")
//    private Object zaikan;


//
//    @JsonProperty("danmuCount")
//    private Object danmuCount;
//
//    @JsonProperty("coinCount")
//    private Object coinCount;


    @JsonProperty("likeCount")
    private Object likeCount;


    @JsonProperty("nickname")
    private Object nickname;

    @JsonProperty("province")
    private Object province;

    @JsonProperty("city")
    private Object city;

    @JsonProperty("country")
    private Object country;

    @JsonProperty("location")
    private Object location;

    @JsonProperty("ipLocation")
    private Object ipLocation;


    @JsonProperty("collectCount")
    private Object collectCount;

    @JsonProperty("userLikeCount")
    private Object userLikeCount;

    @JsonProperty("id")
    private Object id;

    @JsonProperty("url")
    private Object url;

    @JsonProperty("content")
    private Object content;

    @JsonProperty("mediaType")
    private Object mediaType;

    @JsonProperty("impCount")
    private Object impCount;

    @JsonProperty("duration")
    private Object duration;

    @JsonProperty("verifiedType")
    private Object verifiedType;

    @JsonProperty("mcn")
    private Object mcn;

    @JsonProperty("isKOL")
    private Object isKOL;

    @JsonProperty("pbw")
    private Object pbw;

    @JsonProperty("title")
    private Object title;

    @JsonProperty("contentLength")
    private Object contentLength;

    @JsonProperty("coverUrl")
    private Object coverUrl;

    @JsonProperty("ocrCover")
    private Object ocrCover;

    @JsonProperty("videoUrl")
    private Object videoUrl;

    @JsonProperty("asr")
    private Object asr;

    @JsonProperty("ocr")
    private Object ocr;

    @JsonProperty("picUrl")
    private Object picUrl;

    @JsonProperty("hashtags")
    private Object hashtags;

    @JsonProperty("hashtagsCount")
    private Object hashtagsCount;

    @JsonProperty("postType")
    private Object postType;

    @JsonProperty("contentType")
    private Object contentType;

    @JsonProperty("updateAt")
    private Object updateAt;

    @JsonProperty("birthday")
    private Object birthday;

    @JsonProperty("emotion")
    private Object emotion;

    @JsonProperty("userDescription")
    private Object userDescription;

    @JsonProperty("userGender")
    private Object userGender;

    @JsonProperty("noteCount")
    private Object noteCount;


    @JsonProperty("dongtaiCount")
    private Object dongtaiCount;

    @JsonProperty("musicCount")
    private Object musicCount;

    @JsonProperty("videoCount")
    private Object videoCount;

    @JsonProperty("favoritCount")
    private Object favoritCount;

    @JsonProperty("verified")
    private Object verified;



}