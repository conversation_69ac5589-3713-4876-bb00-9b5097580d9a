package com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BilibiliKolInfo {

    private String nickname;      // 用户昵称
    private String userId;           // 用户UID
    private Boolean isSettled;    // 是否入驻
    private Long followersCount;       // 粉丝数
    private List<String> userTags;           // 用户标签


    private Long pricePost;    //直发价格
    private Long priceForward;    //转发价格
    private Long priceCustomer;    //定制视频价格
    private Long priceEmbedding;    //植入视频价格
    private Long likeMedian;    //点赞中位数
    private Long commentMedian;    //评论中位数
    private Long collectMedian;    //收藏中位数
    private Long playMedian;    //播放量中位数
    private Long danmuMedian;    //弹幕中位数


    private String updateTime;      // 更新时间

    private List<BilibiliKolInfo.PaidPostInfo> postList;

    @Data
    public static class PaidPostInfo implements Serializable {
        private Boolean isPaid; // 是否商单
        private String postId;     // 内容ID
        private Long readCount;       // 阅读/播放数
        private String coverUrl;      // 封面URL
        private String title;         // 标题     实际为正文   tx_content
        private String url;           // 内容URL
        private Long duration;     // 视频时长（秒）
        private String publishTime;     // 发帖时间
        private Long repostsCount;      // 转发数
        private Long commentCount;    // 评论数
        private Long likeCount;        // 点赞数
        private Long collectCount;     // 收藏数
        private Long coinCount;    // 投币数
        private Long danmuCount; // 弹幕数

        private Long interaction; // 互动量    转发+评论+点赞+收藏+投币


    }


}
