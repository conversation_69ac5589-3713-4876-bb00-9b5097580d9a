package com.tarsocial.bigital.kol.common.domain.request.amp;

import com.tarsocial.bigital.kol.common.domain.request.MinMaxRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class AMPFilterRequest implements Serializable {

    private List<AMPKolTypeRequest> kolType;

    private List<MinMaxRequest> kolTier;

    private String startDate;

    private String endDate;

    /**
     * 平台   douyin/xiaohongshu
     */
    @NotEmpty(message = "platform must not be null")
    private String platform;

    private AMPKeywordRequest keyword;


}
