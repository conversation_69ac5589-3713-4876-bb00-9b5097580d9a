package com.tarsocial.bigital.kol.common.domain.request.hair;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @title HairMonitorTaskRequest
 * @date 2024/12/27 15:14
 * @description 「HairMonitorTaskRequest」
 */
@Data
public class HairMonitorTaskRequest {

    /**
     * 单条帖子链接，各平台支持链接格式见上表【通用参数-作品链接】
     */
    @NotBlank(message = "Url不能为空")
    private  String postUrl;

    /**
     * 抖音：douyin
     * 小红书：xiaohongshu
     * B站：bilibili
     * 快手：kuaishou
     * 微博：weibo
     * 知乎：zhihu
     */
    @NotBlank(message = "platform不能为空")
    private String platform;

}
