package com.tarsocial.bigital.kol.common.domain.dto.mengniu;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AlgorithmResultDto implements Serializable {

    private String topic_name;

    private List<String> platform;

    private String topic_description;

    //V2 最相关TOP
    private List<String> samples;

    //V1 最相关TOP
    private List<String> post_ids;

    //V2 all post
    private List<String> post_id_list;

    private Integer is_relevant;

    private String reason;

    private String marketing_approach;

}
