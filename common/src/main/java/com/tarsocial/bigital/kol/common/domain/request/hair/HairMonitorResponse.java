package com.tarsocial.bigital.kol.common.domain.request.hair;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @title HairMonitorResponse
 * @date 2024/12/27 18:29
 * @description 「HairMonitorResponse」
 */
@Data
public class HairMonitorResponse {

    /**
     * 帖子id
     */
    private String postId;
    /**
     * 平台
     */
    private String platform;
    /**
     *  采集时间
     */
    private Date updateTime;
    /**
     * 监控频次
     */
    private String frequencyType;

    /**
     * 作品发布时间
     */
    private String publishedTime;
    /**
     * 正文
     */
    private String content;

    /**
     * 播放/阅读数
     */
    private Integer playCount;

    /**
     * 互动数
     */
    private Integer interactionCount;
    /**
     *  转发/分享
     */
    private Integer forwardCount;
    /**
     * 评论
     */
    private Integer commentCount;

    /**
     * 点赞
     */
    private Integer likeCount;

    /**
     * 收藏
     */
    private Integer collectCount;
    /**
     * 弹幕
     */
    private Integer bulletCount;
    /**
     * 投币 -b战
     */
    private  Integer coinCount;
    /**
     * 在看-公众号
     */
    private Integer viewCount;

    /**
     *  赞同
     */
    private Integer agreeCount;

    /**
     * 曝光
     */
    private Integer exposureCount;

    /**
     * 话题标签
     */
    private List<String> topicTags;

    private Double cpm;

    private Double cpe;

    private Double cps;

    private Double cpa3;
    /**
     * 新增A3
     */
    private Integer newlyA3;
    /**
     * 新增自然流量曝光
     */
    private Integer naturalExposureCount;


    /**
     * 自动流量/播放数
     */
    private Integer naturalPlayReadCount;

    /**
     * 推广曝光
     */
    private Integer promotionExposureCount;

    /**
     * 推广阅读
     */
    private Integer promotionReadCount;

    /**
     * 加热曝光
     */
    private Integer heatedExposureCount;
    /**
     * 加热阅读
     */
    private Integer heatedReadCount;
    /**
     * 加热互动
     */
    private Integer heatedInteractionCount;

    /**
     * 是否投流
     */
    private Boolean isHeated;
    /**
     * 组件类型
     */
    private Integer compType;

    /**
     * 组件曝光量
     */
    private Integer compImpNum;
    /**
     * 组件点击量
     */
    private Integer compClickPvNum;

    /**
     * 组件点击人数
     */
    private Integer compClickUvNum;

    /**
     * 组件ctr
     */
    private Integer clickPvRate;

    private Long  postSearchCount;


    private  Long postSearchUserCount;


    private  Double postSearchRate;

    /**
     *  2日回搜次数
     */
    private Long  backSearchCount2d;
    /**
     * 2日回搜人数
     */
    private  Long backSearchUserCount2d;

    /**
     * 2日回搜率
     */
    private  Long backSearchRate2d;



    /**
     *  2日回搜次数
     */
    private Long  backSearchCount7d;

    /**
     * 2日回搜人数
     */
    private  Long backSearchUserCount7d;

    /**
     * 2日回搜率
     */
    private  Long backSearchRate7d;

    /**
     *  2日回搜次数
     */
    private Long  backSearchCount15d;

    /**
     * 2日回搜人数
     */
    private  Long backSearchUserCount15d;

    /**
     * 2日回搜率
     */
    private  Long backSearchRate15d;

}
