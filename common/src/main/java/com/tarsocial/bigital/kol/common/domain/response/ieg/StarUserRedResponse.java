package com.tarsocial.bigital.kol.common.domain.response.ieg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StarUserRedResponse {

    private String user_id;
    private String redId;
    private String nickname;
    private Boolean is_star;
    private List<ContentTagsDTO> contentTags;
    private Object personalTags;
    private String mcn_name;
    private String mcn_id;
    private Integer read_median;
    private Integer like_median;
    private Integer comment_median;
    private Integer collect_median;
    private Double interaction_rate;
    private Integer video_rate;
    private Integer cooperation_read_median;
    private Integer cooperation_like_median;
    private Integer cooperation_comment_median;
    private Integer cooperation_collect_median;
    private Double cooperation_interaction_rate;
    private Double cooperation_video_rate;
    private String index_url;
    private GenderDTO gender;
    private List<GenderAgesDTO> genderAges;
    private List<PercentDTO> city;
    private List<PercentDTO> interests;
    private List<FansOverallsDTO> fansOveralls;
    private Integer videoPrice;
    private Integer picturePrice;
    private String update_time;
    private List<PercentDTO> device;
    private String sex;

    @NoArgsConstructor
    @Data
    public static class GenderDTO {
        private Double female;
        private Double male;
    }

    @NoArgsConstructor
    @Data
    public static class ContentTagsDTO {
        private String taxonomy1Tag;
        private List<String> taxonomy2Tags;
    }

    @NoArgsConstructor
    @Data
    public static class GenderAgesDTO {
        private String group;
        private Double percent;
    }

    @NoArgsConstructor
    @Data
    public static class PercentDTO {
        private String name;
        private Double percent;
    }

    @NoArgsConstructor
    @Data
    public static class FansOverallsDTO {
        private String dateKey;
        private Integer fansNum;
    }
}
