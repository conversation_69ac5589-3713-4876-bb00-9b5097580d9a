package com.tarsocial.bigital.kol.common.domain.entity.ocr;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OcrPost implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 平台
     */
    private String kwPlatform;

    /**
     * 帖子id
     */
    private String kwId;

    /**
     * 封面url
     */
    private String kwCoverUrl;

    /**
     * 内容类型
     */
    private String kwContentType;

    /**
     * 内容
     */
    private String txContent;

    /**
     * 多图时返回多图ID，用来拼接图片url。用返回字段thumbnail_pic的地址配上该返回字段的图片ID，即可得到多个图片url。
     */
    private List<String> kwPicUrl;

    /**
     * 视频链接
     */
    private List<String> kwVideoUrl;

    /**
     * 帖子链接
     */
    private String kwUrl;

    private List<ObjectOcrRowData> objectOcrRowdata;

    private List<ObjectAsrRowData> objectAsrRowdata;

    @Data
    public static class ObjectOcrRowData {
        private String txText;
        private Long longTime;
    }

    @Data
    public static class ObjectAsrRowData {
        private String txText;
        private Long longStartTime;
        private Long longEndTime;
    }

}
