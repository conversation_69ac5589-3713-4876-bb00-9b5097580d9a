package com.tarsocial.bigital.kol.common.domain.enums;


import lombok.Getter;


@Getter
public enum EntityTypeEnum {


    BRAND("brand", "品牌"),
    ELEMENT("element", "成分"),
    FORM("form", "Form"),
    FORM2("form", "品类"),
    FUNCTION("function", "功效"),
    HAIR_QUALITY("hair_quality", "发质"),
    PACK("pack", "包装"),
    PAIN_SPOT("pain_spot", "痛点"),
    PRODUCT("product", "产品"),
    PRODUCT_USE("product_use", "产品使用"),
    SCENE("scene", "场景"),
    SKIN_TYPE("skin_type", "肤质"),
    SMELL("smell", "香味"),
    TA("ta", "TA"),
    TA2("ta", "人群"),
    ;

    private final String code;
    private final String name;


    EntityTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        final EntityTypeEnum[] values = EntityTypeEnum.values();
        for (EntityTypeEnum value : values) {
            if (value.code.equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        final EntityTypeEnum[] values = EntityTypeEnum.values();
        for (EntityTypeEnum value : values) {
            if (value.name.equals(name)) {
                return value.getCode();
            }
        }
        return null;
    }

}
