package com.tarsocial.bigital.kol.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/3/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "abi_push_data_log")
public class AbiPushDataLogEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private LocalDateTime pushStartTime;
    private LocalDateTime pushEndTime;
    private Integer dataCount;
    private Integer code;
    private String message;
    private LocalDateTime createTime;

}
