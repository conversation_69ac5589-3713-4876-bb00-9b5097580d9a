package com.tarsocial.bigital.kol.common.domain.request.tencent.ieg;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class IegTaskCreateRequest {

    @NotEmpty(message = "ids is not null")
    private List<String> ids;

    @NotEmpty(message = "platform is not null")
    private String platform;

    private String token;
}
