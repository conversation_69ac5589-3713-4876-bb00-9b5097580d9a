package com.tarsocial.bigital.kol.common.domain.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseResponse<T> {
    private static final int CODE_ERROR = 500;

    private int code;
    private String message;
    private String traceId;
    private T data;

    public BaseResponse(T data) {
        this.code = 200;
        this.message = "success";
        this.traceId = MDC.get("traceId");
        this.data = data;
    }

    public BaseResponse(Integer code, String message) {
        this.code = code;
        this.message = message;
        this.traceId = MDC.get("traceId");
        this.data = null;
    }


    public boolean success() {
        return this.code == 200;
    }

    public static <T> BaseResponse<T> error(String message) {
        return new BaseResponse<>(CODE_ERROR, message);
    }

}
