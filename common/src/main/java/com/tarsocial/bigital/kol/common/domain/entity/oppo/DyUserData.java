package com.tarsocial.bigital.kol.common.domain.entity.oppo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator 2024/11/21
*/
@Data
@TableName(value = "dy_user_data")
public class DyUserData {
    /**
     * 主键，自增
     */
     @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 更新日期
     */
    @TableField(value = "update_date")
    private String updateDate;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 被查询次数
     */
    @TableField(value = "query_number")
    private Integer queryNumber;

    /**
     * 用户昵称
     */
    @TableField(value = "user_nickname")
    private String userNickname;

    /**
     * user_id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 粉丝数
     */
    @TableField(value = "followers_count")
    private Long followersCount;

    /**
     * 用户头像
     */
    @TableField(value = "user_avatar")
    private String userAvatar;

    /**
     * CPM
     */
    @TableField(value = "cpm")
    private Double cpm;

    /**
     * 预期播放量
     */
    @TableField(value = "expected_play_count")
    private Long expectedPlayCount;

    /**
     * 短视频价格
     */
    @TableField(value = "short_video_price")
    private Long shortVideoPrice;

    /**
     * 长视频价格
     */
    @TableField(value = "long_video_price")
    private Long longVideoPrice;

    /**
     * 特长视频价格
     */
    @TableField(value = "special_video_price")
    private Long specialVideoPrice;

    /**
     * 非商单播放中位数
     */
    @TableField(value = "non_commercial_play_median")
    private Long nonCommercialPlayMedian;

    /**
     * 非商单完播率
     */
    @TableField(value = "non_commercial_completion_rate")
    private Double nonCommercialCompletionRate;

    /**
     * 非商单互动率
     */
    @TableField(value = "non_commercial_engagement_rate")
    private Double nonCommercialEngagementRate;

    /**
     * 非商单互动量
     */
    @TableField(value = "non_commercial_engagement_count")
    private Long nonCommercialEngagementCount;

    /**
     * 商单播放中位数
     */
    @TableField(value = "commercial_play_median")
    private Long commercialPlayMedian;

    /**
     * 商单完播率
     */
    @TableField(value = "commercial_completion_rate")
    private Double commercialCompletionRate;

    /**
     * 商单互动率
     */
    @TableField(value = "commercial_engagement_rate")
    private Double commercialEngagementRate;

    /**
     * 商单互动量
     */
    @TableField(value = "commercial_engagement_count")
    private Long commercialEngagementCount;

    /**
     * 粉丝设备分布
     */
    @TableField(value = "followers_device_distribution")
    private String followersDeviceDistribution;

    /**
     * 粉丝地域分布
     */
    @TableField(value = "followers_region_distribution")
    private String followersRegionDistribution;

    /**
     * 粉丝城市分布
     */
    @TableField(value = "followers_city_distribution")
    private String followersCityDistribution;

    /**
     * 粉丝性别分布
     */
    @TableField(value = "followers_gender_distribution")
    private String followersGenderDistribution;

    /**
     * 粉丝年龄分布
     */
    @TableField(value = "followers_age_distribution")
    private String followersAgeDistribution;

    /**
     * 粉丝城市等级
     */
    @TableField(value = "followers_city_level")
    private String followersCityLevel;

    /**
     * MCN
     */
    @TableField(value = "mcn")
    private String mcn;

    /**
     * 粉丝八大人群
     */
    @TableField(value = "followers_groups")
    private String followersGroups;

    /**
     * 行业标签
     */
    @TableField(value = "industry_tags")
    private String industryTags;

    /**
     * 行业标签1
     */
    @TableField(value = "industry_sub_tags")
    private String industrySubTags;

    /**
     * 内容标签
     */
    @TableField(value = "content_tags")
    private String contentTags;

    /**
     * 内容标签1
     */
    @TableField(value = "content_sub_tags")
    private String contentSubTags;

    /**
     * 转化指数
     */
    @TableField(value = "conversion_index")
    private Double conversionIndex;

    /**
     * 星图传播指数
     */
    @TableField(value = "star_map_communication_index")
    private Double starMapCommunicationIndex;

    /**
     * 星图种草指数
     */
    @TableField(value = "star_map_grassroots_index")
    private Double starMapGrassrootsIndex;

    /**
     * 星图性价比指数
     */
    @TableField(value = "star_map_cost_effectiveness_index")
    private Double starMapCostEffectivenessIndex;

    /**
     * 星图合作指数
     */
    @TableField(value = "star_map_cooperation_index")
    private Double starMapCooperationIndex;

    /**
     * 月连接用户数
     */
    @TableField(value = "monthly_connected_users")
    private Long monthlyConnectedUsers;

    /**
     * 月深度用户数
     */
    @TableField(value = "monthly_depth_users")
    private Long monthlyDepthUsers;

    /**
     * 30_商单播放中位数
     */
    @TableField(value = "commercial_play_median_30d")
    private Long commercialPlayMedian30d;

    /**
     * 30_商单完播率
     */
    @TableField(value = "commercial_completion_rate_30d")
    private Double commercialCompletionRate30d;

    /**
     * 30_商单互动率
     */
    @TableField(value = "commercial_engagement_rate_30d")
    private Double commercialEngagementRate30d;

    /**
     * 30_商单互动量
     */
    @TableField(value = "commercial_engagement_count_30d")
    private Long commercialEngagementCount30d;

    /**
     * 30_非商单播放中位数
     */
    @TableField(value = "non_commercial_play_median_30d")
    private Long nonCommercialPlayMedian30d;

    /**
     * 30_非商单完播率
     */
    @TableField(value = "non_commercial_completion_rate_30d")
    private Double nonCommercialCompletionRate30d;

    /**
     * 30_非商单互动率
     */
    @TableField(value = "non_commercial_engagement_rate_30d")
    private Double nonCommercialEngagementRate30d;

    /**
     * 30_非商单互动量
     */
    @TableField(value = "non_commercial_engagement_count_30d")
    private Long nonCommercialEngagementCount30d;

    /**
     * 星图链接
     */
    @TableField(value = "star_url")
    private String starUrl;

    /**
     * 主页链接
     */
    @TableField(value = "user_url")
    private String userUrl;

    /**
     * 15天增粉率
     */
    @TableField(value = "follower_growth_rate_15d")
    private Double followerGrowthRate15d;

    /**
     * 30天增粉率
     */
    @TableField(value = "follower_growth_rate_30d")
    private Double followerGrowthRate30d;

    /**
     * 近30天粉丝增长数
     */
    @TableField(value = "follower_growth_30d")
    private Long followerGrowth30d;

    /**
     * 观众性别分布
     */
    @TableField(value = "audience_gender_distribution")
    private String audienceGenderDistribution;

    /**
     * 观众年龄分布
     */
    @TableField(value = "audience_age_distribution")
    private String audienceAgeDistribution;

    /**
     * 观众地域分布
     */
    @TableField(value = "audience_region_distribution")
    private String audienceRegionDistribution;

    /**
     * 观众城市分布
     */
    @TableField(value = "audience_city_distribution")
    private String audienceCityDistribution;

    /**
     * 观众城市等级分布
     */
    @TableField(value = "audience_city_level_distribution")
    private String audienceCityLevelDistribution;

    /**
     * 观众设备分布
     */
    @TableField(value = "audience_device_distribution")
    private String audienceDeviceDistribution;

    /**
     * 观众八大人群分布
     */
    @TableField(value = "audience_major_groups_distribution")
    private String audienceMajorGroupsDistribution;

    /**
     * 达人描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 非商单平均转发-90天
     */
    @TableField(value = "non_commercial_avg_shares_90d")
    private Long nonCommercialAvgShares90d;

    /**
     * 非商单平均评论-90天
     */
    @TableField(value = "non_commercial_avg_comments_90d")
    private Long nonCommercialAvgComments90d;

    /**
     * 非商单平均点赞-90天
     */
    @TableField(value = "non_commercial_avg_likes_90d")
    private Long nonCommercialAvgLikes90d;

    /**
     * 商单平均转发-90天
     */
    @TableField(value = "commercial_avg_shares_90d")
    private Long commercialAvgShares90d;

    /**
     * 商单平均评论-90天
     */
    @TableField(value = "commercial_avg_comments_90d")
    private Long commercialAvgComments90d;

    /**
     * 商单平均点赞-90天
     */
    @TableField(value = "commercial_avg_likes_90d")
    private Long commercialAvgLikes90d;

    /**
     * 非商单平均转发-30天
     */
    @TableField(value = "non_commercial_avg_shares_30d")
    private Long nonCommercialAvgShares30d;

    /**
     * 非商单平均评论-30天
     */
    @TableField(value = "non_commercial_avg_comments_30d")
    private Long nonCommercialAvgComments30d;

    /**
     * 非商单平均点赞-30天
     */
    @TableField(value = "non_commercial_avg_likes_30d")
    private Long nonCommercialAvgLikes30d;

    /**
     * 商单平均转发-30天
     */
    @TableField(value = "commercial_avg_shares_30d")
    private Long commercialAvgShares30d;

    /**
     * 商单平均评论-30天
     */
    @TableField(value = "commercial_avg_comments_30d")
    private Long commercialAvgComments30d;

    /**
     * 商单平均点赞-30天
     */
    @TableField(value = "commercial_avg_likes_30d")
    private Long commercialAvgLikes30d;

    /**
     * 涨粉黑马榜
     */
    @TableField(value = "follower_growth_rank")
    private Long followerGrowthRank;

    /**
     * 商业合作品牌
     */
    @TableField(value = "collaboration_brands")
    private String collaborationBrands;

    /**
     * 商业合作品牌
     */
    @TableField(value = "collaboration_brands_beauty")
    private String collaborationBrandsBeauty;

    /**
     * 图文价格
     */
    @TableField(value = "image_text_price")
    private Long imageTextPrice;

    /**
     * 达人身份标签
     */
    @TableField(value = "identity_tags")
    private String identityTags;

    /**
     * 达人社会身份标签
     */
    @TableField(value = "social_identity_tags")
    private String socialIdentityTags;

    /**
     * 看后搜指数
     */
    @TableField(value = "search_index")
    private Double searchIndex;

    /**
     * 预期CPA3等级
     */
    @TableField(value = "expected_cpa_level_3")
    private Long expectedCpaLevel3;

    /**
     * 曝光率
     */
    @TableField(value = "explosive_article_rate")
    private Double explosiveArticleRate;

    public static final String COL_UPDATE_DATE = "update_date";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_QUERY_NUMBER = "query_number";

    public static final String COL_USER_NICKNAME = "user_nickname";

    public static final String COL_USER_ID = "user_id";

    public static final String COL_FOLLOWERS_COUNT = "followers_count";

    public static final String COL_USER_AVATAR = "user_avatar";

    public static final String COL_CPM = "cpm";

    public static final String COL_EXPECTED_PLAY_COUNT = "expected_play_count";

    public static final String COL_SHORT_VIDEO_PRICE = "short_video_price";

    public static final String COL_LONG_VIDEO_PRICE = "long_video_price";

    public static final String COL_SPECIAL_VIDEO_PRICE = "special_video_price";

    public static final String COL_NON_COMMERCIAL_PLAY_MEDIAN = "non_commercial_play_median";

    public static final String COL_NON_COMMERCIAL_COMPLETION_RATE = "non_commercial_completion_rate";

    public static final String COL_NON_COMMERCIAL_ENGAGEMENT_RATE = "non_commercial_engagement_rate";

    public static final String COL_NON_COMMERCIAL_ENGAGEMENT_COUNT = "non_commercial_engagement_count";

    public static final String COL_COMMERCIAL_PLAY_MEDIAN = "commercial_play_median";

    public static final String COL_COMMERCIAL_COMPLETION_RATE = "commercial_completion_rate";

    public static final String COL_COMMERCIAL_ENGAGEMENT_RATE = "commercial_engagement_rate";

    public static final String COL_COMMERCIAL_ENGAGEMENT_COUNT = "commercial_engagement_count";

    public static final String COL_FOLLOWERS_DEVICE_DISTRIBUTION = "followers_device_distribution";

    public static final String COL_FOLLOWERS_REGION_DISTRIBUTION = "followers_region_distribution";

    public static final String COL_FOLLOWERS_CITY_DISTRIBUTION = "followers_city_distribution";

    public static final String COL_FOLLOWERS_GENDER_DISTRIBUTION = "followers_gender_distribution";

    public static final String COL_FOLLOWERS_AGE_DISTRIBUTION = "followers_age_distribution";

    public static final String COL_FOLLOWERS_CITY_LEVEL = "followers_city_level";

    public static final String COL_MCN = "mcn";

    public static final String COL_FOLLOWERS_GROUPS = "followers_groups";

    public static final String COL_INDUSTRY_TAGS = "industry_tags";

    public static final String COL_INDUSTRY_SUB_TAGS = "industry_sub_tags";

    public static final String COL_CONTENT_TAGS = "content_tags";

    public static final String COL_CONTENT_SUB_TAGS = "content_sub_tags";

    public static final String COL_CONVERSION_INDEX = "conversion_index";

    public static final String COL_STAR_MAP_COMMUNICATION_INDEX = "star_map_communication_index";

    public static final String COL_STAR_MAP_GRASSROOTS_INDEX = "star_map_grassroots_index";

    public static final String COL_STAR_MAP_COST_EFFECTIVENESS_INDEX = "star_map_cost_effectiveness_index";

    public static final String COL_STAR_MAP_COOPERATION_INDEX = "star_map_cooperation_index";

    public static final String COL_MONTHLY_CONNECTED_USERS = "monthly_connected_users";

    public static final String COL_MONTHLY_DEPTH_USERS = "monthly_depth_users";

    public static final String COL_COMMERCIAL_PLAY_MEDIAN_30D = "commercial_play_median_30d";

    public static final String COL_COMMERCIAL_COMPLETION_RATE_30D = "commercial_completion_rate_30d";

    public static final String COL_COMMERCIAL_ENGAGEMENT_RATE_30D = "commercial_engagement_rate_30d";

    public static final String COL_COMMERCIAL_ENGAGEMENT_COUNT_30D = "commercial_engagement_count_30d";

    public static final String COL_NON_COMMERCIAL_PLAY_MEDIAN_30D = "non_commercial_play_median_30d";

    public static final String COL_NON_COMMERCIAL_COMPLETION_RATE_30D = "non_commercial_completion_rate_30d";

    public static final String COL_NON_COMMERCIAL_ENGAGEMENT_RATE_30D = "non_commercial_engagement_rate_30d";

    public static final String COL_NON_COMMERCIAL_ENGAGEMENT_COUNT_30D = "non_commercial_engagement_count_30d";

    public static final String COL_STAR_URL = "star_url";

    public static final String COL_USER_URL = "user_url";

    public static final String COL_FOLLOWER_GROWTH_RATE_15D = "follower_growth_rate_15d";

    public static final String COL_FOLLOWER_GROWTH_RATE_30D = "follower_growth_rate_30d";

    public static final String COL_FOLLOWER_GROWTH_30D = "follower_growth_30d";

    public static final String COL_AUDIENCE_GENDER_DISTRIBUTION = "audience_gender_distribution";

    public static final String COL_AUDIENCE_AGE_DISTRIBUTION = "audience_age_distribution";

    public static final String COL_AUDIENCE_REGION_DISTRIBUTION = "audience_region_distribution";

    public static final String COL_AUDIENCE_CITY_DISTRIBUTION = "audience_city_distribution";

    public static final String COL_AUDIENCE_CITY_LEVEL_DISTRIBUTION = "audience_city_level_distribution";

    public static final String COL_AUDIENCE_DEVICE_DISTRIBUTION = "audience_device_distribution";

    public static final String COL_AUDIENCE_MAJOR_GROUPS_DISTRIBUTION = "audience_major_groups_distribution";

    public static final String COL_DESCRIPTION = "description";

    public static final String COL_NON_COMMERCIAL_AVG_SHARES_90D = "non_commercial_avg_shares_90d";

    public static final String COL_NON_COMMERCIAL_AVG_COMMENTS_90D = "non_commercial_avg_comments_90d";

    public static final String COL_NON_COMMERCIAL_AVG_LIKES_90D = "non_commercial_avg_likes_90d";

    public static final String COL_COMMERCIAL_AVG_SHARES_90D = "commercial_avg_shares_90d";

    public static final String COL_COMMERCIAL_AVG_COMMENTS_90D = "commercial_avg_comments_90d";

    public static final String COL_COMMERCIAL_AVG_LIKES_90D = "commercial_avg_likes_90d";

    public static final String COL_NON_COMMERCIAL_AVG_SHARES_30D = "non_commercial_avg_shares_30d";

    public static final String COL_NON_COMMERCIAL_AVG_COMMENTS_30D = "non_commercial_avg_comments_30d";

    public static final String COL_NON_COMMERCIAL_AVG_LIKES_30D = "non_commercial_avg_likes_30d";

    public static final String COL_COMMERCIAL_AVG_SHARES_30D = "commercial_avg_shares_30d";

    public static final String COL_COMMERCIAL_AVG_COMMENTS_30D = "commercial_avg_comments_30d";

    public static final String COL_COMMERCIAL_AVG_LIKES_30D = "commercial_avg_likes_30d";

    public static final String COL_FOLLOWER_GROWTH_RANK = "follower_growth_rank";

    public static final String COL_COLLABORATION_BRANDS = "collaboration_brands";

    public static final String COL_COLLABORATION_BRANDS_BEAUTY = "collaboration_brands_beauty";

    public static final String COL_IMAGE_TEXT_PRICE = "image_text_price";

    public static final String COL_IDENTITY_TAGS = "identity_tags";

    public static final String COL_SOCIAL_IDENTITY_TAGS = "social_identity_tags";

    public static final String COL_SEARCH_INDEX = "search_index";

    public static final String COL_EXPECTED_CPA_LEVEL_3 = "expected_cpa_level_3";

    public static final String EXPLOSIVE_ARTICLE_RATE = "explosive_article_rate";
}