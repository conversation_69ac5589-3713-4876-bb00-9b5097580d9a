package com.tarsocial.bigital.kol.common.domain.request.hair;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @title HairExportKolResponse
 * @date 2025/1/2 17:33
 * @description 「HairExportKolResponse」
 */
@Data
public class HairExportKolResponse {


    // 人物状态
    private String status = "success";

    private String platform;

    private String userId;

    private String platformUserId;

    private String secUid;

    private String kolId;

    private String nickname;

    private String avatar;

    private String url;

    private String starUrl;

    private String province;
    private String city;
    private String gender;
    private String description;
    private Boolean isStar;
    /**
     * 抖音、红书、知乎、B站、快手
     */
    private String mcnName;

    private Long followersCount;
    /**
     * 月深度用户数
     */
    private Long inter;

    /**
     * 月链接用户数
     */
    private Long cover;


    /**
     * 粉丝增长量
     */
    private Long fansIncrement;

    /**
     * 粉丝增长率
     */
    private Double fansIncrementRate;

    /**
     * 优质粉丝数
     */
    private Long followersCover;
    //followersQuality

    /**
     * 粉丝质量
     */
    private Double followersQuality;

    /**
     * 社会身份
     */
    private List<String> socialStatusTag;

    /**
     * 行业身份
     */
    private List<String> industryTraitsTag;
    /**
     * 抖音、小红书
     */
    private List<String> socialNetworkTag;

    private List<String> accountType;
    /**
     * 内容类型
     */
    private List<String> tags;

    private String controlStatus;

    private Double responseRate;

    @ApiModelProperty(value = "**粉丝年龄")
    private List<NestedFansCommon> fansAges;

    @ApiModelProperty(value = "粉丝性别")
    private List<NestedFansCommon> fansGender;

    @ApiModelProperty(value = "粉丝城市等级")
    private List<NestedFansCommon> fansCityLevel;

    @ApiModelProperty(value = "粉丝城市")
    private List<NestedFansCommon> fansCity;

    @ApiModelProperty(value = "八大人群")
    private List<ObjectFansTargetDetailDTO> fansTarget;


    @ApiModelProperty(value = "粉丝兴趣")
    private List<NestedFansCommon> fansInterest;

    @ApiModelProperty(value = "粉丝省份")
    private List<NestedFansCommon> fansProvince;

    @ApiModelProperty(value = "粉丝设备")
    private List<NestedFansCommon> fansPhone;


    @ApiModelProperty(value = "观众年龄")
    private List<NestedFansCommon> viewersAge;

    @ApiModelProperty(value = "观众性别")
    private List<NestedFansCommon> viewersGender;
    @ApiModelProperty(value = "观众城市等级")
    private List<NestedFansCommon> viewersCityLevel;

    @ApiModelProperty(value = "观众城市")
    private List<NestedFansCommon> viewersCity;

    @ApiModelProperty(value = "观众八大人群")
    private List<NestedFansCommon> viewersTarget;

    @ApiModelProperty(value = "观众兴趣")
    private List<NestedFansCommon> viewersInterest;

    @ApiModelProperty(value = "观众省份")
    private List<NestedFansCommon> viewersProvince;

    @ApiModelProperty(value = "观众设备")
    private List<NestedFansCommon> viewersPhone;
    /**
     * 铁粉年龄
     */
    private List<NestedFansCommon> diehardAge;

    /**
     * 铁粉性别
     */
    private List<NestedFansCommon> diehardGender;
    /**
     * 铁粉城市等级
     */
    private List<NestedFansCommon> diehardCityLevel;
    /**
     * 铁粉城市
     */
    private List<NestedFansCommon> diehardCity;
    /**
     * 铁粉八大人群
     */
    private List<NestedFansCommon> diehardTarget;
    /**
     * 铁粉兴趣
     */
    private List<NestedFansCommon> diehardInterest;
    /**
     * 铁粉省份
     */
    private List<NestedFansCommon> diehardProvince;

    /**
     * 铁粉设备
     */
    private List<NestedFansCommon> diehardPhone;
    /**
     * 互动平均数
     */
    private Long interactionAvg;

    /**
     * 播放平均数
     */
    private Double readAvg;

    /**
     * 转发平均数
     */
    private Double shareAvg;
    /**
     * 评论平均数
     */
    private Double commentAvg;

    /**
     * 点赞平均数
     */
    private Double likeAvg;
    /**
     * 收藏平均数
     */
    private Long collectAvg;

    /**
     * 投币平均数
     */
    private Long coinCountAvg;

    /**
     * 弹幕平均数
     */
    private Long danmuCountAvg;

    /**
     * 曝光平均数
     */
    private Long impAvg;

    /**
     * 互动率
     */
    private Double interactionRate;
    /**
     * 爆文率
     */
    private Double burstRate;
    /**
     * 帖子数量
     */
    private Long postCount;
    /**
     * 互动总数
     */
    private Long interactionTotal;

    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ObjectFansTargetDetailDTO {

        @JsonProperty("kw_key")
        private String kwKey;
        @JsonProperty("double_value")
        private Double doubleValue;
        @JsonProperty("long_value")
        private Integer longValue;
    }
}
