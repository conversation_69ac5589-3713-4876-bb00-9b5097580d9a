package com.tarsocial.bigital.kol.common.domain.response.amp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AMPKolResponse implements Serializable {

    private String kw_userId;

    private Long long_followersCount;

    private String kw_avatar;

    private String kw_nickname;

    private String kw_url;

    private Long long_avg_interaction;

    private Long long_p6m_postNum;

    private String kw_platformUserId;

    private List<AMPPostResponse> posts;

}
