package com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class KuaishouLiveInfo implements Serializable {

    @Schema(title = "用户昵称")
    private String nickname;

    @Schema(title = "kolId")
    private String kolId;

    @Schema(title = "性别")
    private String gender;

    @Schema(title = "认证信息")
    private String verifiedType;

    @Schema(title = "是否入驻直播广场")
    private Boolean isLivePlaza;

    @Schema(title = "粉丝数")
    private Long followersCount;

    private String mcnName;                 // MCN名称

    private List<String> userTags;           // 用户标签

    private List<String> industryTags;       // 行业标签


    @Schema(title = "品牌推广专场(按小时)价格")
    @JsonAlias("long_priceBrandPromotionByHour")
    private Long priceBrandPromotionByHour;

    @Schema(title = "品牌推广专场(按天)价格")
    @JsonAlias("long_priceBrandPromotionByDay")
    private Long priceBrandPromotionByDay;

    @Schema(title = "品牌推广专场(按商品)价格")
    @JsonAlias("long_priceBrandPromotionByGoods")
    private Long priceBrandPromotionByGoods;

    @Schema(title = "预期观看人数")
    @JsonAlias("long_expectancyPlayCnt")
    private Long expectancyPlayCnt;

    @Schema(title = "预期最高在线人数")
    @JsonAlias("long_expectancyMaxOnlineCnt")
    private Long expectancyMaxOnlineCnt;

    @Schema(title = "预期CPM")
    @JsonAlias("double_expectancyCPM")
    private Double expectancyCPM;

    @Schema(title = "周均直播场次")
    @JsonAlias("double_weeklyLiveCnt")

    private Double weeklyLiveCnt;
    @Schema(title = "近期直播场次")
    @JsonAlias("long_liveCnt")
    private Long liveCnt;

    @Schema(title = "平均观看人次")
    @JsonAlias("long_watchUCnt")
    private Long watchUCnt;

    @Schema(title = "最高在线人数")
    @JsonAlias("long_expectancyPcuCnt")
    private Long expectancyPcuCnt;

    private String updateTime;      // 更新时间


    @Schema(title = "直播间列表")
    @JsonAlias("object_latestLiveRooms")
    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    private List<ObjectLatestLiveRooms> objectLatestLiveRooms;


    @Data
    public static class ObjectLatestLiveRooms implements Serializable {

        @JsonAlias("tx_title")
        private String title;

        //直播ID
        @JsonAlias("room_id")
        private String roomId;

        //封面图
        @JsonAlias("kw_coverUrl")
        private String coverUrl;

        //直播日期
        private String publishedAt;

        @Schema(title = "时长")
        @JsonAlias("long_duration")
        private Long duration;

        //直播观看量
        @JsonAlias("watch_cnt")
        private Long watchCount;

    }


}
