package com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tencent_ieg_user_log")
public class TencentIegUserLog implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 子任务详情
     * 默认 用户ID
     * 星图直播粉丝画像/星图直播观众画像/直播数据概览 KolId
     */
    private String subTaskId;


    /**
     * kolId
     */
    private String kolId;

    /**
     * 平台
     */
    private String platform;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 任务重试次数
     */
    private Integer retry;

    /**
     * 失败原因
     */
    private String errorDetail;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String taskName;

    private String tenantName;

    /**
     * 业务类型：1-IEG直播 ，2-刊例价
     */
    private Integer businessType;
}
