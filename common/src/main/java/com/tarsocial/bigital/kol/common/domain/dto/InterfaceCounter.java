package com.tarsocial.bigital.kol.common.domain.dto;

import com.tarsocial.bigital.kol.common.domain.entity.InterfaceDataCount;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
public class InterfaceCounter {

    private String clientId;

    private String interfaceName;

    private String target;

    private String batchNo;

    private String traceId;

    private String query;

    // 该批次全部数据量
    private Long totalCount;

    // 该批次已经滚动查询的数据量（含本次）
    private Long scrollCount;

    // 本次查询请求数据量
    private Long queryCount;

    // 本次查询返回的数据量
    private Long dataCount;

    private String queryDate;

    private Date queryTime;

    public Boolean hasMore() {
        return this.scrollCount < this.totalCount;
    }

    public Long usageTime() {
        return System.currentTimeMillis() - this.queryTime.getTime();
    }

    public InterfaceDataCount toCount() {
        InterfaceDataCount dataCount = new InterfaceDataCount();
        dataCount.setClientId(this.clientId);
        dataCount.setInterfaceName(this.interfaceName);
        dataCount.setTarget(this.target);
        dataCount.setBatchNo(this.batchNo);
        dataCount.setTraceId(this.traceId);
        dataCount.setQuery(this.query);
        dataCount.setTotalCount(this.totalCount);
        dataCount.setScrollCount(this.scrollCount);
        dataCount.setQueryCount(this.queryCount);
        dataCount.setDataCount(this.dataCount);
        dataCount.setQueryDate(this.queryDate);
        dataCount.setQueryTime(this.queryTime);
        dataCount.setUsageTime(usageTime());
        return dataCount;
    }


    public static InterfaceCounter init() {
        return init(new Date());
    }

    public static InterfaceCounter init(Date queryTime) {
        InterfaceCounter counter = new InterfaceCounter();
        counter.setQueryTime(queryTime);
        counter.setQueryDate(new SimpleDateFormat("yyyyMMdd").format(queryTime));
        return counter;
    }

}
