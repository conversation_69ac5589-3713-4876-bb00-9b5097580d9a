package com.tarsocial.bigital.kol.common.domain.response.mengniu;

import lombok.Data;
import org.slf4j.MDC;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BaseListResponse<T> {

    private Integer size;

    private Integer errcode;

    private String traceId;

    private String errmsg;

    private List<T> data;

    public BaseListResponse(List<T> data) {
        this.errcode = 0;
        this.errmsg = "success";
        this.traceId = MDC.get("traceId");
        this.data = data;
        this.size = data.size();
    }

    public BaseListResponse(Integer code, String message) {
        this.errcode = code;
        this.errmsg = message;
        this.traceId = MDC.get("traceId");
        this.data = null;
    }
}
