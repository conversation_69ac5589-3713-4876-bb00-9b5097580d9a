package com.tarsocial.bigital.kol.common.domain.entity.tencent;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_task")
public class Task implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 话题英文名
     */
    private String topicKey;

    /**
     * 话题名称
     */
    private String topicName;

    /**
     * 帖子发布时间
     */
    private Date publishedAt;

    /**
     * 抓取开始时间
     */
    private Date startTime;

    /**
     * 抓取结束时间
     */
    private Date endTime;

    /**
     * 抓取频率
     */
    private Integer frequency;

    /**
     * 0:正常  1:删除
     */
    private Integer deleted;

    private Date createTime;

    private Date updateTime;
}
