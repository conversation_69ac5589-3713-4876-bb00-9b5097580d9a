package com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RedKolInfo {

    private String nickname;      // 用户昵称
    private String platformUserId;                 // 平台号
    private String userId;           // 用户UID
    private Boolean isSettled;    // 是否入驻
    private Long followersCount;       // 粉丝数
    private String mcnId;                   // MCN ID
    private String mcnName;                 // MCN名称
    private List<String> userTags;           // 用户标签
    private List<String> industryTags;       // 行业标签


    private Long priceVideo;    // 视频笔记一口价
    private Long priceImage;    // 图文笔记一口价

    private Double cpm;    //预期CPM
    private Double cpr;    //预期CPR
    private Double picCpm;    //预期图文cpm
    private Double picCpr;    //预期图文cpr

    private Long n30dImpMedian;    // 30天商单笔记曝光中位数
    private Long n30dInteractionMedian;    // 30天商单笔记互动中位数
    private Long n30dReadMedian;    // 30天商单笔记阅读中位数
    private Long n30dLikeMedian;    // 30天商单笔记点赞中位数
    private Long n30dCommentMedian;    // 30天商单笔记评论中位数
    private Long n30dCollectMedian;    // 30天商单笔记收藏中位数
    private Double n30dInteractionRate;    // 30天商单笔记互动率
    private Double n30dPlayOverRate;    // 30天商单笔记视频完播率


    private String updateTime;      // 更新时间


    private List<RedKolInfo.PaidPostInfo> postList;

    @Data
    public static class PaidPostInfo implements Serializable {

        private Boolean isPaid; // 是否商单
        private String postId;     // 内容ID
        private Long readCount;       // 阅读/播放数
        private String coverUrl;      // 封面URL
        private String title;         // 标题     实际为正文   tx_content
        private String url;           // 内容URL
        private Long duration;     // 视频时长（秒）
        private String publishTime;     // 发帖时间
        private Long repostsCount;      // 转发数
        private Long commentCount;    // 评论数
        private Long likeCount;        // 点赞数
        private Long collectCount;     // 收藏数
        private Long interaction; // 互动量    转发+评论+点赞+收藏

    }


}
