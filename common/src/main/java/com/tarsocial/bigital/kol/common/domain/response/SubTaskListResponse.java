package com.tarsocial.bigital.kol.common.domain.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SubTaskListResponse
 * @date 2024年06月05日
 */
@NoArgsConstructor
@Data
public class SubTaskListResponse {

    private Integer pageNum;
    private Integer pageSize;
    private Integer totalPage;
    private Integer total;
    private List<RowsDTO> rows;
    private Integer page;
    private Integer size;

    @NoArgsConstructor
    @Data
    public static class RowsDTO {
        private Long id;
        private Long subTaskId;
        private String callBackMsg;
        private Long taskId;
        private String detail;
        private String status;
        private Integer weight;
        private Integer downloadCount;
        private String createTime;
        private Integer errCode;
    }
}
