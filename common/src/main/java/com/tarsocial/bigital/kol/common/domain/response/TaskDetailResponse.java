package com.tarsocial.bigital.kol.common.domain.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TaskDetailResponse
 * @date 2024年05月31日
 */
@Data
public class TaskDetailResponse {

    private Long id;

    private Long mainTaskId;

    private Long esTaskId;

    private Long orgId;

    private String taskName;

    private String platformString;

    private Long platform;

    private String taskTypeString;

    private Long taskType;

    private Integer weight;

    private String status;

    private Boolean delFlag;

    private Long parentId;

    private Date createAt;

    private Date updateAt;

    private String addCondition;

    private String taskDetail;

    private List<String> taskDetailArray;

    private long finishNum;

    private long total;

    private BigDecimal finishRatio;

    private Double downloadDataCount;

    private long hasDownLoad;

    private long hasIntoEs;

    private String bizNo;

    private long failCount;

}
