package com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime;

import lombok.Data;

import java.util.List;

@Data
public class WeiboKolInfo {

    private String nickname;      // 用户昵称
    private String userId;           // 用户UID
    private Boolean isSettled;    // 是否入驻
    private Long followersCount;       // 粉丝数
    private List<String> userTags;           // 用户标签

    private Long pricePost;    //直发价格
    private Long priceForward;    //转发价格

    private Long priceVideo;    // 视频报价
    private Long priceImage;    // 图文报价
    private String updateTime;      // 更新时间


    private Long n30dReleaseMedian;    //近30天发布博文数
    private Long n30dReadMedian;    //近30天阅读中位数
    private Long n30dLikeMedian;    //    近30天点赞中位数
    private Long n30dCommentMedian;    // 近30天评论中位数
    private Long n30dRepostsMedian;    // 近30天转发中位数


    private Long n90dReleaseMedian;    //近90天发布博文数
    private Long n90dReadMedian;    //近90天阅读中位数
    private Long n90dLikeMedian;    //    近90天点赞中位数
    private Long n90dCommentMedian;    // 近90天评论中位数
    private Long n90dRepostsMedian;    // 近90天转发中位数

    private Double n90dCpm;     // 近90天直发CPM
    private Double n90dCpe;     // 近90天直发CPE
    private Double n90dRepostsCpm;     // 近90天转发CPM
    private Double n90dRepostsCpe;     // 近90天转发CPE


}
