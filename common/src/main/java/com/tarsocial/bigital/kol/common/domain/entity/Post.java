package com.tarsocial.bigital.kol.common.domain.entity;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class Post implements Serializable {

    @Schema(title = "内容ID")
    @JsonAlias("kw_id")
    private String id;

    @Schema(title = "平台")
    @JsonAlias("kw_platform")
    private String platform;

    @JsonAlias("kw_platformName")
    private String platformName;

    @Schema(title = "正文")
    @JsonAlias("tx_content")
    private String content;

    @Schema(title = "封面OCR")
    @JsonAlias("tx_ocrCover")
    private String ocrCover;

    @Schema(title = "域名")
    @JsonAlias("kw_domain")
    private String domain;

    @Schema(title = "标题")
    @JsonAlias("tx_title")
    private String title;

    @Schema(title = "内容类型")
    @JsonAlias("kw_contentType")
    private String contentType;

    @Schema(title = "发帖时间")
    @JsonAlias("date_publishedAt")
    private String datePublishedAt;

    @Schema(title = "互动数")
    @JsonAlias("long_interaction")
    private Long interaction;

    @Schema(title = "转发数")
    @JsonAlias("long_repostsCount")
    private Long repostsCount;

    @Schema(title = "评论数")
    @JsonAlias("long_commentsCount")
    private Long commentsCount;

    @Schema(title = "点赞数")
    @JsonAlias("long_likeCount")
    private Long likeCount;

    @Schema(title = "收藏数")
    @JsonAlias("long_collectCount")
    private Long collectCount;

    @Schema(title = "阅读/播放数")
    @JsonAlias("long_readCount")
    private Long readCount;

    @Schema(title = "链接")
    @JsonAlias("kw_url")
    private String url;

    @Schema(title = "视频时长（单位：秒）")
    @JsonAlias("long_duration")
    private Long duration;

    @Schema(title = "正文长度")
    @JsonAlias("long_contentLength")
    private Long contentLength;

    @Schema(title = "是否原创")
    @JsonAlias("bool_isOriginal")
    private Boolean isOriginal;

    @Schema(title = "提及话题列表")
    @JsonAlias("kw_hashtags")
    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    private List<String> hashtags;

    @Schema(title = "提及话题数量")
    @JsonAlias("long_hashtagsCount")
    private Long hashtagsCount;

    @JsonAlias("kw_picUrl")
    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    private List<String> picUrl;

    @Schema(title = "封面url")
    @JsonAlias("kw_coverUrl")
    private String coverUrl;

    @JsonAlias("object_user")
    private ObjectUser objectUser;

    @JsonAlias("object_origin")
    private ObjectOrigin objectOrigin;

    @Schema(title = "文本识别明细")
    @JsonAlias("object_ocrRowdata")
    private List<ObjectOcrRowData> objectOcrRowData;

    @Schema(title = "语音识别明细")
    @JsonAlias("object_asrRowdata")
    private List<ObjectAsrRowData> objectAsrRowData;

    @Schema(title = "是否商单")
    @JsonAlias("bool_isPaid")
    private Boolean isPaid;

    @Schema(title = "商单价格")
    @JsonAlias("long_paidPrice")
    private Long paidPrice;

    @Schema(title = "视频标签")
    @JsonAlias("kw_videoTag")
    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    private List<String> videoTag;

    @JsonAlias("bool_eCommerceEnable")
    private Boolean isECommerceEnable;

    @JsonAlias("object_promotions")
    private List<ObjectPromotions> objectPromotions;

    @JsonAlias("kw_brand")
    @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
    private List<String> brand;

    @Schema(title = "帖子类型（原创0,转发,评论,动态,弹幕,问题,回答,主帖,跟帖）")
    @JsonAlias("kw_postType")
    private String postType;

    @JsonAlias("object_emotionLabel")
    private ObjectEmotionLabel objectEmotionLabel;

    @Schema(title = "背景音乐")
    @JsonAlias("object_music")
    private ObjectMusic objectMusic;

    @Schema(title = "是否含有链接")
    @JsonAlias("bool_haslink")
    private Boolean haslink;

    @Schema(title = "商品url")
    @JsonAlias("kw_productUrl")
    private String productUrl;

    @Schema(title = "商品名称")
    @JsonAlias("tkw_productName")
    private String productName;

    @Schema(title = "商品id")
    @JsonAlias("kw_productId")
    private String productId;

    @Schema(title = "入库时间")
    @JsonAlias("date_createAt")
    private String createAt;

    @Schema(title = "更新时间")
    @JsonAlias("date_updateAt")
    private String updateAt;

    @Schema(title = "是否删除")
    @JsonAlias("bool_isDelete")
    private Boolean isDelete;

    @JsonAlias("long_coinCount")
    private Long coinCount;

    @JsonAlias("long_danmuCount")
    private Long danmuCount;

    @Data
    public static class ObjectMusic implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "音乐id")
        @JsonAlias("kw_id")
        private String id;
    }

    @Data
    public static class ObjectUser implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "用户昵称")
        @JsonAlias("tkw_nickname")
        private String nickname;

        @Schema(title = "用户UID")
        @JsonAlias("kw_userId")
        private String userId;

        @Schema(title = "用户平台号")
        @JsonAlias("kw_platformUserId")
        private String platformUserId;

        @Schema(title = "粉丝数")
        @JsonAlias("long_followersCount")
        private Long followersCount;

        @Schema(title = "用户生日")
        @JsonAlias("date_birthday")
        private String birthday;

        @Schema(title = "用户个人描述")
        @JsonAlias("tx_description")
        private String description;

        @Schema(title = "用户主页地址")
        @JsonAlias("kw_url")
        private String url;

        @Schema(title = "用户头像地址")
        @JsonAlias("kw_avatar")
        private String avatar;

        @Schema(title = "性别，m：男、f：女、n：未知")
        @JsonAlias("kw_gender")
        private String gender;

        @Schema(title = "是否认证")
        @JsonAlias("bool_verified")
        private Boolean verified;

        @Schema(title = "认证原因")
        @JsonAlias("tkw_verifiedReason")
        private String verifiedReason;
    }

    @Data
    public static class ObjectPromotions implements Serializable {

        private static final long serialVersionUID = 1L;

        @JsonAlias("kw_promotionId")
        private String promotionId;

        @Schema(title = "购物车标题")
        @JsonAlias("tx_title")
        private String title;

        @JsonAlias("double_price")
        private Double price;

        @JsonAlias("kw_url")
        private String url;

        @JsonAlias("kw_imgsUrl")
        private List<String> imgsUrl;

        @JsonAlias("bool_promotionsPrice")
        private Boolean promotionsPrice;
    }

    @Schema(title = "原帖")
    @Data
    public static class ObjectOrigin implements Serializable {

        private static final long serialVersionUID = -21328915040840523L;

        @Schema(title = "原帖用户")
        @JsonAlias("object_user")
        private ObjectUser objectUser;

        @Schema(title = "正文")
        @JsonAlias("tx_content")
        private String content;

        @Schema(title = "正文类型")
        @JsonAlias("kw_contentType")
        private String contentType;

        @Schema(title = "文章URL")
        @JsonAlias("kw_newsUrl")
        private String url;

        @Schema(title = "图片URL")
        @JsonAlias("kw_picUrl")
        @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
        private List<String> picUrl;

        @Schema(title = "发帖时间")
        @JsonAlias("date_publishedAt")
        private String datePublishedAt;

        @Schema(title = "互动量")
        @JsonAlias("long_interaction")
        private Long interaction;

    }

    @Data
    public static class ObjectOcrRowData implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "ocr内容")
        @JsonAlias("tx_text")
        private String text;

        @Schema(title = "出现时间")
        @JsonAlias("long_time")
        private Long time;
    }

    @Data
    public static class ObjectAsrRowData implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "语音文本")
        @JsonAlias("tx_text")
        private String text;

        @Schema(title = "语音开始秒数")
        @JsonAlias("long_startTime")
        private Long startTime;

        @Schema(title = "语音结束秒数")
        @JsonAlias("long_endTime")
        private Long endTime;
    }

    @Data
    public static class ObjectEmotionLabel implements Serializable {

        private static final long serialVersionUID = 181712153803529604L;

        @Schema(title = "情感")
        @JsonAlias("kw_emotion")
        private String emotion;

        /**
         * tkw: 可搜索, 可聚合
         */
        @Schema(title = "情感置信度")
        @JsonAlias("double_probability")
        private Double probability;

    }

}
