package com.tarsocial.bigital.kol.common.domain.request.hair;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @title HairMonitorKolTaskRequest
 * @date 2025/1/2 16:20
 * @description 「HairMonitorKolTaskRequest」
 */
@Data
public class HairMonitorKolTaskRequest {

    @NotBlank(message = "kolUrl不能为空")
    private String userUrl;

    @NotBlank(message = "platform不能为空")
    private String platform;

    /**
     * 当前字段是默认字段 不对外展示
     */
    private Long campaignId = 2L;

}
