package com.tarsocial.bigital.kol.common.domain.request;

import com.tarsocial.bigital.kol.common.domain.dto.EntPipelineDto;
import com.tarsocial.bigital.kol.common.domain.dto.RelPipelineDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class NerFlexibleCalculateRequest implements Serializable {

    private EntPipelineDto entModelPipeline;

    private RelPipelineDto relModelPipeline;

    /**
     * 整贴情感
     */
    private Boolean sentiment;

    /**
     * 标准实体
     */
    private Boolean standard = false;

    //评论
    private Boolean comment = false;

    //查询24年之前的数据
    private Boolean ago = false;

    private List<String> columns;
}
