package com.tarsocial.bigital.kol.common.domain.request.hair;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @title GetPlatformPriceTaskDataResponse
 * @date 2025/1/6 14:51
 * @description 「GetPlatformPriceTaskDataResponse」
 */
@Data
public class GetPlatformPriceTaskDataResponse {

    private String taskId;

    private String status;

    private String errorMsg;

    private Date updateTime;

    private String user_id;

    private String kolName;

    private String platformUserId;
    /**
     * 抓取时间
     */
    private String crawlTime;

    /**
     * 图文价格
     */
    private Double priceImageArticle;

    private Double price1_20;
    private Double price20_60;
    private Double price60;
    /**
     * 视频任务价格
     */
    private Double priceVideoTask;
    /**
     * 共创参与价格
     */
    private Double priceCooperateSlave;

    /**
     * 共创发布价格
     */
    private Double priceCooperateMaste;

    /**
     * 直发价格
     */
    private Double pricePost;
    /**
     * 转发价格
     */
    private Double priceForward;
    /**
     * 定制视频价格。
     */
    private Double priceCustomer;
    /**
     * 视频植入价格
     */
    private Double priceEmbedding;

    private String priceInfo;
    /**
     * 微信价格信息
     */
    private String wxPriceInfo;
    /**
     * 视频号价格信息
     */
    private String sphPriceInfo;
    private Double price10;
    private Double price1_3;
    private Double price3_5;
    private Double price5_10;
    private Double priceWrite;
    private Double pricePicter;
}
