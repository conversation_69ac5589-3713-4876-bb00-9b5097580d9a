package com.tarsocial.bigital.kol.common.domain.dto.tencent;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @ClassName TencentPostExcel
 * @date 2024年05月21日
 */
@NoArgsConstructor
@Data
public class TencentPostExcel {

    @ExcelProperty(value = "数据范围", index = 0)
    private String dataRange;
    @ExcelProperty(value = "发帖时间", index = 1)
    private String publishTime;
    @ExcelProperty(value = "平台", index = 2)
    private String platform;
    @ExcelProperty(value = "标题", index = 3)
    private String title;
    @ExcelProperty(value = "阅读/播放数", index = 4)
    private Long playCount;
    @ExcelProperty(value = "点赞数", index = 5)
    private Long likeCount;
    @ExcelProperty(value = "评论数", index = 6)
    private Long commentCount;
    @ExcelProperty(value = "转发数", index = 7)
    private Long forwardCount;
    @ExcelProperty(value = "收藏数", index = 8)
    private Long favorateCount;
    @ExcelProperty(value = "视频链接", index = 9)
    private String url;
    @ExcelProperty(value = "内容ID", index = 10)
    private String contentId;
    @ExcelProperty(value = "视频时长(秒)", index = 11)
    private Long videoLength;
    @ExcelProperty(value = "正文长度", index = 12)
    private Long wordCount;
    @ExcelProperty(value = "是否原创", index = 13)
    private Boolean isOriginal;
    @ExcelProperty(value = "封面url", index = 14)
    private String coverUrl;
    @ExcelProperty(value = "封面OCR", index = 15)
    private String coverOCR;
    @ExcelProperty(value = "提及话题", index = 16)
    private String topic;
    @ExcelProperty(value = "提及话题数量", index = 17)
    private Long topicCount;
    @ExcelProperty(value = "内容类型", index = 18)
    private String contentType;
    @ExcelProperty(value = "是否商单", index = 19)
    private Boolean isCommercialOrder;
    @ExcelProperty(value = "商单价格", index = 20)
    private Long orderPrice;
    @ExcelProperty(value = "视频标签", index = 21)
    private String videoTag;
    @ExcelProperty(value = "音乐id", index = 22)
    private String musicId;
    @ExcelProperty(value = "购物车链接", index = 23)
    private Boolean hasProduct;
    @ExcelProperty(value = "商品信息", index = 24)
    private String product;
    @ExcelProperty(value = "入仟传的库的时间", index = 25)
    private String createTime;
    @ExcelProperty(value = "更新时间", index = 26)
    private String updateTime;
    @ExcelProperty(value = "用户昵称", index = 27)
    private String userName;
    @ExcelProperty(value = "用户UID", index = 28)
    private String userUID;
    @ExcelProperty(value = "用户平台号", index = 29)
    private String userAccount;
    @ExcelProperty(value = "粉丝数", index = 30)
    private Long followerCount;
    @ExcelProperty(value = "用户生日", index = 31)
    private String userBirthday;
    @ExcelProperty(value = "用户个人描述", index = 32)
    private String userDescription;
    @ExcelProperty(value = "用户主页地址", index = 33)
    private String userHomepageUrl;
    @ExcelProperty(value = "用户头像地址", index = 34)
    private String userAvatarUrl;
    @ExcelProperty(value = "性别", index = 35)
    private String gender;
    @ExcelProperty(value = "认证原因", index = 36)
    private String verifiedName;
    @ExcelProperty(value = "是否删除", index = 37)
    private Boolean isDelete;


}
