package com.tarsocial.bigital.kol.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ner_flexible")
public class NerFlexibleEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String fileName;
    private String downloadUrl;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String modelType;
    private String modelSchema;
    private Boolean sentiment;

    private Boolean standard;
    private Boolean comment;
    private Boolean ago;
    private String columns;

    private String taskStatus;

}
