package com.tarsocial.bigital.kol.common.domain.response.ieg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StarUserKsResponse {

    private Long user_id;
    private Object kwai_id;
    private String nickname;
    private Boolean is_star;
    private Integer photo_expect_click;
    private Integer photo_expect_cpm;
    private Integer photo_Week_release;
    private Double accept_order_rate;
    private Integer complete_order_rRate;
    private Integer complete_order_cnt;
    private List<String> advertiser_industries;
    private String address;
    private String mcn_name;
    private String tags;
    private String industry_tags;
    private List<PercentageDTO> sex_percentage;
    private List<PercentageDTO> age_percentage;
    private List<PercentageDTO> mobileBrand_Percentage;
    private List<PercentageDTO> activity_percentage;
    private List<PercentageDTO> city_percentage;
    private List<PercentageDTO> area_percentage;
    private List<PercentageDTO> fans_trend;
    private Integer order_bid;
    private Double pcomplete_play_rateData;
    private Double binteraction_RateData;
    private Integer play_mid;
    private String update_time;
    private PriceInfoDTO price_info;
    private PortraitDTO viewer_portrait;
    private PortraitDTO fans_portrait;
    private String gender;

    @NoArgsConstructor
    @Data
    public static class PercentageDTO {
        private String label;
        private Integer tgi;
        private Double value;
    }

    @NoArgsConstructor
    @Data
    public static class PriceInfoDTO {
        private Integer s1Price;
        private Integer s20Price;
        private Integer s60Price;
        private Integer liveHourPrice;
        private Integer liveDayPrice;
        private Integer liveProductPrice;
    }

    @NoArgsConstructor
    @Data
    public static class PortraitDTO {
        private List<PercentageDTO> sex;
        private List<PercentageDTO> age;
        private List<PercentageDTO> mobile_brand;
        private List<PercentageDTO> mobile_price;
        private List<PercentageDTO> activity;
        private List<PercentageDTO> city_level;
        private List<PercentageDTO> interest;
        private List<PercentageDTO> city;
        private List<PercentageDTO> area;
        private List<PercentageDTO> nine_major_groups;
        private List<PercentageDTO> area_top_with_permeation;
        private List<PercentageDTO> area_top_with_radio;

    }

}
