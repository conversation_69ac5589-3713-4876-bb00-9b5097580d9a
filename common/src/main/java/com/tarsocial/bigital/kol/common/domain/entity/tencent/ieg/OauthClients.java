package com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "oauth_clients")
public class OauthClients implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String clientId;

    private String clientSecret;

    private String redirectUri;

    private String grantTypes;

    private String scope;

    private String userId;

    private Date insertTime;

    private String remark;

    private Integer status;
}
