package com.tarsocial.bigital.kol.common.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PostSerachRequest implements Serializable {

    @ApiModelProperty(value = "日期类型 day/week/month", required = true)
    @NotEmpty(message = "dateType is not null")
    private String dateType;

    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "startDate is not null")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "endDate is not null")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "平台")
    private List<Long> platformIdsList;

    @ApiModelProperty(value = "品类")
    private Long categoryRuleId;

    @ApiModelProperty(value = "品牌")
    private List<Long> brandRuleIds;

    @ApiModelProperty(value = "内容类型 PGC UGC")
    private List<Long> contentTypeRuleIds;

    @ApiModelProperty(value = "情感 正 负 中")
    private List<String> emotions;

    @ApiModelProperty(value = "评论 是否")
    private Boolean commentType;

    private Integer size = 1000;

    @ApiModelProperty(value = "排序字段")
    private String orderField = "long_interaction";


    @ApiModelProperty(value = "话题")
    private String topic ;

    //@ApiModelProperty(value = "排序规则，升序：ASC;降序：DESC")
    //private String sortDirection = "DESC";

    private Long angleRuleId;

    @ApiModelProperty(value = "Asc  / Desc")
    private String order = "Desc";

    @ApiModelProperty(value = "声量")
    private List<Long> volumeTypeRuleIds;

    @ApiModelProperty(value = "词云词")
    private String word;
}
