package com.tarsocial.bigital.kol.common.domain.dto.mengniu;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class TodayResultDto implements Serializable {

    /**
     * 热点名称 - 即词条名
     */
    private String hotName;

    /**
     * 热点发生时间
     */
    private String hotTime;

    /**
     * 热度值
     */
    private Double hotValue;

    /**
     * 热搜指数
     */
    private Double hotSearchIndex;

    /**
     * 声量指数
     */
    private Double buzzIndex;

    /**
     * 互动量指数
     */
    private Double interactionIndex;

    /**
     * 热点描述
     */
    private String hotDescription;

    /**
     * 相关热搜
     */
    private List<String> relatedHot;

    /**
     * 相关帖子
     */
    private List<RelatedPostInfo> relatedPosts;

    /**
     * 覆盖地域
     */
    private List<String> regions;

    /**
     * 关注人群
     */
    private Map<String, Object> crowd;

    /**
     * 热点来源平台 - 即词条来源平台
     */
    private List<String> platforms;

    /**
     * 热点声量分布 - 即词条来源平台的占比为100%
     */
    private Map<String, Double> buzzRatio;


    @Data
    public static class RelatedPostInfo implements Serializable {

        private String title;

        private String content;

    }

}
