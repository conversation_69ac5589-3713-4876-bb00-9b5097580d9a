package com.tarsocial.bigital.kol.common.domain.response;

/**
 * <AUTHOR>
 */

public enum ResponseCode implements BaseResponseCode {
    REFUSED(-1, "REFUSED"),
    SUCCESS(0, "成功"),
    ERROR(1, "错误"),
    FOUND(302, "REDIRECT"),
    BAD_REQUEST(400, "错误请求"),
    UNAUTHORIZED(401, "未经授权，缺少或错误的认证", "未经授权，缺少或错误的认证"),
    FORBIDDEN(403, "当用户被认证后，但用户没有被授权在特定资源上执行操作。", "当用户被认证后，但用户没有被授权在特定资源上执行操作。"),
    NOT_FOUND(404, "未找到"),
    METHOD_NOT_ALLOWED(405, "方法不允许"),
    UNSUPPORTED_MEDIA_TYPE(415, "不支持的媒体类型"),
    INTERNAL_SERVER_ERROR(500, "内部服务器错误"),
    TIME_OUT(502, "超时"),
    APP_ID_EMPTY(11001, "app_id 为空"),
    APP_ID_INVALID(11002, "app_id 无效"),
    APP_SECRET_INVALID(11003, "app_secret 无效"),
    TIMESTAMP_INVALID(11004, "时间戳为空或无效"),
    ACCESS_TOKEN_INVALID(11005, "访问令牌为空或无效"),
    NONCE_INVALID(11006, "nonce 为空或无效"),
    SIGNATURE_ERROR(11007, "签名无效"),
    AUTH_NO_ENTRIES(40111, "授权成功，但当前系统不允许你访问"),
    TOKEN_INVALID(40121, "抱歉，您的请求无效"),
    TOKEN_EXPIRED(40122, "抱歉，您的访问令牌已过期，请重新登录"),
    TOKEN_INFO_WRONG(40123, "抱歉，您的访问令牌有误"),
    TOKEN_EMPTY(40128, "抱歉，您的访问令牌为空"),
    DuplicateLogin(40129, "该账号已在其他设备登录，您已被强制下线"),
    EMAIL_CODE_ERROR(50000, "验证码错误"),
    EMAIL_CODE_EXPIRES(50001, "验证码过期"),
    EMAIL_CODE_NOT_EMPTY(50002, "验证码不能为空"),
    EMAIL_CORRECT_FORMAT(60000, "邮箱格式不正确"),
    EMAIL_NOT_EMPTY(60001, "邮箱不能为空"),
    USER_NOT_FOUND(70000, "用户不存在"),
    USER_DISABLED(70001, "用户已被禁用"),
    USER_EXPIRED(70002, "用户已过期"),
    USER_DELETED(70003, "用户已被删除"),
    USER_AUTHORITY_NOT_FOUND(70004, "用户权限 未找到"),
    USER_ROLE_NOT_FOUND(70005, "用户角色 未找到"),
    USER_LOCKED(70006, "用户已被锁定"),
    USER_EMAIL_NOT_EXIST_OR_CODE_INCORRECT(70007, "邮箱不存在或验证码错误"),
    USER_ACCOUNT_NOT_EXIST_OR_PASSWORD_INCORRECT(70008, "账号不存在或密码错误"),
    TENANT_NOT_FOUND(80000, "租户未找到"),
    TENANT_DISABLED(80001, "租户已被禁用"),
    TENANT_EXPIRED(80002, "租户已过期"),
    TENANT_DELETED(80003, "租户已被删除"),
    TENANT_USER_AUTHORITY_NOT_FOUND(90000, "租户 用户权限 未找到"),
    TENANT_USER_ROLE_NOT_FOUND(90001, "租户 用户角色 未找到"),
    TENANT_USER_NOT_FOUND(90002, "租户成员未找到"),
    PWD_FORMAT_ERROR(90000, "密码格式有误，正确格式为：base64(rsa(原文密码))"),

    PARAM_ERROR(110000, "参数错误"),

    VALIDATE_ERROR(110002, "参数错误"),
    DATABASE_EXCEPTION(110003, "数据库异常");

    private int code;
    private String message;
    private String desc;

    ResponseCode(int code, String message, String desc) {
        this.code = code;
        this.message = message;
        this.desc = desc;
    }
    ResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessageByCode(int code) {
        ResponseCode[] values = ResponseCode.values();
        for (ResponseCode status : values) {
            if (status.getCode() == code) {
                return status.getMessage();
            }
        }
        return "未知状态";
    }



    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getDesc() {
        return desc;
    }


}
