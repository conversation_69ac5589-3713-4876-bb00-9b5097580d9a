package com.tarsocial.bigital.kol.common.domain.request.hair;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title HairMonitorResponseV2
 * @date 2025年01月14日13:55:59
 * @description 「HairMonitorResponseV2」
 */
@Data
public class HairMonitorResponseV2 {

    private String status;
    /**
     * 帖子id
     */
    private String postId;
    /**
     * 平台
     */
    private String platform;

    /**
     * 作品发布时间
     */
    private String publishedTime;
    /**
     * 正文
     */
    private String content;

    private String url;

    private List<String> topicTags;

    private HairMonitorBaseMetric baseMetric;

    private Map<String, List<HairMonitorTrendMetric>> trendMetric;


}
