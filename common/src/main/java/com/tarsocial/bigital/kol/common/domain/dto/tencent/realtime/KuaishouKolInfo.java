package com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class KuaishouKolInfo {

    private String nickname;      // 用户昵称
    private String platformUserId;                 // 抖音号
    private String userId;           // 用户UID
    private Boolean isSettled;    // 是否入驻
    private Long followersCount;       // 粉丝数
    private String mcnName;                 // MCN名称
    private List<String> userTags;           // 用户标签
    private List<String> industryTags;       // 行业标签
    private Long price1To20s;         // 1-20s报价
    private Long price21To60s;        // 21-60s报价
    private Long price60sPlus;        // 60s以上报价
    private Long expectedPlayNum;   //预期播放量
    private Double cpm;    //预期CPM


    private Long playMedian;    // 播放量中位数
    private Double interactRate;    // 互动率
    private Double playOverRate;    // 完播率


    private Double n30dLikeAvg;    // 聚星30天平均点赞
    private Double n30dCommentAvg;    // 聚星30天平均评论
    private Double n30dShareAvg;    // 聚星30天平均分享
    private Double n90dLikeAvg;    // 聚星90天平均点赞
    private Double n90dCommentAvg;    // 聚星90天平均评论
    private Double n90dShareAvg;    // 聚星90天平均分享

    private String updateTime;      // 更新时间


    private List<KuaishouKolInfo.PaidPostInfo> postList;

    @Data
    public static class PaidPostInfo implements Serializable {

        private Boolean isPaid; // 是否商单
        private String postId;     // 内容ID
        private Long readCount;       // 阅读/播放数
        private String coverUrl;      // 封面URL
        private String title;         // 标题     实际为正文   tx_content
        private String url;           // 内容URL
        private Long duration;     // 视频时长（秒）
        private String publishTime;     // 发帖时间
        private Long repostsCount;      // 转发数
        private Long commentCount;    // 评论数
        private Long likeCount;        // 点赞数
        private Long collectCount;     // 收藏数
        private Long interaction; // 互动量    转发+评论+点赞+收藏

    }


}
