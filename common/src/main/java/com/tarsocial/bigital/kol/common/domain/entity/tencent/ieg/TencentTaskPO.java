package com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tencent_task")
public class TencentTaskPO implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /** 用户ID */
    private String userId;

    /** 任务状态：进行中（默认）/成功/查无此人/超时 */
    private String taskStatus;

    /** 待创建后置任务userId List */
    private String postPosition;

    /** 平台 */
    private String platform;

    /** 创建时间（数据库自动生成） */
    private Date createdTime;

    /** 业务类型 */
    private String businessType;

    /** 腾讯查询批次号 */
    private String tencentBatchId;
}
