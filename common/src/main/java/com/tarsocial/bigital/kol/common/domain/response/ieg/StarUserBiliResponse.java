package com.tarsocial.bigital.kol.common.domain.response.ieg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StarUserBiliResponse {

    private String user_id;
    private String nickname;
    private Boolean is_star;
    private Integer partition_id;
    private String second_partition_id;
    private String partition_name;
    private String second_partition_name;
    private String region_desc;
    private List<String> tags;
    private Double interactive_rate;
    private Object magnetic_value;
    private Integer is_core_agent;
    private Object active_status;
    private Object synthetical_score;
    private Integer average_barrage_cnt;
    private Integer average_collect_cnt;
    private Integer average_comment_cnt;
    private Integer average_like_cnt;
    private Integer avg_play;
    private List<String> star_tags;
    private List<FansSexDistributionsDTO> fans_sex_distributions;
    private List<FansSexDistributionsDTO> fans_age_distributions;
    private List<FansSexDistributionsDTO> fans_top_region_distributions;
    private List<FansSexDistributionsDTO> fans_device_distributions;
    private Integer quoted_price_one;
    private Integer quoted_price_two;
    private Integer quoted_price_three;
    private Integer quoted_price_four;
    private Object fans_count_distributions;
    private List<NotesListDTO> notes_list;
    private String update_time;
    private List<FansSexDistributionsDTO> fans_sex_distributions_audience;
    private List<FansSexDistributionsDTO> fans_age_distributions_audience;
    private List<FansSexDistributionsDTO> fans_view_proportion;
    private List<FansSexDistributionsDTO> fans_device_distributions_audience;
    private List<FansSexDistributionsDTO> fans_top_region_distributions_audience;
    private List<String> first_follow_partition_audience;
    private List<String> second_follow_partition_audience;
    private List<FansSexDistributionsDTO> new_follow_tags_audience;
    private List<FansSexDistributionsDTO> fans_city_distributions;
    private List<String> fans_first_follow_partition;
    private List<String> fans_second_follow_partition;
    private List<FansSexDistributionsDTO> new_fans_follow_tags;
    private String gender;

    @NoArgsConstructor
    @Data
    public static class FansSexDistributionsDTO {
        private Double count;
        private String section_desc;
    }

    @NoArgsConstructor
    @Data
    public static class NotesListDTO {
        private String bvid;
        private Long comment_cnt;
        private String cover;
        private Integer duration;
        private Long like_cnt;
        private Long play_cnt;
        private Long post_id;
        private Long pub_time;
        private String title;
        private Integer type;
        private String type_desc;
        private Long user_id;
    }
}
