package com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tencent_quark_task")
public class TencentQuarkTask implements Serializable {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String kolId;

    private String userId;

    private String platform;

    //tencent_task表主键
    private Long tencentTaskId;

    //夸克任务类型
    private String quarkTaskType;

    private Long quarkTaskId;

    private String quarkTaskStatus;

    private String quarkSubTaskId;

    private String quarkErrorDetail;

    private String quarkTaskName;

    //业务创建的任务类型
    private String businessTaskType;

    //主业务类型：区分 腾讯实时，IEG  用来给业务统计收费
    private String businessType;

    private Date createdTime;

    private Date updateTime;
}
