package com.tarsocial.bigital.kol.common.domain.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 * @title HairBaseResponse
 * @date 2025/1/16 14:56
 * @description 「HairBaseResponse」
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HairBaseResponse<T> {

    private static final int CODE_ERROR = -1;

    private int errCode;
    private String errMsg;
    private String traceId;
    private T data;

    public HairBaseResponse(T data) {
        this.errCode = 0;
        this.errMsg = "success";
        this.traceId = MDC.get("traceId");
        this.data = data;
    }

    public HairBaseResponse(Integer code, String message) {
        this.errCode = code;
        this.errMsg = message;
        this.traceId = MDC.get("traceId");
        this.data = null;
    }


    public boolean success() {
        return this.errCode == 200;
    }

    public static <T> HairBaseResponse<T> error(String message) {
        return new HairBaseResponse<>(CODE_ERROR, message);
    }
}
