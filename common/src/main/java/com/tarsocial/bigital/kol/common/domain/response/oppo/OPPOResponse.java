package com.tarsocial.bigital.kol.common.domain.response.oppo;

import lombok.Data;
import org.slf4j.MDC;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OPPOResponse<T> {

    private Integer size;

    private Integer errcode;

    private String traceId;

    private String errmsg;

    private String batchNo;

    private Boolean hasMore = false;

    private List<T> data;

    public OPPOResponse(List<T> data, String batchNo, Boolean hasMore) {
        this.errcode = 0;
        this.errmsg = "success";
        this.traceId = MDC.get("traceId");
        this.data = data;
        this.size = data.size();
        this.hasMore = hasMore;
        this.batchNo = batchNo;
    }

    public OPPOResponse(Integer code, String message) {
        this.errcode = code;
        this.errmsg = message;
        this.traceId = MDC.get("traceId");
        this.data = null;
    }
}
