package com.tarsocial.bigital.kol.common.domain.request.tencent.ieg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StarUserRequest {

    private String user_ids;

    private String token;

    private Integer platform;

    private List<String> userIdList;

    public void setUserIdList(List<String> userIdList) {
        if (Objects.isNull(userIdList) && StringUtils.hasLength(this.user_ids)) {
            String[] split = this.user_ids.split(",");
            this.userIdList = Arrays.asList(split);
        } else {
            this.userIdList = userIdList;
        }
    }

}
