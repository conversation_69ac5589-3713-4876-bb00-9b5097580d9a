package com.tarsocial.bigital.kol.common.domain.entity.guangnian;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class GamePostVo {

    /**
     * 命中关键词
     */
    @ExcelProperty(value = "命中关键词", index = 0)
    private String keyword;

    /**
     * 发布时间
     */
    @ExcelProperty(value = "平台类别", index = 1)
    private String platform;

    /**
     * 发帖时间
     */
    @ExcelProperty(value = "发帖时间", index = 2)
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date publishedAt;

    /**
     * 作者
     */
    @ExcelProperty(value = "作者", index = 3)
    private String authorName;

    /**
     * 正文
     */
    @ExcelProperty(value = "正文", index = 4)
    private String content;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题", index = 5)
    private String title;

    /**
     * 链接
     */
    @ExcelProperty(value = "链接", index = 6)
    private String url;

    /**
     * 原贴内容
     */
    @ExcelProperty(value = "原贴内容", index = 7)
    private String originContent;

    /**
     * 倾向性
     */
    @ExcelProperty(value = "倾向性", index = 8)
    private String emotion;

    /**
     * 内容类型
     */
    @ExcelProperty(value = "内容类型", index = 9)
    private String type;

    /**
     * 粉丝数
     */
    @ExcelProperty(value = "粉丝数", index = 10)
    private Long fans;

    private String ocrCover;
}
