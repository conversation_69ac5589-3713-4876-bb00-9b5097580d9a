-- 为 kol_data 表添加情感分析字段
-- 执行前请确认表名和字段名是否正确

-- 添加主贴情感字段
ALTER TABLE kol_data ADD COLUMN IF NOT EXISTS main_post_sentiment VARCHAR(50) COMMENT '主贴对汽车品牌的情感倾向：正面/中性/负面';

-- 添加评论情感字段
ALTER TABLE kol_data ADD COLUMN IF NOT EXISTS comment_sentiment VARCHAR(50) COMMENT '评论对汽车品牌的情感倾向：正面/中性/负面';

-- 添加感受词字段
ALTER TABLE kol_data ADD COLUMN IF NOT EXISTS feeling_words JSON COMMENT '用户对汽车品牌的感受词，JSON数组格式';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_kol_data_sentiment ON kol_data(main_post_sentiment, comment_sentiment);

-- 查看表结构确认字段已添加
-- SELECT column_name, data_type, is_nullable, column_default, column_comment
-- FROM information_schema.columns
-- WHERE table_name = 'kol_data'
-- AND column_name IN ('main_post_sentiment', 'comment_sentiment');
