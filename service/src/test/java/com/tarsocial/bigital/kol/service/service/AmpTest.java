package com.tarsocial.bigital.kol.service.service;

import com.tarsocial.bigital.kol.common.domain.request.MinMaxRequest;
import com.tarsocial.bigital.kol.common.domain.request.amp.AMPFilterRequest;
import com.tarsocial.bigital.kol.common.domain.request.amp.AMPKeywordRequest;
import com.tarsocial.bigital.kol.common.domain.request.amp.AMPKolTypeRequest;
import com.tarsocial.bigital.kol.service.TransmissionServiceApplication;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.testng.collections.Lists;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = TransmissionServiceApplication.class)
public class AmpTest {


    @Resource
    private AmpService ampService;


    @Test
    void ampTest() {
        AMPKolTypeRequest t1 = new AMPKolTypeRequest();
        t1.setFirst("颜值达人");
        t1.setSecond(Lists.newArrayList( "美女"));
        AMPKolTypeRequest t2 = new AMPKolTypeRequest();
        t2.setFirst("生活家居");
        AMPFilterRequest req = new AMPFilterRequest();
        req.setPlatform("douyin");
        req.setKolType(Lists.newArrayList(t1, t2));
        req.setStartDate("2024-06-01");
        req.setEndDate("2024-12-30");

        MinMaxRequest minMaxRequest1 = new MinMaxRequest();
        minMaxRequest1.setMin(100L);
        minMaxRequest1.setMax(200L);

        MinMaxRequest minMaxRequest2 = new MinMaxRequest();
        minMaxRequest2.setMin(100000L);
//        minMaxRequest2.setMax(L);

        req.setKolTier(Lists.newArrayList(minMaxRequest1,minMaxRequest2));

        AMPKeywordRequest keywordRequest = new AMPKeywordRequest();
        List<String> in = Lists.newArrayList("反差");
        List<List<String>> list = new ArrayList<>();
        list.add(in);
        keywordRequest.setInclude(list);
        keywordRequest.setExclude(Lists.newArrayList("修车"));
        req.setKeyword(keywordRequest);
        ampService.kolList(req);
    }


}
