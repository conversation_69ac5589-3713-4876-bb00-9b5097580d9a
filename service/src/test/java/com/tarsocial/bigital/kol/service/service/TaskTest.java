package com.tarsocial.bigital.kol.service.service;
import java.sql.SQLException;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;

import com.tarsocial.bigital.kol.common.domain.entity.AccessInterfaceLog;
import com.tarsocial.bigital.kol.common.domain.entity.QuarkUserInfo;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.IegTaskCreateRequest;
import com.tarsocial.bigital.kol.common.domain.response.ieg.LiveTaskCreateResponse;
import com.tarsocial.bigital.kol.common.domain.response.ieg.TaskUserDetailResponse;
import com.tarsocial.bigital.kol.service.TransmissionServiceApplication;
import com.tarsocial.bigital.kol.service.mapper.AccessInterfaceLogMapper;
import com.tarsocial.bigital.kol.service.service.ieg.IegProcessService;
import com.tarsocial.config.TokenService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@SpringBootTest(classes = TransmissionServiceApplication.class)
public class TaskTest {


    @Resource
    private IegProcessService iegProcessService;

    @Resource
    private HotTopicJobService minioService;

    @Resource
    private MengniuHotTopService mengNiuHotTopService;

    @Resource
    private AlignEntityService alignEntityService;


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Test
    void alignEntityServiceTest() {
//        alignEntityService.getAllEntityMapping("beauty");
        System.out.println(1);
    }

    @Test
    void minioServiceTest222() {
//        Object o = redisTemplate.opsForValue().get("1733922271487_6629c0b80000000001032965");
//        redisTemplate.delete("1733922271487_6629c0b80000000001032965")
        //1748514196771
        deleteKeysMatchingPattern("1748507865873*");
    }

    /**
     * 批量删除redis Key
     * @param pattern
     */
    public void deleteKeysMatchingPattern(String pattern) {
        String cursor = "0";  // 初始化游标

        do {
            // 使用 SCAN 命令扫描所有键
            Set<String> keys = redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
                Set<String> result = new HashSet<>();
                // 使用 ScanOptions.match() 来设置匹配模式
                ScanOptions scanOptions = ScanOptions.scanOptions().match(pattern).build();

                // 执行 SCAN 命令
                Cursor<byte[]> cursorData = connection.scan(scanOptions);

                // 将返回的字节数组转换为 String 并添加到结果集中
                cursorData.forEachRemaining(byteArray -> result.add(new String(byteArray)));
                return result;
            });

            // 如果找到匹配的键，删除它们
            if (!CollectionUtils.isEmpty(keys)) {
                for (String key : keys) {
                    redisTemplate.delete(key);
                    System.out.println("Deleted key: " + key);
                }
            }

            // 更新游标，继续扫描下一个批次
            cursor = redisTemplate.execute((RedisCallback<String>) connection -> {
                Cursor<byte[]> cursorData = connection.scan(ScanOptions.scanOptions().match(pattern).build());
                return new String(cursorData.getCursorId() + "");
            });

        } while (!"0".equals(cursor)); // 当游标返回到 "0" 时，扫描结束
    }

    @Resource
    private AccessInterfaceLogMapper accessInterfaceLogMapper;

    @Resource
    private TokenService tokenService;

    @Test
    void AccessInterfaceLogMapperTest() {
        AccessInterfaceLog log = new AccessInterfaceLog();
        log.setPath("111");
        log.setUrl("1111");
        log.setParams("11111");
        log.setSourceIp("111111");
        log.setTimes(22L);
        log.setAccessTime(new Date());
        log.setCreateTime(new Date());
        log.setStatusType("11");

        accessInterfaceLogMapper.insert(log);
    }
    @Test
    void minioServiceTest() throws SQLException {
//        mengNiuHotTopService.performance("20241118");

//        minioService.monitor();
//        minioService.executeTask(128L, "HotTopicPost_20250306_7c1a08a8-2249-4cf5-8e1a-2320aa09057b_cluster.jsonl","v2_20250306");
//        minioService.executeTask(128L, "蒙牛离线交数-0307-2_删三大类-2.jsonl","v2_20250306");
        minioService.executeTask(171L, "HotTopicPost_V2_20250328_cluster.jsonl","v2_20250328");
//        mengNiuHotTopService.saveFinalResult("v2_20250312");

//        try {
//            minioService.executeTask(1L, "","");
//        } catch (SQLException e) {
//            e.printStackTrace();
//        }
    }


    @Test
    void taskRetry() {
        iegProcessService.publicPriceTaskUpdate();
//        iegProcessService.taskRetry();
    }

    @Test
    void test1() {

        IegTaskCreateRequest req = new IegTaskCreateRequest();
        req.setIds(Lists.newArrayList("2616442883152379", "3830324264963371", "1029569197389603", "377852523187374", "108912642669", "76115138206", "1808014629675101", "69607380350", "100014134659", "2652493335240272", "50085884024", "3837750497777960", "1306661995414887", "235714847972219", "4286328952067680", "2586473455691661", "1028738383625696", "1251713547768308", "96460635937", "2946305205752208"));
        req.setPlatform("douyin");
//        req.setToken("");
        TaskUserDetailResponse taskUserDetailResponse = iegProcessService.taskDetail(req);
        Map<String, List<TaskUserDetailResponse.UserStatusDetail>> collect = taskUserDetailResponse.getDetail().stream().collect(Collectors.groupingBy(TaskUserDetailResponse.UserStatusDetail::getStatus));
        System.out.println(JSON.toJSONString(taskUserDetailResponse));
    }

    @Test
    void test2() {
        IegTaskCreateRequest iegTaskCreateRequest = new IegTaskCreateRequest();
        iegTaskCreateRequest.setPlatform("kuaishou");
        iegTaskCreateRequest.setIds(Lists.newArrayList("52022133", "72450292", "218134268", "993812234", "19626317"));
        List<QuarkUserInfo> quarkUserInfos = iegProcessService.pushIegUser(iegTaskCreateRequest);
        System.out.println(JSON.toJSONString(quarkUserInfos));
    }

    @Test
    void test3() {
        LiveTaskCreateResponse douyin = iegProcessService.addAllTask(Lists.newArrayList("1984163642 ","2374322670 ","1590657780 ","1491882291 ","4197068996 ","1415819700 ","1254145207 ","853380855 ","1532790331 ","4253779115 ","1915186496 ","3820924418 ","2875321659 ","405558606 ","4087641408 ","4139018251 ","4214277389 ","4543390651 ","4634173561 ","195227235 ","1969948694 ","2187802578 ","2413395357 ","2435401866 ","2961692134 ","2988437272 ","3109577811 ","3286023031 ","1896801144 ","1941824069 ","2036321719 ","2120373099 ","2266936487 ","3605009584 ","4250413593 ","1896951991 ","1941716591 ","2162342681 ","232471357 ","2393072551 ","2519275965 ","262979154 ","3725515488 ","1330442986 ","3429059302 ","115432142 ","1793159071 ","2233589170 ","2409496546 ","2589655148"), "kuaishou");
        System.out.println(1);
    }

    @Test
    void getToken() {
        tokenService.getToken();
    }
}
