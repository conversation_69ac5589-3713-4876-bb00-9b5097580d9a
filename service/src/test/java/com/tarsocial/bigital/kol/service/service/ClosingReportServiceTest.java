//package com.tarsocial.bigital.kol.service.service;
//
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Lists;
//import com.tarsocial.bigital.kol.common.domain.dto.AutoRecommendDto;
//import com.tarsocial.bigital.kol.common.domain.dto.UserHeadersDto;
//import com.tarsocial.bigital.kol.common.domain.entity.Campaign;
//import com.tarsocial.bigital.kol.common.domain.esEntity.espost.EsPost;
//import com.tarsocial.bigital.kol.common.domain.request.CampaignReportRequest;
//import com.tarsocial.bigital.kol.common.domain.request.ExportCampaignKolRequest;
//import com.tarsocial.bigital.kol.service.KolServiceApplication;
//import com.tarsocial.bigital.kol.common.domain.request.CompareFilterRequest;
//import com.tarsocial.bigital.kol.common.domain.request.ExportCampaignKolRequest;
//import com.tarsocial.bigital.kol.service.KolServiceApplication;
//import com.tarsocial.bigital.kol.service.elastic.model.KolUserQueryEs;
//import com.tarsocial.bigital.kol.service.elastic.model.QuantumKolQueryEs;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockHttpServletRequest;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.List;
//
//@SpringBootTest(classes = KolServiceApplication.class)
//public class ClosingReportServiceTest {
//
//    @PostConstruct
//    public void init() {
//        MockHttpServletRequest request = new MockHttpServletRequest();
//        request.addHeader("TS-Tenant", "beauty");
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//    }
//
//    @Autowired
//    private ClosingReportService closingReportService;
//
//    @Autowired
//    private CampaignListService campaignListService;
//
//    @Resource
//    private CampaignKolService campaignKolService;
//
//    @Resource
//    private KolUserQueryEs kolUserQueryEs;
//
//    @Resource
//    private QuantumKolQueryEs quantumKolQueryEs;
//
//    @Resource
//    private KolService kolService;
//
//    @Resource
//    private BrandCompareService brandCompareService;
//
//    @Test
//    void brandTest() {
//        CompareFilterRequest request = new CompareFilterRequest();
//        request.setStartDate("2023-09-10");
//        request.setEndDate("2023-10-15");
//        request.setBrands(Lists.newArrayList("Aekyung"));
//        request.setDateType("day");
//        request.setCountBy("volume");
//        brandCompareService.getTrend(request);
//    }
//
//    @Test
//    void getESPostByUrl() {
//        List<EsPost> esPostByUrl = closingReportService.getESPostByUrl(Lists.newArrayList("https://www.douyin.com/video/7149203656439565605"));
//        System.out.println(JSON.toJSONString(esPostByUrl));
//    }
//
//    @Test
//    void testAuto() {
//        //level=prestige    &category=Skincare
//        List<AutoRecommendDto> prestige = campaignListService.autoRecommend("prestige", "Skincare");
//        System.out.println(prestige);
//    }
//
//    @Test
//    void testPortrait() {
//        //848456505900387 douyin
////        kolUserQueryEs.kolDetailQuery("99157282", "bilibili");
//        kolService.userPortrait("83307044560", "douyin");
//    }
//
//    @Test
//    void exportKol() {
//        ExportCampaignKolRequest exportCampaignKolRequest = new ExportCampaignKolRequest();
//        exportCampaignKolRequest.setCampaignId(5002L);
//        exportCampaignKolRequest.setKolUrls(Lists.newArrayList("https://www.xiaohongshu.com/user/profile/562ef75ae00dd861d410784a"));
//        exportCampaignKolRequest.setOperateId(10021L);
//        exportCampaignKolRequest.setOperateName("adsaldhq");
//
//        campaignKolService.exportCampaignKol(exportCampaignKolRequest.getKolUrls(),
//                exportCampaignKolRequest.getOperateId(),
//                exportCampaignKolRequest.getOperateName(),
//                exportCampaignKolRequest.getCampaignId());
//    }
//
//    @Test
//    void rank() {
//        CampaignReportRequest req = new CampaignReportRequest();
//        req.setCampaignId(5359L);
//        closingReportService.getPostDetail(req);
//        System.out.println(1);
//    }
//
//    @Test
//    void testAutoAc() {
//        Campaign campaign = new Campaign();
//        campaign.setId(99999999L);
//        campaign.setCategoryOneLevel("Makeup");
//        campaign.setPriceZone("masstige");
//        campaignListService.autoAllocation(new BigDecimal("1000000"), campaign);
//    }
//
//    @Test
//    void quantumKol() {
//        ArrayList<String> strings = new ArrayList<>();
//        strings.add("douyin_92658578924");
//        quantumKolQueryEs.kolUserQuery(strings);
//    }
//
//
//    public static void main(String[] args) {
//        hongshuHeaders();
//    }
//
//    private static void douyinHeaders() {
//        UserHeadersDto userHeadersDto = new UserHeadersDto();
//        List<UserHeadersDto.HeaderDetail> list = Lists.newArrayList();
//        list.add(new UserHeadersDto.HeaderDetail("内容类型", "contentTypes", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("粉丝数量", "followersCount", false, true));
//
//        list.add(new UserHeadersDto.HeaderDetail("互动率", "interateRate", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("完播率", "playOverRate", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("CPE", "cpe", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("CPM", "cpm", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("粉丝增量", "fansIncrement", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("粉丝增长率", "fansIncrementRate", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("播放中位数", "readMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("预估互动", "interactionMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("互动量", "interactionAvg", false, false));
//        userHeadersDto.setBase(list);
//        userHeadersDto.setCooperation(Lists.newArrayList(new UserHeadersDto.HeaderDetail("近半年合作品牌", "", true, true)));
//        userHeadersDto.setKol("price1_20");
//        userHeadersDto.setFinalSort(Lists.newArrayList("近半年合作品牌", "达人报价"));
//
//        System.out.println(JSON.toJSONString(userHeadersDto));
//    }
//
//    private static void hongshuHeaders() {
//        UserHeadersDto userHeadersDto = new UserHeadersDto();
//        List<UserHeadersDto.HeaderDetail> list = Lists.newArrayList();
//        list.add(new UserHeadersDto.HeaderDetail("内容类型", "contentTypes", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("粉丝数量", "followersCount", false, true));
//
//        list.add(new UserHeadersDto.HeaderDetail("互动率", "interateRate", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("完播率", "playOverRate", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("粉丝增量", "fansIncrement", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("粉丝增长率", "fansIncrementRate", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("播放中位数", "readMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("曝光中位数", "impMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("活跃粉丝占比", "activeFansRate", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("预估互动", "interactionMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("互动量", "interactionAvg", false, false));
//
//        userHeadersDto.setBase(list);
//        userHeadersDto.setCooperation(Lists.newArrayList(new UserHeadersDto.HeaderDetail("近半年合作品牌", "", true, true)));
//        userHeadersDto.setKol("videoPrice");
//        userHeadersDto.setFinalSort(Lists.newArrayList("近半年合作品牌", "达人报价", "即将上线字段"));
//
//        System.out.println(JSON.toJSONString(userHeadersDto));
//    }
//
//    private static void weiboHeaders() {
//        UserHeadersDto userHeadersDto = new UserHeadersDto();
//        List<UserHeadersDto.HeaderDetail> list = Lists.newArrayList();
//        list.add(new UserHeadersDto.HeaderDetail("内容类型", "contentTypes", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("粉丝数量", "followersCount", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("CPE", "cpe_weibo", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("CPM", "cpm_weibo", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("预估互动", "interactionMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("互动量", "interactionAvg", false, false));
//
//
//        userHeadersDto.setBase(list);
//        userHeadersDto.setCooperation(Lists.newArrayList(new UserHeadersDto.HeaderDetail("近半年合作品牌", "", true, true)));
//        userHeadersDto.setKol("priceImageArticle");
//        userHeadersDto.setFinalSort(Lists.newArrayList("近半年合作品牌", "达人报价"));
//
//        userHeadersDto.setComing(Lists.newArrayList(
//                new UserHeadersDto.HeaderDetail("预估阅读单价（图文）", "", false, false),
//                new UserHeadersDto.HeaderDetail("预估阅读单价（视频）", "", false, false),
//                new UserHeadersDto.HeaderDetail("日常笔记千赞比例", "", false, false),
//                new UserHeadersDto.HeaderDetail("商单笔记千赞比例", "", false, false)
//        ));
//        System.out.println(JSON.toJSONString(userHeadersDto));
//    }
//
//    private static void biliHeaders() {
//        UserHeadersDto userHeadersDto = new UserHeadersDto();
//        List<UserHeadersDto.HeaderDetail> list = Lists.newArrayList();
//        list.add(new UserHeadersDto.HeaderDetail("UP主类型", "contentTypes", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("粉丝数量", "followersCount", false, true));
//
//        list.add(new UserHeadersDto.HeaderDetail("CPM", "cpm", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("互动率中位数", "interacticeRateMedian", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("播放中位数", "readMedian", false, true));
//        list.add(new UserHeadersDto.HeaderDetail("评论中位数", "commentMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("点赞中位数", "likeMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("弹幕中位数", "danmuMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("预估互动", "interactionMedian", false, false));
//        list.add(new UserHeadersDto.HeaderDetail("互动量", "interactionAvg", false, false));
//
//
//        userHeadersDto.setBase(list);
//        userHeadersDto.setCooperation(Lists.newArrayList(new UserHeadersDto.HeaderDetail("近半年合作品牌", "", true, true)));
//        userHeadersDto.setKol("pricePost");
//        userHeadersDto.setFinalSort(Lists.newArrayList("近半年合作品牌", "达人报价"));
//
//        System.out.println(JSON.toJSONString(userHeadersDto));
//    }
//}
