package com.tarsocial.bigital.kol.service.service;

import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.QuarkPostDto;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.IegTaskCreateRequest;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.TencentTaskQueryRequest;
import com.tarsocial.bigital.kol.common.domain.response.ieg.TencentTaskResponse;
import com.tarsocial.bigital.kol.service.TransmissionServiceApplication;
import com.tarsocial.bigital.kol.service.constants.TaskTypeEnum;
import com.tarsocial.bigital.kol.service.dto.QuarkTaskConditionDto;
import com.tarsocial.bigital.kol.service.service.ieg.QuarkTaskService;
import com.tarsocial.bigital.kol.service.service.ieg.RealtimeService;
import io.lettuce.core.ScriptOutputType;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;


@SpringBootTest(classes = TransmissionServiceApplication.class)
public class RealtimeTest {


    @Resource
    private RealtimeService realtimeService;


    @Resource
    private QuarkTaskService quarkTaskService;


    @Test
    void liveTaskTest2() {
        //61f153340000000010006a75
        //"95815008948","712897203015095","3175031068697678","2581280782026868"
        List<QuarkPostDto> xiaohongshu = quarkTaskService.queryPost(Lists.newArrayList("61f153340000000010006a75"), "xiaohongshu");
        System.out.println(1);
    }


    @Test
    void liveTaskTest() {
//        IegTaskCreateRequest request = new IegTaskCreateRequest();
//        request.setPlatform("douyin");
//        request.setIds(Lists.list("2581280782026868"));
//        Long s = realtimeService.liveTask(request);
        realtimeService.perMinuteJOB();
    }

    @Test
    void perMinuteJOBTest() {
        realtimeService.perMinuteJOB();
    }


    @Test
    void kolTest() {
        IegTaskCreateRequest request = new IegTaskCreateRequest();
        request.setPlatform("douyin");
        request.setIds(Lists.list("62427282029"));
        Long aLong = realtimeService.shortVideoTask(request);
        System.out.println(1);
    }


    @Test
    void query() {
        TencentTaskQueryRequest taskQueryRequest = new TencentTaskQueryRequest();
        taskQueryRequest.setTaskId(22L);
        taskQueryRequest.setIds(Lists.list("62427282029"));
        taskQueryRequest.setPlatform("douyin");
        TencentTaskResponse tencentTaskResponse = realtimeService.queryLiveTask(taskQueryRequest);
        System.out.println(1);
    }

}
