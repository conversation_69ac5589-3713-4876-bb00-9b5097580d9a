package com.tarsocial.bigital.kol.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentQuarkTask;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentTaskPO;
import com.tarsocial.bigital.kol.service.TransmissionServiceApplication;
import com.tarsocial.bigital.kol.service.constants.BusinessTypeEnum;
import com.tarsocial.bigital.kol.service.mapper.TencentTaskMapper;
import com.tarsocial.bigital.kol.service.service.ieg.RealtimeService;
import com.tarsocial.bigital.kol.service.service.tencent.ieg.TencentQuarkTaskService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest(classes = TransmissionServiceApplication.class)
public class IegStatisticsTest {


    @Resource
    private RealtimeService realtimeService;

    @Resource
    private TencentTaskMapper tencentTaskMapper;

    @Resource
    private TencentQuarkTaskService tencentQuarkTaskService;

    private static final List<String> SHORT_PLATFORM = Lists.newArrayList("douyin", "xiaohongshu", "kuaishou", "bilibili", "weibo");

    private static final List<String> LIVE_PLATFORM = Lists.newArrayList("douyin", "kuaishou");


    /**
     * 腾讯IEG实时任务 月度统计
     */
    @Test
    void statisticsTest() {

        String startTime = "2025-09-01";
        String endTime = "2025-10-01";

        List<String> dateList = DateUtil.splitDateByDay(startTime, endTime);

//        System.out.println("日期\t抖音\t超时(抖音)\t小红书\t超时(小红书)\t快手\t超时(快手)\tB站\t超时(B站)\t微博\t超时(微博)");

        printLive(dateList);
    }


    private void printLive(List<String> dateList) {
        printResult(dateList, BusinessTypeEnum.REALTIME_LIVE.getCode(), LIVE_PLATFORM);
    }

    private void printShort(List<String> dateList) {
        printResult(dateList, BusinessTypeEnum.REALTIME_KOL.getCode(), SHORT_PLATFORM);
    }


    private void printResult(List<String> dateList, Integer type, List<String> platforms) {
        for (int i = 0; i < dateList.size(); i++) {
            if (i == dateList.size() - 1) {
                return;
            }

            String start = dateList.get(i);
            String end = dateList.get(i + 1);

            LambdaQueryWrapper<TencentTaskPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.ge(TencentTaskPO::getCreatedTime, start);
            wrapper.lt(TencentTaskPO::getCreatedTime, end);
            wrapper.eq(TencentTaskPO::getBusinessType, type);
            List<TencentTaskPO> tencentTaskPOS = tencentTaskMapper.selectList(wrapper);

            System.out.print(start);

            List<TencentQuarkTask> quarkTasks = null;
            List<Long> canceledTaskIds = new ArrayList<>();
            List<Long> taskId = tencentTaskPOS.stream().map(TencentTaskPO::getId).collect(Collectors.toList());
            if (!taskId.isEmpty()) {
                LambdaQueryWrapper<TencentQuarkTask> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(TencentQuarkTask::getTencentTaskId, taskId);
                quarkTasks = tencentQuarkTaskService.list(queryWrapper);
            }

            Map<String, List<TencentTaskPO>> platformMap = tencentTaskPOS.stream().collect(Collectors.groupingBy(TencentTaskPO::getPlatform));

            for (String platform : platforms) {
                List<TencentTaskPO> platformList = platformMap.get(platform);
                if (platformList != null) {
                    List<Long> platformTaskId = platformList.stream().map(TencentTaskPO::getId).collect(Collectors.toList());
                    List<TencentQuarkTask> platformQuarkTasks = quarkTasks.stream().filter(quarkTask -> platformTaskId.contains(quarkTask.getTencentTaskId())).collect(Collectors.toList());
                    canceledTaskIds = getCanceledTaskIds(platformQuarkTasks);
                    System.out.print("\t" + platformList.size() + "\t" + canceledTaskIds.size());
                } else {
                    System.out.print("\t0\t0");
                }
            }
            System.out.println();
        }
    }


    public List<Long> getCanceledTaskIds(List<TencentQuarkTask> quarkTasks) {
        return quarkTasks.stream()
                .collect(Collectors.groupingBy(
                        TencentQuarkTask::getTencentTaskId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                taskList -> taskList.stream()
                                        .allMatch(task -> "CANCEL".equals(task.getQuarkTaskStatus()))
                        )
                ))
                .entrySet().stream()
                .filter(Map.Entry::getValue)  // 保留状态完全为CANCEL的条目
                .map(Map.Entry::getKey)       // 提取tencentTaskId
                .collect(Collectors.toList()); // 收集为List
    }

}
