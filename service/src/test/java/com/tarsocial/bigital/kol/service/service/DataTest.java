package com.tarsocial.bigital.kol.service.service;


import com.tarsocial.bigital.kol.common.domain.entity.tencent.Task;
import com.tarsocial.bigital.kol.service.config.sign.SignEntity;
import com.tarsocial.bigital.kol.service.config.sign.SignModel;
import com.tarsocial.bigital.kol.service.constants.Constants;
import com.tarsocial.bigital.kol.service.job.guannian.GameTask;
import com.tarsocial.bigital.kol.service.service.guangnian.GameDataProcessService;
import com.tarsocial.bigital.kol.service.service.ocr.OcrDataProcessService;
import com.tarsocial.bigital.kol.service.service.tencent.TaskService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.*;

@SpringBootTest
@Slf4j
public class DataTest {

    @Resource
    private GameDataProcessService gameDataProcessService;

    @Resource
    private GameTask gameTask;


    @Resource
    private OcrDataProcessService ocrDataProcessService;

    @Resource
    private TaskService taskService;

    @Test
    void testPost() {
        gameDataProcessService.quarkPost(DateUtils.addDays(new Date(), -10), DateUtils.addDays(new Date(), -1), 90092L, Constants.TWO_KEYWORD);
    }


    @Test
    void testQuarkPost() {
        gameDataProcessService.quarkPostTest(DateUtils.addDays(new Date(), -2), DateUtils.addDays(new Date(), -1), 90093L);
    }

    @Test
    void pushFunPlusPost() {
        gameTask.pushFunPlusPost();
    }

    @Test
    void selectPostList() {
        ocrDataProcessService.selectPostList(Collections.singletonList("7364784330088008987"), "douyin");
    }

    @Test
    public void testSign() throws Throwable {
        String appSecret = "281503a6ae5b33e5bbcf97639f66a6fb";
        // 单元测试，测试 SignEntity.design 方法
        Map<String, String[]> params = new HashMap<>();

        SignEntity check = new SignEntity();
        check.setAppId(1L);
        check.setNonce("248994");
        check.setTimestamp("1716449568122");
        check.setSignModel(SignModel.SHA_256);
        params.put("body", new String[]{"{\"topicKey\":\"测试topickey123\",\"topicName\":\"测试话题名称-1416\",\"startTime\":\"2024-05-21\",\"endTime\":\"2024-05-26\",\"frequency\":2}"});
        check.setParams(params);

        String countersign = check.countersign(appSecret);

        System.out.println(countersign);
    }


    @Test
    public void taskList() throws Throwable {
        List<Task> tasks = taskService.taskList(1L, new Date());
        System.out.println(tasks.size());
    }

    @Test
    public void DateTest() {
        System.out.println(DateUtil.daysBetween(DateUtil.convertDate("2024-05-23 00:00:00", "yyyy-MM-dd HH:mm:ss"), new Date()));
        System.out.println(DateUtil.daysBetween(DateUtil.convertDate("2024-05-23 18:00:00", "yyyy-MM-dd HH:mm:ss"), new Date()));
        System.out.println(DateUtil.daysBetween(DateUtil.convertDate("2024-05-22 18:00:00", "yyyy-MM-dd HH:mm:ss"), new Date()));
        System.out.println(DateUtil.daysBetween(DateUtil.convertDate("2024-05-22 16:00:00", "yyyy-MM-dd HH:mm:ss"), new Date()));
        System.out.println(DateUtil.daysBetween(DateUtil.convertDate("2024-05-21 16:00:00", "yyyy-MM-dd HH:mm:ss"), new Date()));
        System.out.println(DateUtil.daysBetween(new Date(), new Date()));
    }

}
