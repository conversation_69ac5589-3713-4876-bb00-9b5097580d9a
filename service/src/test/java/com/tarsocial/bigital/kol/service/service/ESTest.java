package com.tarsocial.bigital.kol.service.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.service.TransmissionServiceApplication;
import com.tarsocial.bigital.kol.service.util.QueryUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@SpringBootTest(classes = TransmissionServiceApplication.class)
public class ESTest {
    @Qualifier("elasticsearchClient")
    @Resource
    private ElasticsearchClient client;


    @Test
    void test1() {
        List<String> list = Lists.newArrayList();
        list.addAll(getContent("2023-10-01", "2023-10-03"));
        list.addAll(getContent("2023-10-02", "2023-10-04"));
        list.addAll(getContent("2023-10-03", "2023-10-05"));
        list.addAll(getContent("2023-10-04", "2023-10-06"));
        list.addAll(getContent("2023-10-05", "2023-10-07"));

        try {
            FileWriter writer = new FileWriter("C:/Users/<USER>/Desktop/C/output.txt");
            writer.write(JSON.toJSONString(list));
            writer.close();
            System.out.println("Successfully wrote to the file.");
        } catch (IOException e) {
            System.out.println("An error occurred while writing to the file.");
            e.printStackTrace();
        }

    }

    private List<String> getContent(String start, String end) {
        List<String> list = Lists.newArrayList();
        SearchRequest searchRequest = SearchRequest.of(s -> {
            List<Query> must = Lists.newArrayList();
            must.add(QueryUtil.exists("tx_content"));
            must.add(QueryUtil.range("date_publishedAt", start, end));
            List<Query> mustNot = Lists.newArrayList();
            SearchRequest.Builder builder = s
                    .index("post")
                    .size(10000)
                    .query(q -> q.bool(q2 -> q2.must(must)
                            .mustNot(mustNot)));
            return builder;
        });
        SearchResponse<Map> searchResponse = null;
        System.out.println(searchRequest.toString());
        try {
            searchResponse = client.search(searchRequest, Map.class);
            List<Hit<Map>> hits = searchResponse.hits().hits();
            hits.forEach(i -> {
                String tx_content = i.source().get("tx_content").toString();
                list.add(tx_content);
            });

        } catch (IOException e) {
        }
        return list;
    }


    public static void main(String[] args) {
        String content = "Hello, World!";

        try {
            FileWriter writer = new FileWriter("C:/Users/<USER>/Desktop/C/output.txt");
            writer.write(content);
            writer.close();
            System.out.println("Successfully wrote to the file.");
        } catch (IOException e) {
            System.out.println("An error occurred while writing to the file.");
            e.printStackTrace();
        }
    }

}
