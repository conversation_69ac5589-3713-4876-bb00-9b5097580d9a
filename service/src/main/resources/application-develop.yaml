server:
  port: 17999
debug: false
logging:
  level:
    root: INFO
service:
  user-center:
    endPoint: https://apiusercenter.tarsocial.com
    ## 应用ID
    appId: 64
    ## 应用密钥
    appSecret: 4eaa260937c84d35adc1728cfe0cf68b
    ## 租户ID
    tenantId: 188

  middle:
    es:
      host: es-read-bas.tarsocial.com
      username: ta-read
      password: tDVu0TzifsQOc
  gs:
    es:
      host: es.gs2.tarsocial.com
      username: java-gs2
      password: 5VTDeIJr1UR.w
      port: 9200
  oss:
    endpoint: https://oss-cn-shanghai.aliyuncs.com
    accessKeyId: LTAI5tHWK6JVBb3YQkKdLyJ6
    accessKeySecret: ******************************
    bucket: abi-budtech-data-sync-nonprod
    bucketName: budtech_targetsocial_dev

##  用户中心接口白名单
user-center:
  security:
    auth:
      whitelist: /auth/**,/user/registe,/run,/error,/login/**,/external/**,/internal/**,/actuator/**,/v2/**,/swagger-ui.html,/swagger-ui.html/**,/userPassword/**,/sendCode/**,/druid/**,/actuator/**,/_health,/swagger-*/**,/api/ieg/get_token,/external/tencent/**,/game/**,/post/**,/backup/**,/api/ieg/live/task/retry,/ner/flexible/**,/api/getToken,/oauth/**,/dynamic/**

management:
  health:
    elasticsearch:
      enabled: false

dag:
  api-url: http://dag-api-ztweb.tarsocial.com
  organizationId: 1034
  username: <EMAIL>
  password: ocEG6#vj
  auth:
    url: http://dag-auth-ztweb.tarsocial.com/

spring:
  datasource:
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbcUrl: ********************************************************************************************************************************
      username: t_vivo_user
      password: mW6WzVAYvDSnV
  redis:
    host: ************
    port: 6379
    username:
    password: VgvUSZuXdvUn67Hv
    database: 40
    lettuce:
      pool:
        max-active: 20    #连接池的最大的连接数
        max-wait: -1       #最大的等待时间
        max-idle: 8       #连接池中最大的空闲连接时间
        min-idle: 0       #连接池中最小的空闲连接时间
    timeout: 30000 # 连接超时时间（毫秒)
  mail:
    # 配置 SMTP 服务器地址
    host: smtp.exmail.qq.com
    protocol: smtps
    # 发送者邮箱
    username: <EMAIL>
    # 配置密码,注意不是真正的密码,而是刚刚申请到的授权码
    password: CNL4vHz8TLGm9hvi
    # 端口号465或587
    port: 465
    # 默认的邮件编码为UTF-8
    default-encoding: UTF-8
    # 配置SSL 加密工厂
    properties:
      mail:
        auth: true
        smtp:
          socketFactoryClass: javax.net.ssl.SSLSocketFactory
          ssl:
            enable: true
        starttls:
          enable: true
        #表示开启 DEBUG 模式,这样,邮件发送过程的日志会在控制台打印出来,方便排查错误
        debug: false
  huoshanoss:
    access-key: AKLTMDE1NmYxYmFhYjkxNGE3ZDg5MzFmYmY2N2MzZTZmN2Y
    secret-key: TmpOak9HTmtZall5WXpBNU5EWXdZamxtT0RjNE1EaGxZV0V5TXpGaE9EYw==
    endpoint: tos-cn-shanghai.volces.com
    region: cn-shanghai
    connect-timeout-mills: 1000000
    bucket-name: kfhs-develop
  kafka:
    consumer:
      group-id:
        temp: develop-temp
        high: develop-high
        low: develop-low-2
        flexible-high: develop-flexible-high

app:
  app-id: abi-9he0jYt7
  app-secret: BFFC5CC375B01FDFF06548863AD6FD2B
  app-schema: abi_applet

quark:
  serverUrl: http://data-center-ztweb.tarsocial.com/
  wordCloudUrl: http://data-wordcloud-ztweb.tarsocial.com/
  dataTask: http://dag-task-ztweb.tarsocial.com/

wordCloud:
  url: http://t-data-wordcloud-ztweb.tarsocial.com/wordcloud/list

feign:
  secret: AQz3%##R1pfM
  client:
    config:
      default:
        connectTimeout: 900000
        readTimeout: 900000
sign:
  intercept:
    list: /external/**

ieg:
  intercept:
    list: /api/ieg/send_user_ids,/api/ieg/live/task/create,/api/ieg/live/query/user,/ner/flexible/**

game:
  email:
    receiver: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    ccUser:

jv:
  server:
    url: https://cpsp.rdmedia.cn/qianchuan-api
  data:
    num: 5000

xxl:
  job:
    admin:
      addresses: http://xxljob-uc.tarsocial.com/xxl-job-admin
    accessToken: default_token
    executor:
      appname: data-transmission-dev
      ## http://xxljob-cli-bas-dev.tarsocial.com:端口号   发布服务后有固定端口
      address: http://xxljob-cli-bas-dev.tarsocial.com:32269
      # 提供admin访问的端口
      ip:
      port: 8888
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30


mengniu:
  datasource:
    url: *******************************************
    user: t_gs_user
    password: VCDZyPsYQ1gb7NK3FcWs

kol:
  datasource:
    url:  ****************************************
    user: t_vivo_user
    password: mW6WzVAYvDSnV
llm:
  api:
    url: https://llm.tarsocial.com/bridge/v2

align:
  datasource:
    url: *********************************************
    user: p_align_tool_user
    password: 0PeRPJsSGGBRZNN