<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tarsocial.bigital.kol.service.mapper.InterfaceDataCountMapper">
  <resultMap id="BaseResultMap" type="com.tarsocial.bigital.kol.common.domain.entity.InterfaceDataCount">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="client_id" jdbcType="VARCHAR" property="clientId" />
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="target" jdbcType="VARCHAR" property="target" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="trace_id" jdbcType="VARCHAR" property="traceId" />
    <result column="query" jdbcType="VARCHAR" property="query" />
    <result column="total_count" jdbcType="BIGINT" property="totalCount" />
    <result column="scroll_count" jdbcType="BIGINT" property="scrollCount" />
    <result column="query_count" jdbcType="BIGINT" property="queryCount" />
    <result column="data_count" jdbcType="BIGINT" property="dataCount" />
    <result column="query_date" jdbcType="VARCHAR" property="queryDate" />
    <result column="query_time" jdbcType="TIMESTAMP" property="queryTime" />
    <result column="usage_time" jdbcType="BIGINT" property="usageTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, client_id, interface_name, target, batch_no, trace_id, query, total_count, scroll_count, 
    query_count, data_count, query_date, query_time, usage_time
  </sql>
</mapper>