<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tarsocial.bigital.kol.service.mapper.TencentIegUserLogMapper">
    <insert id="batchSave" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                insert into tencent_ieg_user_log(sub_task_id, kol_id, platform, type, task_id, status, retry,error_detail)
                values
                <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
                    #{item.subTaskId},#{item.kolId},#{item.platform},#{item.type},#{item.taskId},#{item.status},#{item.retry},#{item.errorDetail}
                </foreach>
                ON DUPLICATE KEY UPDATE
                sub_task_id = values(sub_task_id),
                kol_id = values(kol_id),
                platform = values(platform),
                type = values(type),
                task_id = values(task_id),
                status = values(status),
                retry = values(retry),
                error_detail = values(error_detail)
            </when>
            <otherwise>
                select 0
            </otherwise>
        </choose>
    </insert>
    <update id="updateTaskStatus">
        update tencent_ieg_user_log set platform = #{log.platform}
        <if test="log.kolId != null">
            ,kol_id = #{log.kolId}
        </if>
        where sub_task_id = #{log.subTaskId} and platform = #{log.platform};
    </update>
</mapper>
