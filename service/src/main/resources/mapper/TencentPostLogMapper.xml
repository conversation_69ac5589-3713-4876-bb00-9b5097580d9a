<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tarsocial.bigital.kol.service.mapper.TencentPostLogMapper">
    <insert id="batchSave" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                insert into tencent_post_log(topic_key, post_id, platform, post_published, post_updated)
                values
                <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
                    #{item.topicKey},#{item.postId},#{item.platform},#{item.postPublished},#{item.postUpdated}
                </foreach>
                ON DUPLICATE KEY UPDATE
                topic_key = values(topic_key),
                post_id = values(post_id),
                platform = values(platform),
                post_published = values(post_published),
                post_updated = values(post_updated)
            </when>
            <otherwise>
                select 0
            </otherwise>
        </choose>
    </insert>
</mapper>
