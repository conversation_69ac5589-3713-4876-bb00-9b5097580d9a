<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tarsocial.bigital.kol.service.mapper.DyUserDataMapper">
  <resultMap id="BaseResultMap" type="com.tarsocial.bigital.kol.common.domain.entity.oppo.DyUserData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="update_date" jdbcType="VARCHAR" property="updateDate" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="query_number" jdbcType="INTEGER" property="queryNumber" />
    <result column="user_nickname" jdbcType="VARCHAR" property="userNickname" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="followers_count" jdbcType="BIGINT" property="followersCount" />
    <result column="user_avatar" jdbcType="VARCHAR" property="userAvatar" />
    <result column="cpm" jdbcType="DOUBLE" property="cpm" />
    <result column="expected_play_count" jdbcType="BIGINT" property="expectedPlayCount" />
    <result column="short_video_price" jdbcType="BIGINT" property="shortVideoPrice" />
    <result column="long_video_price" jdbcType="BIGINT" property="longVideoPrice" />
    <result column="special_video_price" jdbcType="BIGINT" property="specialVideoPrice" />
    <result column="non_commercial_play_median" jdbcType="BIGINT" property="nonCommercialPlayMedian" />
    <result column="non_commercial_completion_rate" jdbcType="DOUBLE" property="nonCommercialCompletionRate" />
    <result column="non_commercial_engagement_rate" jdbcType="DOUBLE" property="nonCommercialEngagementRate" />
    <result column="non_commercial_engagement_count" jdbcType="BIGINT" property="nonCommercialEngagementCount" />
    <result column="commercial_play_median" jdbcType="BIGINT" property="commercialPlayMedian" />
    <result column="commercial_completion_rate" jdbcType="DOUBLE" property="commercialCompletionRate" />
    <result column="commercial_engagement_rate" jdbcType="DOUBLE" property="commercialEngagementRate" />
    <result column="commercial_engagement_count" jdbcType="BIGINT" property="commercialEngagementCount" />
    <result column="followers_device_distribution" jdbcType="VARCHAR" property="followersDeviceDistribution" />
    <result column="followers_region_distribution" jdbcType="VARCHAR" property="followersRegionDistribution" />
    <result column="followers_city_distribution" jdbcType="VARCHAR" property="followersCityDistribution" />
    <result column="followers_gender_distribution" jdbcType="VARCHAR" property="followersGenderDistribution" />
    <result column="followers_age_distribution" jdbcType="VARCHAR" property="followersAgeDistribution" />
    <result column="followers_city_level" jdbcType="VARCHAR" property="followersCityLevel" />
    <result column="mcn" jdbcType="VARCHAR" property="mcn" />
    <result column="followers_groups" jdbcType="VARCHAR" property="followersGroups" />
    <result column="industry_tags" jdbcType="VARCHAR" property="industryTags" />
    <result column="industry_sub_tags" jdbcType="VARCHAR" property="industrySubTags" />
    <result column="content_tags" jdbcType="VARCHAR" property="contentTags" />
    <result column="content_sub_tags" jdbcType="VARCHAR" property="contentSubTags" />
    <result column="conversion_index" jdbcType="DOUBLE" property="conversionIndex" />
    <result column="star_map_communication_index" jdbcType="DOUBLE" property="starMapCommunicationIndex" />
    <result column="star_map_grassroots_index" jdbcType="DOUBLE" property="starMapGrassrootsIndex" />
    <result column="star_map_cost_effectiveness_index" jdbcType="DOUBLE" property="starMapCostEffectivenessIndex" />
    <result column="star_map_cooperation_index" jdbcType="DOUBLE" property="starMapCooperationIndex" />
    <result column="monthly_connected_users" jdbcType="BIGINT" property="monthlyConnectedUsers" />
    <result column="monthly_depth_users" jdbcType="BIGINT" property="monthlyDepthUsers" />
    <result column="commercial_play_median_30d" jdbcType="BIGINT" property="commercialPlayMedian30d" />
    <result column="commercial_completion_rate_30d" jdbcType="DOUBLE" property="commercialCompletionRate30d" />
    <result column="commercial_engagement_rate_30d" jdbcType="DOUBLE" property="commercialEngagementRate30d" />
    <result column="commercial_engagement_count_30d" jdbcType="BIGINT" property="commercialEngagementCount30d" />
    <result column="non_commercial_play_median_30d" jdbcType="BIGINT" property="nonCommercialPlayMedian30d" />
    <result column="non_commercial_completion_rate_30d" jdbcType="DOUBLE" property="nonCommercialCompletionRate30d" />
    <result column="non_commercial_engagement_rate_30d" jdbcType="DOUBLE" property="nonCommercialEngagementRate30d" />
    <result column="non_commercial_engagement_count_30d" jdbcType="BIGINT" property="nonCommercialEngagementCount30d" />
    <result column="star_url" jdbcType="VARCHAR" property="starUrl" />
    <result column="user_url" jdbcType="VARCHAR" property="userUrl" />
    <result column="follower_growth_rate_15d" jdbcType="DOUBLE" property="followerGrowthRate15d" />
    <result column="follower_growth_rate_30d" jdbcType="DOUBLE" property="followerGrowthRate30d" />
    <result column="follower_growth_30d" jdbcType="BIGINT" property="followerGrowth30d" />
    <result column="audience_gender_distribution" jdbcType="VARCHAR" property="audienceGenderDistribution" />
    <result column="audience_age_distribution" jdbcType="VARCHAR" property="audienceAgeDistribution" />
    <result column="audience_region_distribution" jdbcType="VARCHAR" property="audienceRegionDistribution" />
    <result column="audience_city_distribution" jdbcType="VARCHAR" property="audienceCityDistribution" />
    <result column="audience_city_level_distribution" jdbcType="VARCHAR" property="audienceCityLevelDistribution" />
    <result column="audience_device_distribution" jdbcType="VARCHAR" property="audienceDeviceDistribution" />
    <result column="audience_major_groups_distribution" jdbcType="VARCHAR" property="audienceMajorGroupsDistribution" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="non_commercial_avg_shares_90d" jdbcType="BIGINT" property="nonCommercialAvgShares90d" />
    <result column="non_commercial_avg_comments_90d" jdbcType="BIGINT" property="nonCommercialAvgComments90d" />
    <result column="non_commercial_avg_likes_90d" jdbcType="BIGINT" property="nonCommercialAvgLikes90d" />
    <result column="commercial_avg_shares_90d" jdbcType="BIGINT" property="commercialAvgShares90d" />
    <result column="commercial_avg_comments_90d" jdbcType="BIGINT" property="commercialAvgComments90d" />
    <result column="commercial_avg_likes_90d" jdbcType="BIGINT" property="commercialAvgLikes90d" />
    <result column="non_commercial_avg_shares_30d" jdbcType="BIGINT" property="nonCommercialAvgShares30d" />
    <result column="non_commercial_avg_comments_30d" jdbcType="BIGINT" property="nonCommercialAvgComments30d" />
    <result column="non_commercial_avg_likes_30d" jdbcType="BIGINT" property="nonCommercialAvgLikes30d" />
    <result column="commercial_avg_shares_30d" jdbcType="BIGINT" property="commercialAvgShares30d" />
    <result column="commercial_avg_comments_30d" jdbcType="BIGINT" property="commercialAvgComments30d" />
    <result column="commercial_avg_likes_30d" jdbcType="BIGINT" property="commercialAvgLikes30d" />
    <result column="follower_growth_rank" jdbcType="BIGINT" property="followerGrowthRank" />
    <result column="collaboration_brands" jdbcType="VARCHAR" property="collaborationBrands" />
    <result column="collaboration_brands_beauty" jdbcType="VARCHAR" property="collaborationBrandsBeauty" />
    <result column="image_text_price" jdbcType="BIGINT" property="imageTextPrice" />
    <result column="identity_tags" jdbcType="VARCHAR" property="identityTags" />
    <result column="social_identity_tags" jdbcType="VARCHAR" property="socialIdentityTags" />
    <result column="search_index" jdbcType="DOUBLE" property="searchIndex" />
    <result column="expected_cpa_level_3" jdbcType="BIGINT" property="expectedCpaLevel3" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, update_date, update_time, query_number, user_nickname, user_id, followers_count, 
    user_avatar, cpm, expected_play_count, short_video_price, long_video_price, special_video_price, 
    non_commercial_play_median, non_commercial_completion_rate, non_commercial_engagement_rate, 
    non_commercial_engagement_count, commercial_play_median, commercial_completion_rate, 
    commercial_engagement_rate, commercial_engagement_count, followers_device_distribution, 
    followers_region_distribution, followers_city_distribution, followers_gender_distribution, 
    followers_age_distribution, followers_city_level, mcn, followers_groups, industry_tags, 
    industry_sub_tags, content_tags, content_sub_tags, conversion_index, star_map_communication_index, 
    star_map_grassroots_index, star_map_cost_effectiveness_index, star_map_cooperation_index, 
    monthly_connected_users, monthly_depth_users, commercial_play_median_30d, commercial_completion_rate_30d, 
    commercial_engagement_rate_30d, commercial_engagement_count_30d, non_commercial_play_median_30d, 
    non_commercial_completion_rate_30d, non_commercial_engagement_rate_30d, non_commercial_engagement_count_30d, 
    star_url, user_url, follower_growth_rate_15d, follower_growth_rate_30d, follower_growth_30d, 
    audience_gender_distribution, audience_age_distribution, audience_region_distribution, 
    audience_city_distribution, audience_city_level_distribution, audience_device_distribution, 
    audience_major_groups_distribution, description, non_commercial_avg_shares_90d, non_commercial_avg_comments_90d, 
    non_commercial_avg_likes_90d, commercial_avg_shares_90d, commercial_avg_comments_90d, 
    commercial_avg_likes_90d, non_commercial_avg_shares_30d, non_commercial_avg_comments_30d, 
    non_commercial_avg_likes_30d, commercial_avg_shares_30d, commercial_avg_comments_30d, 
    commercial_avg_likes_30d, follower_growth_rank, collaboration_brands, collaboration_brands_beauty, image_text_price,
    identity_tags, social_identity_tags, search_index, expected_cpa_level_3
  </sql>

  <update id="addQueryNumber" parameterType="java.util.List">
    update dy_user_data
    set query_number = query_number + 1
    where id in
    <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>
</mapper>