<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tarsocial.bigital.kol.service.mapper.TaskMapper">
    <insert id="batchSave" parameterType="java.util.List">
        <choose>
            <when test="list != null and list.size() > 0">
                insert into sys_task
                (app_id, topic_key, topic_name,published_at, start_time, end_time, frequency)
                values
                <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
                    #{item.appId},#{item.topicKey},#{item.topicName},#{item.publishedAt},#{item.startTime},#{item.endTime},#{item.frequency}
                </foreach>
                ON DUPLICATE KEY UPDATE
                app_id = values(app_id),
                topic_key = values(topic_key),
                topic_name = values(topic_name),
                published_at = values(published_at),
                start_time = values(start_time),
                end_time = values(end_time),
                frequency = values(frequency)
            </when>
            <otherwise>
                select 0
            </otherwise>
        </choose>
    </insert>
</mapper>
