server:
  compression:
    enabled: true
    mime-types: application/json
  port: 17999
  application:
    name: data-service
  servlet:
    encoding:
      charset: UTF-8
      force-response: true
spring:
  servlet:
    multipart:
      max-request-size: 300MB
      max-file-size: 300MB
      enabled: true
      location: ./
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    async:
      request-timeout: 30000
  profiles:
    active: develop
  main:
    lazy-initialization: false
    allow-circular-references: true
    allow-bean-definition-overriding: true
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      indent_output: true
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false
  kafka:
    bootstrap-servers: ************:9092,***********:9092,************:9092
    topic:
      temp: ner_temp
      high: ner_high
      low: ner_low,ner_low2,ner_low3
      flexible-rapid-in: flexible-in
      flexible-rapid-out: flexible-out

logging:
  charset:
    console: UTF-8
    file: UTF-8
    config: classpath:logback-spring.xml
  file:
    path: logs # 相对路径 或 绝对路径
    name: data-transmission
  group:
    db: org.springframework.jdbc,com.tarsocial.bigital.kol.service.mapper
  level:
    root: INFO
    db: DEBUG
    com.tarsocial.bigital.kol.service.mapper: DEBUG
  logback:
    rollingpolicy:
      clean-history-on-start: true

management:
  health:
    redis:
      enabled: false
    elasticsearch:
      enabled: false
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: /_health


#MINIO
oss:
  name: minio
  accessKey: sAPG7SZj2ZEMMpDwI7VV
  secretKey: RGl2ZhyKLvv9dBWR0fQnB46AEumZZuvTndQAlbVu
  bucketName: mengniu
  endpoint: http://************:9000
  fileExt: png,jpg,jpeg,pdf

emotion:
  serviceUrl: http://t-emotion-cla-v2.aistack-idc.tarsocial.com


pg:
  es:
    host: es-industry.tarsocial.com
    username: gs2-java-read
    password: PbzSgSgobeXww