server:
  port: 17999
debug: false
logging:
  level:
    root: INFO
service:
  user-center:
    endPoint: https://apiusercenter.tarsocial.com
    ## 应用ID
    appId: 65
    ## 应用密钥
    appSecret: 7d6d92fc111946caae6ac1667098c6ac
    ## 租户ID
    tenantId: 190

  middle:
    es:
      host: es.read.united-data-platform.tarsocial.com
      username: read
      password: uPfiowjeofweu9r8uryefwehiF
  gs:
    es:
      host: es-industry.tarsocial.com
      username: gs2-java-read
      password: PbzSgSgobeXww
#      port: 9200
  oss:
    endpoint: https://oss-cn-shanghai.aliyuncs.com
    accessKeyId: LTAI5tHWK6JVBb3YQkKdLyJ6
    accessKeySecret: ******************************
    bucket: abi-budtech-data-sync-nonprod
    bucketName: budtech_targetsocial_dev

##  用户中心接口白名单
user-center:
  security:
    auth:
      whitelist: /auth/**,/user/registe,/run,/error,/login/**,/external/**,/internal/**,/actuator/**,/v2/**,/swagger-ui.html,/swagger-ui.html/**,/userPassword/**,/sendCode/**,/druid/**,/actuator/**,/_health,/swagger-*/**,/api/ieg/get_token,/external/tencent/**,/game/**,/post/**,/backup/**,/api/ieg/live/task/retry,/oauth/**,/api/getToken

management:
  health:
    elasticsearch:
      enabled: false

dag:
  api-url: https://dag-api-ztweb.tarsocial.com
  organizationId: 1034
  username: <EMAIL>
  password: ocEG6#vj
  auth:
    url: https://dag-auth-ztweb.tarsocial.com/

spring:
  datasource:
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbcUrl: ******************************************************************************************************************************************
      username: data-transmissiondbuser
      password: mW6WzV4dwdh2Cxe22kAYvDSnV
  redis:
    host: ************
    port: 6379
    username:
    password: VgvUSZuXdvUn67Hv
    database: 40
    lettuce:
      pool:
        max-active: 20    #连接池的最大的连接数
        max-wait: -1       #最大的等待时间
        max-idle: 8       #连接池中最大的空闲连接时间
        min-idle: 0       #连接池中最小的空闲连接时间
    timeout: 30000 # 连接超时时间（毫秒)
  mail:
    # 配置 SMTP 服务器地址
    host: smtp.exmail.qq.com
    protocol: smtps
    # 发送者邮箱
    username: <EMAIL>
    # 配置密码,注意不是真正的密码,而是刚刚申请到的授权码
    password: CNL4vHz8TLGm9hvi
    # 端口号465或587
    port: 465
    # 默认的邮件编码为UTF-8
    default-encoding: UTF-8
    # 配置SSL 加密工厂
    properties:
      mail:
        auth: true
        smtp:
          socketFactoryClass: javax.net.ssl.SSLSocketFactory
          ssl:
            enable: true
        starttls:
          enable: true
        #表示开启 DEBUG 模式,这样,邮件发送过程的日志会在控制台打印出来,方便排查错误
        debug: false
  huoshanoss:
    access-key: AKLTMDE1NmYxYmFhYjkxNGE3ZDg5MzFmYmY2N2MzZTZmN2Y
    secret-key: TmpOak9HTmtZall5WXpBNU5EWXdZamxtT0RjNE1EaGxZV0V5TXpGaE9EYw==
    endpoint: tos-cn-shanghai.volces.com
    region: cn-shanghai
    connect-timeout-mills: 1000000
    bucket-name: kfhs-develop
  kafka:
    consumer:
      group-id:
        flexible-high: develop-flexible-high

app:
  app-id: abi-9he0jYt7
  app-secret: BFFC5CC375B01FDFF06548863AD6FD2B
  app-schema: abi_applet

quark:
  serverUrl: https://data-center-ztweb.tarsocial.com/
  wordCloudUrl: http://data-wordcloud-ztweb.tarsocial.com/
  dataTask: https://dag-task-ztweb.tarsocial.com/

wordCloud:
  url: http://t-data-wordcloud-ztweb.tarsocial.com/wordcloud/list

feign:
  secret: AQz3%##R1pfM
  client:
    config:
      default:
        connectTimeout: 900000
        readTimeout: 900000
sign:
  intercept:
    list: /external/**

ieg:
  intercept:
    list: /api/ieg/send_user_ids,/api/ieg/live/task/create,/api/ieg/live/query/user,/amp/kolList

game:
  email:
    receiver: <EMAIL>
    ccUser:

jv:
  server:
    url: https://qianchuan.cpsp.com/api
  data:
    num: 2000

xxl:
  job:
    admin:
      addresses: http://xxljob-uc.tarsocial.com/xxl-job-admin
    accessToken: default_token
    executor:
      appname: data-transmission-prod
      ## http://xxljob-cli-bas-dev.tarsocial.com:端口号   发布服务后有固定端口
      address: http://xxljob-cli-bas-prod.tarsocial.com:30183
      # 提供admin访问的端口
      ip:
      port: 8888
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30

mengniu:
  datasource:
    url: *******************************************
    user: t_gs_user
    password: VCDZyPsYQ1gb7NK3FcWs
align:
  datasource:
    url: *********************************************
    user: p_align_tool_user
    password: 0PeRPJsSGGBRZNN