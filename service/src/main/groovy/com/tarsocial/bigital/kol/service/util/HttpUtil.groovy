package com.tarsocial.bigital.kol.service.util

import okhttp3.*
import okio.Buffer
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.time.LocalDateTime
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicLong

/**
 * Created by Stanford on 10/20/16.
 */
class HttpUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpsUtil.class);
    private static final AtomicLong counter = new AtomicLong()
    private static final String path = getTempPath() + "http_${LocalDateTime.now().format('yyyyMMdd')}"
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(300, TimeUnit.SECONDS)
            .readTimeout(300, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build()
    private static final batchId = System.currentTimeMillis()

    static boolean saveLog = true

    static {
        final f = new File(path)
        if (!f.exists()) f.mkdir()
        logger.info('http temp folder: {}', path)
    }

    static final String save(Request req, Map resp) {
        if (!saveLog || !req || !resp) return null

        final filename = "${path}/http_${batchId}_${LocalDateTime.now().format('yyyyMMdd_HHmmss')}_${String.format('%04d', counter.incrementAndGet())}_${resp.code}.log".toString()

        Writer w = new FileWriter(filename)
        w.println('----------------------------- request --')
        w.println("${req.method()}\t${req.url()}")
        req.headers().toMultimap().each { final k, List v ->
            v.each {
                w.println("\t${StringUtils.rightPad(k, 30)} ${it}")
            }
        }
        w.println('')
        final buffer = new Buffer()
        req.body()?.writeTo(buffer)
        w.println(buffer.toString())
        w.println('----------------------------- response --')
        w.println("response code: ${resp.code}, cost: ${String.format('%,d', resp.cost)} ms")
        resp.headers?.each { final k, v ->
            w.println("\t${StringUtils.rightPad(k, 30)} ${v}")
        }
        w.println('')
        w.println(resp.body)
        w.flush()
        w.close()
        return filename
    }


    public static Response request(Request req, int max = 1) {
        if (req == null) return null
        for (int i = 0; i < max; i++) {
            try {
                return client.newCall(req).execute();
            } catch (IOException e) {
                if (i < max - 1)
                    logger.error("Error on request attempt #${i + 1}/${max} (excetion caught, retrying): ${e.message}")
                else
                    logger.error("Error on request attempt #${i + 1}/${max} (excetion caught, retry finished): ${req.method()}\t${req.url()}, ${e.message}")
            } catch (e) {
                logger.error("Error on request attempt #${i + 1}/${max} (excetion caught, no retry): ${req.method()}\t${req.url()}", e)
                break
            }
        }
        return null
    }

    public static Map requestString(Request req, int max = 1) {
        long t = System.currentTimeMillis()
        Response resp = request(req, max)
        t = System.currentTimeMillis() - t
        if (resp == null) return null

        Map headers = [:]
        resp.headers().names().each { String name ->
            headers.put(name.toLowerCase(), resp.header(name))
        }
        Map result = [
                code   : resp.code(),
                headers: headers,
                body   : resp.body().string(),
                cost   : t
        ]
        close(resp)

        save(req, result)

        return result
    }

    public static <T> T requestJson(Request req, Class<T> clazz = Map.class) {
        def resp
        try {
            resp = requestString(req)
            def json = JsonUtil.fromJson(resp?.body, clazz)
            if (json == null) throw new Exception('Bad JSON')
            return json
        } catch (e) {
            logger.error("Error on request: $req\nRequestBody: ${req.body()}\nResponse: $resp", e)
        }
        return null
    }

    public static void main(String[] args) {
        final payloadRelation = '{"data":[{"date_publishedAt": "2020-10-01T11:22:00", "document": "牟逯桑香港凯蒂美妆贸易,主营欧妆,日韩免税护肤品,彩妆!", "long_ageGroup": "0", "document_id": "ik_sl_v2_202010_bbs|bbs|0_2c730761a076de1c0d77544d47ecbc3c", "entity": [{"name": "香港", "mention": "香港", "start_offset": 3, "end_offset": 5, "type": "购买", "recog_type": "rule"}]}]}'
        Request reqRelation = new Request.Builder()
                .url('http://10.10.100.228:8389/algo_oulaiya_relation')
                .post(RequestBody.create(payloadRelation, MediaType.parse("application/json")))
                .header("Content-Type", "application/json")
                .header("cache-control", "no-cache",)
                .build()

        final resp = requestString(reqRelation)
        println(JsonUtil.toPrettyJson(resp))
    }

    public static String getTempPath() {
        String path = System.getProperty("java.io.tmpdir")
        if (!path) path = '/tmp'
        if (!path.endsWith(File.separator)) path = path + File.separator
        return path
    }

    static void close(c) {
        try {
            if (c != null) c.close()
        } catch (e) {
        }
    }
}
