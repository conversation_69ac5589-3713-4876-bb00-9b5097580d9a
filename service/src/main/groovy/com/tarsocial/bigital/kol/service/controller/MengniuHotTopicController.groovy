package com.tarsocial.bigital.kol.service.controller


import com.tarsocial.bigital.kol.common.domain.response.ieg.IegBaseResponse
import com.tarsocial.bigital.kol.common.domain.response.mengniu.BaseListResponse
import com.tarsocial.bigital.kol.service.service.MengniuHotTopService
import com.tarsocial.bigital.kol.service.util.DateUtil
import lombok.AllArgsConstructor
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

import javax.annotation.Resource

/**
 * <AUTHOR> @Date 2024/11/14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/mengniu")
class MengniuHotTopicController {

    @Resource
    MengniuHotTopService mengniuHotTopService

    static Logger logger = LoggerFactory.getLogger(MengniuHotTopicController.class)

    @PostMapping("/getTodayHot")
    BaseListResponse<List> getTodayHotV2(@RequestBody Map<String, String> params) {
        if (params.token == null) {
            return new BaseListResponse<>(-1, "token不能为空")
        }
        String todayStr = DateUtil.formatDate(new Date(), 'yyyy-MM-dd')
        String hh = DateUtil.formatDate(new Date(), 'HH')
        String hotDate = params.hotDate ? params.hotDate : todayStr
        if(hotDate != null && params.hotDate == todayStr && Integer.parseInt(hh) < 12){
            return new BaseListResponse<>(-2, "data is unavailable")
        }
        hotDate = 'v2_'+hotDate
        if(!mengniuHotTopService.log(hotDate.replaceAll('-', ''))){
            return new BaseListResponse<>(-3, "data is unavailable")
        }
        return new BaseListResponse<>(mengniuHotTopService.getTopicList(hotDate, params.batchNo))
    }

    @PostMapping("/getHotspotDetail")
    def getHotspotDetailV2(@RequestBody Map<String, String> params) {
        if (params.token == null) {
            return new IegBaseResponse<>(-1, "token不能为空")
        }
        String todayStr = DateUtil.formatDate(new Date(), 'yyyy-MM-dd')
        String hh = DateUtil.formatDate(new Date(), 'HH')
        String hotDate = params.hotDate ? params.hotDate : todayStr
        if(hotDate != null && params.hotDate == todayStr && Integer.parseInt(hh) < 12){
            return new IegBaseResponse<>(-2, "data is unavailable")
        }
        String v2_hotDate = 'v2_'+hotDate
        if(!mengniuHotTopService.log(v2_hotDate.replaceAll('-', ''))){
            return new IegBaseResponse<>(-3, "data is unavailable")
        }

        return new IegBaseResponse<>(mengniuHotTopService.getFinalResult(v2_hotDate, params.hotName))
    }



    @PostMapping("/v1/getTodayHot")
    BaseListResponse<List> getTodayHot(@RequestBody Map<String, String> params) {
        if (params.token == null) {
            return new IegBaseResponse<>(-1, "token不能为空")
        }
        return new BaseListResponse<>(mengniuHotTopService.getTopicList(params.hotDate, params.batchNo, false))
    }

    @PostMapping("/v1/getHotspotDetail")
    def getHotspotDetail(@RequestBody Map<String, String> params) {
        if (params.token == null) {
            return new IegBaseResponse<>(-1, "token不能为空")
        }
        return new IegBaseResponse<>(mengniuHotTopService.getTopicDetails(params.hotDate, params.hotName))
    }


    @PostMapping("/saveFinalResult")
    String saveFinalResult(@RequestBody Map<String, String> params) {
        return mengniuHotTopService.saveFinalResult(params.hotDate)
    }


}
