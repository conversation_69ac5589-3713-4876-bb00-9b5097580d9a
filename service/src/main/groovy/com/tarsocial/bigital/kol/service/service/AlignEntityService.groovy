package com.tarsocial.bigital.kol.service.service

import com.tarsocial.bigital.kol.service.util.Util
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class AlignEntityService {

    @Value('${align.datasource.url}')
    private String url
    @Value('${align.datasource.user}')
    private String user
    @Value('${align.datasource.password}')
    private String password


    public Map<String, String> getAllEntityMapping(String industry, String type) {
        if (type == null) {
            return new HashMap<>()
        }

        def sqlConf = [
                driver  : 'com.mysql.cj.jdbc.Driver',
                host    : url,
                port    : 3306,
                db      : 'gsdb',
                user    : user,
                password: password
        ]

        List<Map> data = Util.query(sqlConf, """
          SELECT DISTINCT m.identify_entity_name as origin,
                        e.name                 as standard
        FROM t_entity_mapping m
                     LEFT JOIN t_standard_entity e ON m.standard_entity_id = e.id
        WHERE m.industry = '${industry}'
          AND m.status IN ('pass', 'fix')
          AND m.standard_type= '${type}'
        """)

        Map<String, String> resultMap = new HashMap<>(data.size() * 2);
        for (Map<String, String> map : data) {
            String key = (String) map.get("origin")
            String value = (String) map.get("standard")
            resultMap.put(key, value)
        }
        return resultMap
    }

}
