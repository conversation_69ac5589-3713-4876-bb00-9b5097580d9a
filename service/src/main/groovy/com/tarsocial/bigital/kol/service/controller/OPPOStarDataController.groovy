package com.tarsocial.bigital.kol.service.controller

import com.tarsocial.bigital.kol.common.domain.dto.InterfaceCounter
import com.tarsocial.bigital.kol.common.domain.response.oppo.OPPOResponse
import com.tarsocial.bigital.kol.service.service.OPPOStarDataService
import lombok.AllArgsConstructor
import org.slf4j.MDC
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes

import javax.annotation.Resource
import javax.servlet.http.HttpServletRequest

@RestController
@AllArgsConstructor
@RequestMapping("/oppo")
class OPPOStarDataController {

    @Resource
    OPPOStarDataService oppoStarDataService

    @PostMapping("/getStarData")
    def getStarData(@RequestBody Map<String, String> params) {
        if (params.token == null) {
            return new OPPOResponse<>(-1, "token不能为空")
        }
        if (!params.platform || params.platform != "1") {
            return new OPPOResponse<>(500, "平台不存在")
        }

        def counter = InterfaceCounter.init()
        counter.interfaceName = "/oppo/getStarData"
        counter.traceId = MDC.get("traceId")
        counter.clientId = tenantName()
        def data = oppoStarDataService.scrollQuery(counter, params)

        new OPPOResponse(data, counter.batchNo, counter.hasMore())

    }

    def tenantName() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest req = attributes.getRequest();
            return req.getHeader("user-name");
        }
        return "non"
    }

}
