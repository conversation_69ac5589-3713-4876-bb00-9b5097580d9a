package com.tarsocial.bigital.kol.service.controller

import com.tarsocial.bigital.kol.service.util.HttpUtil
import com.tarsocial.bigital.kol.service.util.JsonUtil
import com.tarsocial.bigital.kol.service.util.Util
import lombok.AllArgsConstructor
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
/**
 * <AUTHOR> @Date 2024/11/22
 */
@RestController
@AllArgsConstructor
@RequestMapping("/oauth")
class OauthController {
    static Logger logger = LoggerFactory.getLogger(OauthController.class)

    @Value('${spring.datasource.mysql.jdbcUrl}')
    private String url
    @Value('${spring.datasource.mysql.username}')
    private String user
    @Value('${spring.datasource.mysql.password}')
    private String password
    @Value('${spring.datasource.mysql.driver-class-name}')
    private String driver

    static final Map juliang_app_secret = [
            1816297353698596 : 'c346188ef7cb0b2e1edb2caa30cc284c54c8e801',
    ]
    static final Map red_app_secret = [
            (1550L) : 'k0u0X9hvkI4rPTNk',
    ]

    @GetMapping("/{platform}/{path_app_id}")
    def log(@PathVariable("platform") String platform, @PathVariable("path_app_id") String path_app_id, @RequestParam String auth_code, @RequestParam String state, @RequestParam(required = false) String uid, @RequestParam(required = false) String app_id, @RequestParam(required = false) String material_auth_status, @RequestParam(required = false) String scope) {
        logger.info("platform: $platform, auth_code: $auth_code, state: $state, uid: $uid, app_id: $app_id, material_auth_status: $material_auth_status, scope: $scope")
        def access_token = null
        def refresh_token = null
        def auth_result = null
        if(platform && ( platform == 'pgy' || platform == 'pugongying' || platform == 'juguang') ){
            //蒲公英
            def appIdNumber = path_app_id.toLong()
            if(!red_app_secret.containsKey(appIdNumber))
                return "Authorization failed, app_id ${appIdNumber} unknow!".toString()
            def body = [
                    app_id: appIdNumber,
                    secret: red_app_secret[appIdNumber],
                    auth_code: auth_code
            ]
            def req = new Request.Builder()
                .url("https://adapi.xiaohongshu.com/api/open/oauth2/access_token")
                .post(RequestBody.create(JsonUtil.toJson(body), MediaType.parse("application/json")))
                .header("Content-Type", "application/json")
                .header("cache-control", "no-cache",)
                .build()

            final res_body = JsonUtil.toMap(HttpUtil.requestString(req).body)
            if(res_body && res_body.data && res_body.code == 0){
                access_token = res_body.data.access_token
                refresh_token = res_body.data.refresh_token
            }else {
                return "Authorization failed! ${res_body}"
            }
            auth_result = JsonUtil.toPrettyJson(res_body)
        }
        if(platform && platform.contains("juliang")){
            //巨量
            def appIdNumber = path_app_id.toLong()
            if(!juliang_app_secret.containsKey(appIdNumber)) return "Authorization failed, app_id ${appIdNumber} unknow!".toString()

            def body = [
                    app_id: appIdNumber,
                    secret: juliang_app_secret[appIdNumber],
                    grant_type: "auth_code",
                    auth_code: auth_code
            ]
            def req = new Request.Builder()
                .url("https://ad.oceanengine.com/open_api/oauth2/access_token/")
                .post(RequestBody.create(JsonUtil.toJson(body), MediaType.parse("application/json")))
                .header("Content-Type", "application/json")
                .header("cache-control", "no-cache",)
                .build()

            final res_body = JsonUtil.toMap(HttpUtil.requestString(req).body)
            if(res_body && res_body.data && res_body.code == 0){
                access_token = res_body.data.access_token
                refresh_token = res_body.data.refresh_token
            }else {
                return "Authorization failed! ${res_body}"
            }
            auth_result = JsonUtil.toPrettyJson(res_body)
        }


        def sqlConf = [
                driver  : driver,
                host    : url,
                user    : user,
                password: password
        ]

        Util.execute(sqlConf, "insert into oauth_log (platform, auth_code, state, uid, app_id, material_auth_status, scope, access_token, refresh_token, auth_result) values ('$platform', '$auth_code', '$state', '$uid', '$app_id', '$material_auth_status', '$scope', '$access_token', '$refresh_token', '$auth_result')")

        return "Authorization success!"
    }


}
