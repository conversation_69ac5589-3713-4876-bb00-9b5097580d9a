package com.tarsocial.bigital.kol.service.util

import groovy.sql.Sql
import org.apache.commons.lang3.time.DurationFormatUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.sql.DriverManager

class Util {
    private static final Logger logger = LoggerFactory.getLogger(Util.class);

    public static final ts_milli = System.currentTimeMillis()

    public static Sql getSql(Map config) {
        try {
            if (!config.containsKey('driver') || !config.containsKey('host')|| !config.containsKey('user') || !config.containsKey('password')) {
                logger.error('invalid db server;')
                return null
            }
            Class.forName(config.driver as String)
            def conn = DriverManager.getConnection("${config.host}", config.user, config.password)
            return new Sql(conn)
        } catch (SocketException e) {
            logger.error('Error when loading db_config', e)
            return null
        }
    }

    public static List<Map> query(Map sqlConf, String query) {
        long ts = System.currentTimeMillis()
        def sql = getSql(sqlConf)
        try {
            logger.info('querying : {}', query.replaceAll('\\s+', ' '))
            final rows = sql.rows(query)

            logger.info('querying : {}\n\t(cost {}, fetched {} rows)', query.replaceAll('\\s+', ' '), cost(ts), fn(rows?.size() ?: 0))
            return rows
        } catch (e) {
            logger.error('error on querying : ' + query, e)
        } finally {
            close(sql)
        }
    }

    static execute(Map sqlConf, String query) {
        long ts = System.currentTimeMillis()
        def sql = getSql(sqlConf)
        try {
            logger.info('executing : {}', query.replaceAll('\\s+', ' '))
//            final rows = sql.rows(query)
            sql.execute(query)

            logger.info('executed : {}\n\t(cost {})', query.replaceAll('\\s+', ' '), cost(ts))
        } catch (e) {
            logger.error('error on executing : ' + query, e)
        } finally {
            close(sql)
        }
    }

    static String fn(int n, String format = '%,d') {
        return String.format(format, n)
    }

    static String fn(long n, String format = '%,d') {
        return String.format(format, n)
    }

    static String cost(long tsStart = ts_milli, String format = 'H:mm:ss') {
        return DurationFormatUtils.formatDuration(System.currentTimeMillis() - tsStart, format)
    }

    static void close(c) {
        try {
            if (c != null) c.close()
        } catch (e) {
        }
    }
}
