package com.tarsocial.bigital.kol.service.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * Created by Stanford on 10/20/16.
 */
class JsonUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpsUtil.class);
    // ObjectMapper is thread-safe, ref: http://wiki.fasterxml.com/JacksonBestPracticesPerformance
    static final ObjectMapper mapper = new ObjectMapper()

    static {
        mapper.configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
        mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY)
    }

    static String toPrettyJson(Object o) {
        return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(o)
    }

    static String toJson(Object o) {
        return mapper.writeValueAsString(o)
    }

    static <T> T fromJson(String json, Class<T> c) {
        try {
            return json == null ? null : mapper.readValue(json, c);
        } catch (e) {
            logger.error(">> Parse Error ${e.class.simpleName}: ${json}", e)
            return null
        }
    }

    static <T> T fromJson(String json, TypeReference valueTypeRef) {
        try {
            return json == null ? null : mapper.readValue(json, valueTypeRef);
        } catch (e) {
            logger.error(">> Parse Error ${e.class.simpleName}: ${json}", e)
            return null
        }
    }

    static Map toMap(String json) {
        return fromJson(json, Map.class)
    }

    static List toList(String json) {
        return fromJson(json, List.class)
    }
}
