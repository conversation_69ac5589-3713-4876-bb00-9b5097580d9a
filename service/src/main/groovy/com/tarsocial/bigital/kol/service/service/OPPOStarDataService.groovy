package com.tarsocial.bigital.kol.service.service

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.google.common.collect.Lists
import com.tarsocial.bigital.kol.common.domain.dto.InterfaceCounter
import com.tarsocial.bigital.kol.common.domain.dto.oppo.DyUserDataDto
import com.tarsocial.bigital.kol.common.domain.entity.InterfaceDataCount
import com.tarsocial.bigital.kol.common.domain.entity.oppo.DyUserData
import com.tarsocial.bigital.kol.service.exception.BusinessException
import com.tarsocial.bigital.kol.service.mapper.DyUserDataMapper
import com.tarsocial.bigital.kol.service.service.oppo.DyUserDataService
import com.tarsocial.bigital.kol.service.util.NoUtil
import com.tarsocial.bigital.kol.service.util.QueryWrapperUtil
import com.tarsocial.usercenter.web.common.RedisUtils
import org.apache.ibatis.session.SqlSessionFactory
import org.springframework.stereotype.Service

import javax.annotation.Resource
import java.text.SimpleDateFormat

@Service
class OPPOStarDataService {

    @Resource
    private RedisUtils redisUtils;

    @Resource
    SqlSessionFactory sessionFactory

    @Resource
    DyUserDataService dyUserDataService

    @Resource
    InterfaceDataCountService interfaceDataCountService

    private dev_client = "<EMAIL>"

    List<DyUserDataDto> scrollQuery(InterfaceCounter counter, Map params) {

        String condition;
        def current = 0

        def batchNo = params.batchNo
        if (batchNo) {
            InterfaceDataCount lastInfo = batchInfo(batchNo)
            if (!lastInfo)
                throw new BusinessException("批次号不存在 : ${batchNo} ")
            if (lastInfo.totalCount <= lastInfo.scrollCount)
                throw new BusinessException("批次查询已结束 : ${batchNo} ")
            current = lastInfo.getScrollCount()
            condition = lastInfo.getQuery()
        } else {
            batchNo = "O" + new SimpleDateFormat("yyMMddHHmmssSSS").format(counter.queryTime) + NoUtil.generateNo(6)
            condition = paramsCondition(params)
        }

        if (redisUtils.get(redisKey(batchNo))) {
            throw new BusinessException("批次滚动查询中 : ${batchNo} ")
        }

        redisUtils.set(redisKey(batchNo), "${batchNo}_${new SimpleDateFormat('yyyyMMddHHmmss').format(counter.queryTime)}" as String, 1000 * 60 * 10)

        counter.setBatchNo(batchNo)
        counter.setQuery(condition)
        counter.setTarget("dy_user_data")

        def size = Integer.valueOf(params.get("size")?: "1000")

        if (size > 1000) size = 1000

        def where = condition? " WHERE ${condition} " : " "
        QueryWrapper<DyUserData> select = new QueryWrapper()
        select.last(where)

        def count = dyUserDataService.count(select)

        select = new QueryWrapper()
        select.last(" ${where} LIMIT ${current}, ${size} ")

//        def page = dyUserDataService.page(new Page<>(current, size), select)
//        def records = page.getRecords()

        def records = dyUserDataService.list(select)

        counter.setTotalCount(count)
        counter.setScrollCount(current + records.size())
        counter.setQueryCount(size)
        counter.setDataCount(records.size())

        interfaceDataCountService.save(counter.toCount())

        def updateIds = records.collect { dyData -> dyData.getId() }

        if (!counter.clientId || !dev_client.equals(counter.clientId))
            dyUserDataService.addQueryNumber(updateIds)

        redisUtils.del(redisKey(batchNo))

        records.collect{ data ->
            DyUserDataDto.form(data)
        }
    }

    def redisKey(String batchNo) {
        "oppo-batch-${batchNo}"
    }

    InterfaceDataCount batchInfo(String batchNo) {
        QueryWrapper<InterfaceDataCount> wrapper = new QueryWrapper<>()
        wrapper.eq(InterfaceDataCount.COL_BATCH_NO, batchNo).orderBy(true, false, InterfaceDataCount.COL_ID).last(" LIMIT 1")
        interfaceDataCountService.getOne(wrapper)
    }

    def paramsCondition(Map params) {


        List<String> userIds;
        if(params.userIds)
            userIds = Lists.newArrayList(params.userIds.split(',')).collect{ String id -> id.trim()}
        def geCPM = params.geCPM
        def leCPM = params.leCPM
        def geConnectedCount = params.geConnectedCount
        def leConnectedCount = params.leConnectedCount
        def geFollowersCount = params.geFollowersCount
        def leFollowersCount = params.leFollowersCount

        QueryWrapper<DyUserData> wrapper = new QueryWrapper()
        if (userIds) wrapper.in(DyUserData.COL_USER_ID, userIds)
        if (geCPM) wrapper.ge(DyUserData.COL_CPM, geCPM)
        if (leCPM) wrapper.le(DyUserData.COL_CPM, leCPM)
        if (geConnectedCount) wrapper.ge(DyUserData.COL_FOLLOWERS_COUNT, geFollowersCount)
        if (leConnectedCount) wrapper.le(DyUserData.COL_FOLLOWERS_COUNT, leFollowersCount)
        if (geFollowersCount) wrapper.ge(DyUserData.COL_MONTHLY_CONNECTED_USERS, geConnectedCount)
        if (leFollowersCount) wrapper.le(DyUserData.COL_MONTHLY_CONNECTED_USERS, leConnectedCount)

        wrapperCondition(wrapper)
    }

    def wrapperCondition(wrapper) {
        def sql = QueryWrapperUtil.sql(sessionFactory.getConfiguration(), wrapper, DyUserDataMapper.class)
        QueryWrapperUtil.condition(sql)
    }

}
