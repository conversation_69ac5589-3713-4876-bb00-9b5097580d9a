package com.tarsocial.bigital.kol.service.controller

import com.tarsocial.bigital.kol.service.service.DynamicKolSentimentService
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

import javax.annotation.Resource


@RestController()
@RequestMapping("/dynamic")
class DynamicKolSentimentController {

    @Resource
    private DynamicKolSentimentService dynamicKolSentimentService;

    /**
     * 根据 xlsx 导入 KOL 信息
     * 支持表头：kol_id/user_id/nickname/platform（或中文：达人ID/用户ID/昵称/平台）
     * 示例入库表：public.quantum_update（如需其他表，请调整 insertSql 构造）
     */
    @PostMapping(path = "/kol/import", consumes = "multipart/form-data", produces = "application/json")
    @ResponseBody
    def importKolFromXlsx(@RequestParam("file") MultipartFile file,
                          @RequestParam(value = "defaultPlatform", required = false) String defaultPlatform,
                          @RequestParam(value = "hasHeader", required = false, defaultValue = "true") boolean hasHeader) {
        return dynamicKolSentimentService.importKolFromXlsx(file, defaultPlatform, hasHeader)
    }


    /**
     * 根据 kol_data 表中的 kw_userId 批量查询 ES 并入库（字段取 _source）
     * 索引：post_2024*, post_2025*, comment_2024*, comment_2025*
     * 过滤：object_user.kw_userId / kw_postType in [跟帖, 评论] / kw_platformName in [汽车之家, 懂车帝]
     */
    @PostMapping(path = "/kol/sync-es-to-koldata")
    @ResponseBody
    def syncEsToKolData(@RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        return dynamicKolSentimentService.syncEsToKolDataOptimized(size)
    }

    /**
     * 默认brandKeyword = 零跑
     * 调用LLM API分析汽车品牌的情感倾向
     * 读取kol_data表中包含指定品牌相关内容的数据，调用外部API进行情感分析，并将结果写入main_post_sentiment和comment_sentiment字段
     */
    @PostMapping(path = "/kol/analyze-sentiment")
    @ResponseBody
    def analyzeSentimentForBrand(@RequestParam(value = "batchSize", required = false, defaultValue = "1000") Integer batchSize,
                                @RequestParam(value = "brandKeyword", required = true) String brandKeyword) {
        return dynamicKolSentimentService.analyzeSentimentForBrand(batchSize, brandKeyword)
    }

    /**
     * 根据 kol_data 数据生成 JSONL 文件并上传到 OSS
     * 生成的JSONL文件按照ES _source结构，包含情感分析结果映射
     * 示例：/kol/export-to-oss?whereCondition=main_post_sentiment='正面'&limit=500
     */
    @PostMapping(path = "/kol/export-to-oss")
    @ResponseBody
    def exportKolDataToOss(@RequestParam(value = "whereCondition", required = false) String whereCondition,
                          @RequestParam(value = "limit", required = false, defaultValue = "1000") Integer limit,
                          @RequestParam(value = "fileName", required = false) String fileName) {
        return dynamicKolSentimentService.exportKolDataToOss(whereCondition, limit, fileName)
    }
}