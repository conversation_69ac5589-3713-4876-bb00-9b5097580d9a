package com.tarsocial.bigital.kol.service.service;

import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.ScrollRequest;
import co.elastic.clients.elasticsearch.core.ScrollResponse;
import co.elastic.clients.elasticsearch.core.ClearScrollRequest;
import com.tarsocial.bigital.kol.service.util.QueryUtil;
import com.tarsocial.bigital.kol.service.util.Util;
import lombok.var;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.management.Query;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import java.io.ByteArrayInputStream;
import java.io.InputStream;

@Service
public class DynamicKolSentimentService {

    @Value("${kol.datasource.url}")
    private String url;
    @Value("${kol.datasource.user}")
    private String user;
    @Value("${kol.datasource.password}")
    private String password;
    @Value("${kol.datasource.database}")
    private String database;
    private Map<String, Object> sqlConf;

    // LLM API 配置
    @Value("${llm.api.url}")
    private String llmApiUrl;

    // OSS 配置
    private static final String OSS_ENDPOINT = "oss-cn-shanghai.aliyuncs.com";
    private static final String OSS_ACCESS_KEY_ID = "LTAI5tBxi1yBocPRi1o9AnHX";
    private static final String OSS_ACCESS_KEY_SECRET = "******************************";
    private static final String OSS_BUCKET_NAME = "leapmotor-data-store";

    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    void init() {
        sqlConf = new HashMap<>();
        sqlConf.put("driver", "org.postgresql.Driver");
        sqlConf.put("host", url);
        sqlConf.put("user", user);
        sqlConf.put("password", password);
        sqlConf.put("database", "");
    }

    public Map<String, Object> importKolFromXlsx(MultipartFile file, String defaultPlatform, boolean hasHeader) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> errors = new ArrayList<>();
        int total = 0, inserted = 0, skipped = 0;

        try {
            if (file == null || file.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件为空");
                return result;
            }
            String filename = file.getOriginalFilename() == null ? "" : file.getOriginalFilename().toLowerCase();
            if (!filename.endsWith(".xlsx")) {
                result.put("success", false);
                result.put("message", "仅支持 .xlsx 文件");
                return result;
            }

            var workbook = WorkbookFactory.create(file.getInputStream());
            var sheet = workbook.getSheetAt(0);

            Map<String, Integer> headerIndex = new HashMap<>();
            int startRowIdx = 0;

            if (hasHeader) {
                var headerRow = sheet.getRow(0);
                if (headerRow == null) {
                    result.put("success", false);
                    result.put("message", "首行表头不存在");
                    return result;
                }
                var it = headerRow.cellIterator();
                int idx = 0;
                while (it.hasNext()) {
                    var cell = it.next();
                    String name = (cell == null ? "" : cell.toString()).trim().toLowerCase();
                    switch (name) {
                        case "kol_id":
                        case "达人id":
                            headerIndex.put("kol_id", idx); break;
                        case "user_id":
                        case "用户id":
                            headerIndex.put("user_id", idx); break;
                        case "nickname":
                        case "昵称":
                            headerIndex.put("nickname", idx); break;
                        case "platform":
                        case "平台":
                            headerIndex.put("platform", idx); break;
                        default:
                            break;
                    }
                    idx++;
                }
                startRowIdx = 1;
            } else {
                headerIndex.put("kol_id", 0);
                headerIndex.put("platform", 1);
                headerIndex.put("nickname", 2);
                startRowIdx = 0;
            }

            String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            for (int r = startRowIdx; r <= sheet.getLastRowNum(); r++) {
                var row = sheet.getRow(r);
                if (row == null) {
                    skipped++;
                    continue;
                }
                total++;

                String kolId = getCell(row, headerIndex.get("kol_id"));
                String userId = getCell(row, headerIndex.get("user_id"));
                String nickname = getCell(row, headerIndex.get("nickname"));
                String platform = getCell(row, headerIndex.get("platform"));

                if ((platform == null || platform.isEmpty()) && defaultPlatform != null && !defaultPlatform.isEmpty()) {
                    platform = defaultPlatform;
                }
                if ((kolId == null || kolId.isEmpty()) && userId != null && !userId.isEmpty()) {
                    kolId = userId;
                }


                StringBuilder insertSql = new StringBuilder();
                insertSql.append("INSERT INTO kol (kol_id, platform, kol_status, create_time) ")
                         .append("VALUES ('").append(escape(kolId)).append("', '")
                         .append(escape(platform)).append("', '1', '").append(now).append("') ")
                         .append("ON CONFLICT (kol_id, platform) DO UPDATE SET ")
                         .append("kol_status = '1', create_time = '").append(now).append("'");

                Util.execute(sqlConf, insertSql.toString());
                inserted++;
            }

            result.put("success", true);
            result.put("total", total);
            result.put("inserted", inserted);
            result.put("skipped", skipped);
            result.put("errors", errors);
            return result;
        } catch (Exception e) {
            result.put("success", false);
            result.put("total", total);
            result.put("inserted", inserted);
            result.put("skipped", skipped);
            result.put("errors", errors);
            result.put("message", e.getMessage());
            return result;
        }
    }

    private static String getCell(org.apache.poi.ss.usermodel.Row row, Integer idx) {
        if (idx == null) return null;
        var cell = row.getCell(idx);
        return cell == null ? null : cell.toString().trim();
    }


    @javax.annotation.Resource
    @org.springframework.beans.factory.annotation.Qualifier("elasticsearchClient")
    private co.elastic.clients.elasticsearch.ElasticsearchClient esClient;


    private static String trimStr(Object v) { return v == null ? null : String.valueOf(v).trim(); }
    private static boolean isEmpty(String s) { return s == null || s.isEmpty(); }
    private static String nullable(String s) { return s == null ? "" : s; }
    private static String escape(String s) {
        // 如果输入为null，直接返回null（或返回""，根据实际场景调整）
        if (s == null) {
            return null; // 或 return "";
        }
        // 非null时执行替换
        return s.replace("'", "''");
    }

    /**
     * 执行批量插入
     * @param batchValuesList 批量值列表
     * @param cols 列名
     */
    private void executeBatchInsert(List<String> batchValuesList, String cols) {
        if (batchValuesList.isEmpty()) {
            return;
        }

        String batchSql = "INSERT INTO kol_data (" + cols + ") VALUES " +
                String.join(",", batchValuesList) + " AS new_data " +
                "ON DUPLICATE KEY UPDATE " +
                "kw_id = new_data.kw_id, " +
                "kw_userId = new_data.kw_userId, " +
                "platform = new_data.platform, " +
                "kol_status = new_data.kol_status, " +
                "kw_url = new_data.kw_url, " +
                "kw_platformName = new_data.kw_platformName, " +
                "date_publishedAt = new_data.date_publishedAt, " +
                "tkw_nickname = new_data.tkw_nickname, " +
                "tx_title = new_data.tx_title, " +
                "tx_content = new_data.tx_content, " +
                "emotion = new_data.emotion, " +
                "origin_date_publishedAt = new_data.origin_date_publishedAt, " +
                "origin_tkw_nickname = new_data.origin_tkw_nickname, " +
                "origin_kwUserId = new_data.origin_kwUserId, " +
                "origin_tx_content = new_data.origin_tx_content, " +
                "origin_emotion = new_data.origin_emotion, " +
                "origin_long_commentsCount = new_data.origin_long_commentsCount, " +
                "origin_long_likeCount = new_data.origin_long_likeCount, " +
                "origin_long_repostsCount = new_data.origin_long_repostsCount, " +
                "origin_long_collectCount = new_data.origin_long_collectCount, " +
                "type = new_data.type, " +
                "date_createAt = new_data.date_createAt, " +
                "update_time = NOW()";

        Util.execute(sqlConf, batchSql);
    }

    /**
     * 优化版本的 syncEsToKolData 方法，使用批量插入
     */
    public Map<String, Object> syncEsToKolDataOptimized(Integer size) {
        String now = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Map<String, Object> result = new HashMap<>();
        int inserted = 0, skipped = 0;

        try {
            // 1) 读 kol 的 kw_userId
            List<Map> rows = com.tarsocial.bigital.kol.service.util.Util.query(sqlConf,
                    "select distinct kw_userId from kol where kw_userId is not null and kw_userId <> ''");
            List<String> userIds = new ArrayList<>();
            for (Map r : rows) {
                Object v = r.get("kw_userId");
                if (v != null) {
                    String s = String.valueOf(v).trim();
                    if (!s.isEmpty()) userIds.add(s);
                }
            }
            if (userIds.isEmpty()) {
                result.put("success", false);
                result.put("message", "kol 表中无 kw_userId 数据");
                return result;
            }

            // 2) 构造 ES 查询
            java.util.List<co.elastic.clients.elasticsearch._types.query_dsl.Query> filters = new java.util.ArrayList<>();
            filters.add(com.tarsocial.bigital.kol.service.util.QueryUtil.terms("object_user.kw_userId", userIds));
            filters.add(com.tarsocial.bigital.kol.service.util.QueryUtil.terms("kw_postType", java.util.Arrays.asList("跟帖", "评论")));
            filters.add(com.tarsocial.bigital.kol.service.util.QueryUtil.terms("kw_platformName", java.util.Arrays.asList("汽车之家", "懂车帝")));

            // 构造 _source includes
            List<String> sourceIncludes = Arrays.asList("kw_id",
                    "kw_url", "kw_platformName", "date_publishedAt", "date_createAt",
                    "object_user.tkw_nickname", "object_user.kw_userId", "tx_title", "tx_content",
                    "object_emotionLabel.kw_emotion", "object_origin.date_publishedAt",
                    "object_origin.object_user.tkw_nickname", "object_origin.object_user.kw_userId",
                    "object_origin.tx_content", "object_origin.object_emotionLabel.kw_emotion",
                    "object_origin.long_commentsCount", "object_origin.long_likeCount",
                    "object_origin.long_repostsCount", "object_origin.long_collectCount"
            );

            Integer total = 0;
            int scrollSize = (size != null && size > 0) ? size : 1000;
            String scrollTime = "5m";
            String scrollId = null;

            // 批量插入相关变量
            int batchSize = 500;
            List<String> batchValuesList = new ArrayList<>();
            String cols = "`kw_id`,`kw_userId`,`platform`,`kol_status`,`kw_url`,`kw_platformName`," +
                    "`date_publishedAt`,`tkw_nickname`,`tx_title`,`tx_content`,`emotion`," +
                    "`origin_date_publishedAt`,`origin_tkw_nickname`,`origin_kwUserId`," +
                    "`origin_tx_content`,`origin_emotion`," +
                    "`origin_long_commentsCount`,`origin_long_likeCount`,`origin_long_repostsCount`,`origin_long_collectCount`," +
                    "`type`,`date_createAt`,`create_time`";

            try {
                // 初始化 scroll 查询
                co.elastic.clients.elasticsearch.core.SearchRequest initialSearchRequest = co.elastic.clients.elasticsearch.core.SearchRequest.of(s -> s
                        .index("post_2024*,post_2025*,comment_2024*,comment_2025*")
                        .query(q -> q.bool(b -> b.filter(filters)))
                        .size(scrollSize)
                        .scroll(co.elastic.clients.elasticsearch._types.Time.of(t -> t.time(scrollTime)))
                        .source(src -> src.filter(f -> f.includes(sourceIncludes)))
                );

                SearchResponse<Map> response = esClient.search(initialSearchRequest, Map.class);
                scrollId = response.scrollId();
                List<Hit<Map>> hits = response.hits().hits();

                while (hits != null && !hits.isEmpty()) {
                    // 处理每条记录，收集批量插入数据
                    for (Hit<Map> h : hits) {
                        Map source = h.source();
                        if (source == null) {
                            skipped++;
                            continue;
                        }

                        String indexName = h.index();
                        String type = (indexName != null && indexName.startsWith("post_")) ? "post" : "comment";
                        String kwId = trimStr(source.get("kw_id"));
                        String kwUrl = trimStr(source.get("kw_url"));
                        String kwPlatformName = trimStr(source.get("kw_platformName"));
                        String datePublishedAt = trimStr(source.get("date_publishedAt"));
                        String dateCreateAt = trimStr(source.get("date_createAt"));

                        Map objectUser = (Map) source.get("object_user");
                        String tkwNickname = objectUser != null ? trimStr(objectUser.get("tkw_nickname")) : null;
                        String kwUserId = objectUser != null ? trimStr(objectUser.get("kw_userId")) : null;

                        String txTitle = trimStr(source.get("tx_title"));
                        String txContent = trimStr(source.get("tx_content"));

                        Map objectEmotionLabel = (Map) source.get("object_emotionLabel");
                        String emotion = objectEmotionLabel != null ? trimStr(objectEmotionLabel.get("kw_emotion")) : null;

                        Map origin = (Map) source.get("object_origin");
                        String originDatePublishedAt = origin != null ? trimStr(origin.get("date_publishedAt")) : null;

                        Map originUser = origin != null ? (Map) origin.get("object_user") : null;
                        String originTkwNickname = originUser != null ? trimStr(originUser.get("tkw_nickname")) : null;
                        String originKwUserId = originUser != null ? trimStr(originUser.get("kw_userId")) : null;

                        String originTxContent = origin != null ? trimStr(origin.get("tx_content")) : null;
                        Map originEmotionLabel = origin != null ? (Map) origin.get("object_emotionLabel") : null;
                        String originEmotion = originEmotionLabel != null ? trimStr(originEmotionLabel.get("kw_emotion")) : null;

                        Object originComments = origin != null ? origin.get("long_commentsCount") : null;
                        Object originLikes = origin != null ? origin.get("long_likeCount") : null;
                        Object originReposts = origin != null ? origin.get("long_repostsCount") : null;
                        Object originCollect = origin != null ? origin.get("long_collectCount") : null;

                        if (isEmpty(kwUrl) || isEmpty(kwUserId)) {
                            skipped++;
                            continue;
                        }

                        // 构造单条记录的值列表
                        List<String> vals = new ArrayList<>(25);
                        vals.add("'" + escape(nullable(kwId)) + "'");
                        vals.add("'" + escape(nullable(kwUserId)) + "'");
                        vals.add("'" + escape(nullable(kwPlatformName)) + "'");
                        vals.add("1");
                        vals.add("'" + escape(nullable(kwUrl)) + "'");
                        vals.add("'" + escape(nullable(kwPlatformName)) + "'");
                        vals.add("'" + escape(nullable(datePublishedAt)) + "'");
                        vals.add("'" + escape(nullable(tkwNickname)) + "'");
                        vals.add("'" + escape(nullable(txTitle)) + "'");
                        vals.add("'" + escape(nullable(txContent)) + "'");
                        vals.add("'" + escape(nullable(emotion)) + "'");
                        vals.add(originDatePublishedAt == null || originDatePublishedAt.trim().isEmpty()
                                ? "NULL" : "'" + escape(originDatePublishedAt) + "'");
                        vals.add("'" + escape(nullable(originTkwNickname)) + "'");
                        vals.add("'" + escape(nullable(originKwUserId)) + "'");
                        vals.add("'" + escape(nullable(originTxContent)) + "'");
                        vals.add("'" + escape(nullable(originEmotion)) + "'");
                        vals.add(originComments != null ? originComments.toString() : "NULL");
                        vals.add(originLikes != null ? originLikes.toString() : "NULL");
                        vals.add(originReposts != null ? originReposts.toString() : "NULL");
                        vals.add(originCollect != null ? originCollect.toString() : "NULL");
                        vals.add("'" + escape(nullable(type)) + "'");
                        vals.add("'" + escape(nullable(dateCreateAt)) + "'");
                        vals.add("NOW()");

                        String valuesClause = "(" + String.join(",", vals) + ")";
                        batchValuesList.add(valuesClause);

                        // 当达到批量大小时执行批量插入
                        if (batchValuesList.size() >= batchSize) {
                            executeBatchInsert(batchValuesList, cols);
                            inserted += batchValuesList.size();
                            batchValuesList.clear();
                        }
                    }

                    total += hits.size();

                    // 获取下一批数据
                    if (scrollId != null) {
                        String finalScrollId = scrollId;
                        ScrollRequest scrollRequest = ScrollRequest.of(s -> s
                                .scrollId(finalScrollId)
                                .scroll(co.elastic.clients.elasticsearch._types.Time.of(t -> t.time(scrollTime)))
                        );

                        ScrollResponse<Map> scrollResponse = esClient.scroll(scrollRequest, Map.class);
                        scrollId = scrollResponse.scrollId();
                        hits = scrollResponse.hits().hits();
                    } else {
                        break;
                    }
                }

                // 处理剩余的批量数据
                if (!batchValuesList.isEmpty()) {
                    executeBatchInsert(batchValuesList, cols);
                    inserted += batchValuesList.size();
                    batchValuesList.clear();
                }

            } finally {
                // 清理 scroll 上下文
                if (scrollId != null) {
                    try {
                        String finalScrollId1 = scrollId;
                        ClearScrollRequest clearScrollRequest = ClearScrollRequest.of(c -> c
                                .scrollId(finalScrollId1)
                        );
                        esClient.clearScroll(clearScrollRequest);
                    } catch (Exception e) {
                        System.err.println("清理 scroll 上下文失败: " + e.getMessage());
                    }
                }
            }

            result.put("success", true);
            result.put("total", total);
            result.put("inserted", inserted);
            result.put("skipped", skipped);
            return result;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 调用LLM API分析汽车品牌的情感倾向
     * @param batchSize 批量处理大小，默认100
     * @param brandKeyword 品牌关键词，用于筛选相关数据
     * @return 处理结果
     */
    public Map<String, Object> analyzeSentimentForBrand(Integer batchSize, String brandKeyword) {
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Map<String, Object> result = new HashMap<>();
        int processed = 0, updated = 0, failed = 0;

        try {
            int size = (batchSize != null && batchSize > 0) ? batchSize : 100;

            // 验证品牌关键词参数
            if (isEmpty(brandKeyword)) {
                result.put("success", false);
                result.put("message", "品牌关键词不能为空");
                return result;
            }

            // 查询需要分析的数据 - 只查询包含指定品牌相关内容且未分析过的数据
            String query = "SELECT kw_id, tx_title, tx_content, origin_tx_content " +
                          "FROM kol_data " +
                          "WHERE (main_post_sentiment IS NULL OR comment_sentiment IS NULL) " +
                          //"AND (tx_content LIKE '%" + escape(brandKeyword) + "%' OR tx_title LIKE '%" + escape(brandKeyword) + "%' OR origin_tx_content LIKE '%" + escape(brandKeyword) + "%') " +
                          "LIMIT " + size;

            List<Map> rows = com.tarsocial.bigital.kol.service.util.Util.query(sqlConf, query);

            if (rows.isEmpty()) {
                result.put("success", true);
                result.put("message", "没有需要分析的数据");
                result.put("processed", 0);
                return result;
            }

            // 批量处理数据
            for (Map row : rows) {
                try {
                    String kwId = String.valueOf(row.get("kw_id"));
                    String txTitle = trimStr(row.get("tx_title"));
                    String txContent = trimStr(row.get("tx_content"));
                    String originTxContent = trimStr(row.get("origin_tx_content"));

                    // 调用LLM API分析情感
                    Map<String, Object> sentimentResult = callLLMForSentimentAnalysis(txTitle, txContent, originTxContent, brandKeyword);

                    // 构造更新SQL，根据帖子类型和内容情况更新对应字段
                    StringBuilder updateSqlBuilder = new StringBuilder("UPDATE kol_data SET ");
                    boolean hasUpdate = false;

                    String mainSentiment = (String) sentimentResult.get("主贴情感");
                    String commentSentiment = (String) sentimentResult.get("评论情感");
                    Object feelingWords = sentimentResult.get("感受词");

                    // 更新主贴情感字段
                    if (!isEmpty(mainSentiment)) {
                        updateSqlBuilder.append("main_post_sentiment = '").append(escape(mainSentiment)).append("'");
                        hasUpdate = true;
                    }

                    // 更新评论情感字段
                    if (!isEmpty(commentSentiment)) {
                        if (hasUpdate) {
                            updateSqlBuilder.append(", ");
                        }
                        updateSqlBuilder.append("comment_sentiment = '").append(escape(commentSentiment)).append("'");
                        hasUpdate = true;
                    }

                    // 更新感受词字段（如果有感受词数据）
                    if (feelingWords != null) {
                        if (hasUpdate) {
                            updateSqlBuilder.append(", ");
                        }
                        String feelingWordsJson = objectMapper.writeValueAsString(feelingWords);
                        updateSqlBuilder.append("feeling_words = '").append(escape(feelingWordsJson)).append("'");
                        hasUpdate = true;
                    }

                    if (hasUpdate) {
                        updateSqlBuilder.append(", update_time = NOW() WHERE kw_id = '").append(escape(kwId)).append("'");
                        Util.execute(sqlConf, updateSqlBuilder.toString());
                        updated++;
                    } else {
                        // 如果没有内容需要更新，记录为跳过
                        failed++;
                    }

                    processed++;

                    // 添加延迟避免API限流
                    Thread.sleep(100);

                } catch (Exception e) {
                    System.err.println("处理记录失败: " + e.getMessage());
                    failed++;
                    processed++;
                }
            }

            result.put("success", true);
            result.put("processed", processed);
            result.put("updated", updated);
            result.put("failed", failed);
            result.put("timestamp", now);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("processed", processed);
            result.put("updated", updated);
            result.put("failed", failed);
        }

        return result;
    }

    /**
     * 构造内容字符串
     */
    private String buildContent(String title, String content) {
        StringBuilder sb = new StringBuilder();
        if (!isEmpty(title)) {
            sb.append(title);
        }
        if (!isEmpty(content)) {
            if (sb.length() > 0) {
                sb.append(" ");
            }
            sb.append(content);
        }
        return sb.toString();
    }

    /**
     * 调用LLM API进行情感分析
     */
    private Map<String, Object> callLLMForSentimentAnalysis(String txTitle, String objectOriginTxContent, String txContent, String brandKeyword) {
        try {
            // 构造系统提示词
            String systemPrompt = "<prompt>\n" +
                    "<context>\n" +
                    "分析给定内容中作者针对" + brandKeyword + "这个汽车品牌的情感倾向,分别判断主贴内容的情感和评论内容的情感\n" +
                    "</context>\n" +
                    "<instruction>\n" +
                    "1. 需新增列，将提取的用户感受词以指定JSON格式填入该列；\n" +
                    "</instruction>\n" +
                    "<note>\n" +
                    "1. 情感为 正面,中性,负面\n" +
                    "2. 判断主贴情感时,请分析从主贴作者对" + brandKeyword + "的情感倾向,主贴标题为tx_title,主贴内容为object_origin.tx_content\n" +
                    "3. 判断评论情感时,请结合主贴内容从评论作者对" + brandKeyword + "的情感倾向,评论内容字段为tx_content\n" +
//                    "4. 单个感受词时格式为[\"keyword\"]，多个时用英文逗号分隔\n" +
                    "</note>\n" +
                    "<output_format>\n" +
                    "{\n" +
                    "  \"主贴情感\": \"\",\n" +
                    "  \"评论情感\": \"\",\n" +
//                    "  \"感受词\": []\n" +
                    "}\n" +
                    "</output_format>\n" +
                    "</prompt>";

            // 拼接所有内容
            StringBuilder contentBuilder = new StringBuilder();
            if (!isEmpty(txTitle)) {
                contentBuilder.append("标题：").append(txTitle);
            }
            if (!isEmpty(objectOriginTxContent)) {
                if (contentBuilder.length() > 0) {
                    contentBuilder.append("；");
                }
                contentBuilder.append("主贴内容：").append(objectOriginTxContent);
            }
            if (!isEmpty(txContent)) {
                if (contentBuilder.length() > 0) {
                    contentBuilder.append("；");
                }
                contentBuilder.append("评论内容：").append(txContent);
            }

            // 构造用户提示词
            String userPrompt = "分析给定内容中作者针对" + brandKeyword + "这个汽车品牌的情感倾向,分别判断主贴内容的情感和评论内容的情感。其中，待分析的帖子内容为：'" + contentBuilder.toString() + "'";

            // 构造请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("systemPrompt", systemPrompt);
            requestBody.put("userPrompts", Arrays.asList(userPrompt));
            requestBody.put("model", "doubao");

            Map<String, Object> stat = new HashMap<>();
            stat.put("topic", brandKeyword + "汽车情感分析");
            requestBody.put("stat", stat);

            // 发送HTTP请求
            String jsonBody = objectMapper.writeValueAsString(requestBody);

            RequestBody body = RequestBody.create(jsonBody, MediaType.get("application/json; charset=utf-8"));
            Request request = new Request.Builder()
                    .url(llmApiUrl)
                    .post(body)
                    .addHeader("X-App", "shujie")
                    .addHeader("X-User-Id", "0")
                    .addHeader("Accept", "application/json")
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 使用现有的HttpUtil发送请求
            Map<String, Object> response = com.tarsocial.bigital.kol.service.util.HttpUtil.requestJson(request, Map.class);

            if (response != null && response.get("code").equals(200)) {
                String responseBody = (String) response.get("data");
                if (!isEmpty(responseBody)) {
                    // 解析响应结果
                    Map<String, Object> sentimentResult = objectMapper.readValue(responseBody, Map.class);

                    // 处理空值，默认返回"中性"
                    String mainSentiment = (String) sentimentResult.get("主贴情感");
                    String commentSentiment = (String) sentimentResult.get("评论情感");
                    Object feelingWords = sentimentResult.get("感受词");

                    if (isEmpty(mainSentiment)) {
                        sentimentResult.put("主贴情感", "中性");
                    }
                    if (isEmpty(commentSentiment)) {
                        sentimentResult.put("评论情感", "中性");
                    }
                    if (feelingWords == null) {
                        sentimentResult.put("感受词", new ArrayList<>());
                    }

                    return sentimentResult;
                }
            }

            // 如果API调用失败或没有返回内容，返回默认的"中性"情感
            Map<String, Object> defaultResult = new HashMap<>();
            defaultResult.put("主贴情感", "中性");
            defaultResult.put("评论情感", "中性");
            defaultResult.put("感受词", new ArrayList<>());
            return defaultResult;

        } catch (Exception e) {
            System.err.println("调用LLM API失败: " + e.getMessage());
            // 异常情况下也返回默认的"中性"情感
            Map<String, Object> defaultResult = new HashMap<>();
            defaultResult.put("主贴情感", "中性");
            defaultResult.put("评论情感", "中性");
            defaultResult.put("感受词", new ArrayList<>());
            return defaultResult;
        }
    }

    /**
     * 根据 kol_data 数据生成 JSONL 文件并上传到 OSS
     * 生成的JSONL文件按照ES _source结构，包含情感分析结果映射
     * @param whereCondition 查询条件，如："main_post_sentiment = '正面'"
     * @param limit 限制导出数量，默认1000
     * @param fileName 文件名，不传则自动生成
     * @return 处理结果
     */
    public Map<String, Object> exportKolDataToOss(String whereCondition, Integer limit, String fileName) {
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Map<String, Object> result = new HashMap<>();
        int exported = 0;

        try {
            // 设置默认值
            int exportLimit = (limit != null && limit > 0) ? limit : 1000;
            String finalFileName = isEmpty(fileName) ?
                "sentiment_data_export_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".jsonl" :
                fileName;


            // 构造查询SQL - 查询所有需要的字段
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("SELECT kw_url, kw_platformName, date_publishedAt, tkw_nickname, kw_userId, tx_title, tx_content, emotion, ");
            queryBuilder.append("origin_date_publishedAt, origin_tkw_nickname, origin_kwUserId, origin_tx_content, origin_emotion, ");
            queryBuilder.append("origin_long_commentsCount, origin_long_likeCount, origin_long_repostsCount, origin_long_collectCount, ");
            queryBuilder.append("main_post_sentiment, comment_sentiment ");
            queryBuilder.append("FROM ").append(database).append(".kol_data");

            if (!isEmpty(whereCondition)) {
                queryBuilder.append(" WHERE ").append(whereCondition);
            }

            queryBuilder.append(" LIMIT ").append(exportLimit);

            String query = queryBuilder.toString();
            List<Map> rows = Util.query(sqlConf, query);

            if (rows.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有找到符合条件的数据");
                return result;
            }

            // 生成JSONL内容 - 按照ES _source结构
            StringBuilder jsonlContent = new StringBuilder();
            for (Map row : rows) {
                Map<String, Object> sourceData = buildSourceStructure(row);
                String jsonLine = objectMapper.writeValueAsString(sourceData);
                jsonlContent.append(jsonLine).append("\n");
                exported++;
            }

            // 上传到OSS
            String ossUrl = uploadToOss(jsonlContent.toString(), finalFileName);

            result.put("success", true);
            result.put("exported", exported);
            result.put("fileName", finalFileName);
            result.put("ossUrl", ossUrl);
            result.put("timestamp", now);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("exported", exported);
        }

        return result;
    }

    /**
     * 构建ES _source结构的数据
     * @param row 数据库查询结果行
     * @return ES _source结构的Map
     */
    private Map<String, Object> buildSourceStructure(Map row) {
        Map<String, Object> sourceData = new HashMap<>();

        // 基础字段
        sourceData.put("kw_url", trimStr(row.get("kw_url")));
        sourceData.put("kw_platformName", trimStr(row.get("kw_platformName")));
        sourceData.put("date_publishedAt", trimStr(row.get("date_publishedAt")));
        sourceData.put("tx_title", trimStr(row.get("tx_title")));
        sourceData.put("tx_content", trimStr(row.get("tx_content")));

        // object_user 结构
        Map<String, Object> objectUser = new HashMap<>();
        objectUser.put("tkw_nickname", trimStr(row.get("tkw_nickname")));
        objectUser.put("kw_userId", trimStr(row.get("kw_userId")));
        sourceData.put("object_user", objectUser);

        // object_emotionLabel 结构 - 使用主贴情感
        Map<String, Object> objectEmotionLabel = new HashMap<>();
        String mainPostSentiment = trimStr(row.get("main_post_sentiment"));
        objectEmotionLabel.put("kw_emotion", isEmpty(mainPostSentiment) ? trimStr(row.get("emotion")) : mainPostSentiment);
        sourceData.put("object_emotionLabel", objectEmotionLabel);

        // object_origin 结构
        Map<String, Object> objectOrigin = new HashMap<>();
        objectOrigin.put("date_publishedAt", trimStr(row.get("origin_date_publishedAt")));
        objectOrigin.put("tx_content", trimStr(row.get("origin_tx_content")));
        objectOrigin.put("long_commentsCount", row.get("origin_long_commentsCount"));
        objectOrigin.put("long_likeCount", row.get("origin_long_likeCount"));
        objectOrigin.put("long_repostsCount", row.get("origin_long_repostsCount"));
        objectOrigin.put("long_collectCount", row.get("origin_long_collectCount"));

        // object_origin.object_user 结构
        Map<String, Object> originObjectUser = new HashMap<>();
        originObjectUser.put("tkw_nickname", trimStr(row.get("origin_tkw_nickname")));
        originObjectUser.put("kw_userId", trimStr(row.get("origin_kwUserId")));
        objectOrigin.put("object_user", originObjectUser);

        // object_origin.object_emotionLabel 结构 - 使用评论情感
        Map<String, Object> originObjectEmotionLabel = new HashMap<>();
        String commentSentiment = trimStr(row.get("comment_sentiment"));
        originObjectEmotionLabel.put("kw_emotion", isEmpty(commentSentiment) ? trimStr(row.get("origin_emotion")) : commentSentiment);
        objectOrigin.put("object_emotionLabel", originObjectEmotionLabel);

        sourceData.put("object_origin", objectOrigin);

        /*// 感受词字段
        Object feelingWords = row.get("feeling_words");
        if (feelingWords != null) {
            try {
                // 如果是JSON字符串，解析为数组
                if (feelingWords instanceof String) {
                    sourceData.put("feeling_words", objectMapper.readValue((String) feelingWords, Object.class));
                } else {
                    sourceData.put("feeling_words", feelingWords);
                }
            } catch (Exception e) {
                sourceData.put("feeling_words", new ArrayList<>());
            }
        } else {
            sourceData.put("feeling_words", new ArrayList<>());
        }*/

        return sourceData;
    }

    /**
     * 上传内容到阿里云OSS
     * @param content 文件内容
     * @param fileName 文件名
     * @return OSS文件URL
     */
    private String uploadToOss(String content, String fileName) throws Exception {
        OSS ossClient = null;
        try {
            // 创建OSS客户端
            ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET);

            // 将内容转换为输入流
            InputStream inputStream = new ByteArrayInputStream(content.getBytes("UTF-8"));

            // 上传文件
            String objectKey = "sentiment_data_exports/" + fileName;
            ossClient.putObject(OSS_BUCKET_NAME, objectKey, inputStream);

            // 返回文件URL
            return "https://" + OSS_BUCKET_NAME + "." + OSS_ENDPOINT + "/" + objectKey;

        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}
