package com.tarsocial.bigital.kol.service.util

import lombok.extern.slf4j.Slf4j
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody

@Slf4j
class BotUtil {

    private static final String default_webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0109722c-619b-4f28-a243-c25873f0b2d1"


    static void send(String msg, String webhook = default_webhook) {
        if (!msg) return

        def req = new Request.Builder()
                .url(webhook)
                .post(RequestBody.create(MediaType.parse("application/json"), "{\"msgtype\":\"text\",\"text\":{\"content\":\"${msg}\"}}"))
                .build()
        HttpUtil.request(req)
    }


    static void main(String[] args) {
        send("hello")
    }

}
