package com.tarsocial.bigital.kol.service.util

import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody

class QuarkUtil {
    private static Map config = [
            team : 991,
            name : "<PERSON><PERSON>_ka<PERSON><EMAIL>",
            password : "09#\$^ffsY"
    ]

    static String quarkLoginToken(){
        def requestMap = [
                "username": config.name,"password": config.password,"logType":"INNER","appName":"datacenter","groupType":"DEPARTMENT"
        ]
        Request req = new Request.Builder()
                .url("http://dag-api.tarsocial.com/web/authenticate?owner=QUARK")
                .post(RequestBody.create(JsonUtil.toJson(requestMap), MediaType.parse("application/json")))
                .header("Content-Type", "application/json")
                .build()
        def ret = HttpsUtil.requestString(req, 3)
        JsonUtil.fromJson(ret.body, Map).data.token

    }

    // quark规则转dsl
    static Map dsl( String ruleId = null){
        def url = "http://data-center-ztweb.tarsocial.com/rule/dsl?ruleIds=${ruleId}"
        Request req = new Request.Builder()
                .url(url)
                .header("Content-Type", "application/json")
                .header("cache-control", "no-cache")
                .header("Authorization", "Bearer ${quarkLoginToken()}")
                .build()
        HttpsUtil.requestString(req, 3)
    }

    public static String cleanDSL(String dsl){
        dsl.replaceAll("\"adjust_pure_negative\" : true,", "").replaceAll("\"include_lower\":true,\"include_upper\":true,", "")
    }


    static void main(String[] args) {
        def dsl = dsl("145322")
        println(dsl)
    }
}
