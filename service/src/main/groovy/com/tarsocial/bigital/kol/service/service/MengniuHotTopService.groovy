package com.tarsocial.bigital.kol.service.service

import com.alibaba.fastjson.JSON
import com.tarsocial.bigital.kol.service.util.BotUtil
import com.tarsocial.bigital.kol.service.util.HttpUtil
import com.tarsocial.bigital.kol.service.util.JsonUtil
import com.tarsocial.bigital.kol.service.util.Util
import groovy.json.JsonSlurper
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.RequestBody
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.time.LocalDateTime
import java.util.concurrent.Executor
import java.util.concurrent.atomic.AtomicInteger

@Service
class MengniuHotTopService {

    @Value('${mengniu.datasource.url}')
    private String url
    @Value('${mengniu.datasource.user}')
    private String user
    @Value('${mengniu.datasource.password}')
    private String password
    private Map sqlConf

    @Resource
    @Qualifier("asyncExecutor")
    private Executor asyncExecutor

    @PostConstruct
    void init() {
        sqlConf = [
                driver  : 'org.postgresql.Driver',
                host    : url,
                port    : 3306,
                db      : 'gsdb',
                user    : user,
                password: password
        ]
    }

    String performance(String hotDate) {
        def results = new HashMap()
        if (hotDate == null) {
            LocalDateTime.now().format('yyyyMMdd')
        }
        List<Map> data = Util.query(sqlConf, """
            select topic,
                   ln(max(max_hot) + 1)          hot_index,
                   ln(count(1) + 1)              buzz_index,
                   ln(max(long_interaction) + 1) interaction_index,
                   count(CASE WHEN kw_platform = 'weibo' THEN 1 END) as weibo_count,
                   count(CASE WHEN kw_platform = 'douyin' THEN 1 END) as douyin_count,
                   count(CASE WHEN kw_platform = 'xiaohongshu' THEN 1 END) as xiaohongshu_count,
                   count(1) as buzz
            from hot_topic.result_${hotDate}
            group by topic        
        """)

        def maxHotIndex = ((data.max { it.hot_index } ?: ['hot_index': 0])?.hot_index)?:0
        def maxBuzzIndex = ((data.max { it.buzz_index } ?: ['buzz_index': 0])?.buzz_index)?:0
        def maxInteractionIndex = ((data.max { it.interaction_index } ?: ['interaction_index': 0])?.interaction_index)?:0
        def minHotIndex = ((data.min { it.hot_index } ?: ['hot_index': 0])?.hot_index)?:0
        def minBuzzIndex = ((data.min { it.buzz_index } ?: ['buzz_index': 0])?.buzz_index)?:0
        def minInteractionIndex = ((data.min { it.interaction_index } ?: ['interaction_index': 0])?.interaction_index)?:0

        data.each { Map map ->
            def topic = map['topic']
            def d = [
                    hotName : topic,
                    hotTime : hotDate,
                    hotValue: 0, hotSearchIndex: 0, buzzIndex: 0, interactionIndex: 0
            ]
            if (map.hot_index != null && 0 != (maxHotIndex - minHotIndex)) {
                d['hotSearchIndex'] = (map.hot_index - minHotIndex) / (maxHotIndex - minHotIndex) * 100
            }
            if(map.buzz_index != null && 0 != (maxBuzzIndex - minBuzzIndex)){
                d['buzzIndex'] = (map.buzz_index - minBuzzIndex) / (maxBuzzIndex - minBuzzIndex) * 100
            }
            if(map.interaction_index != null && 0 != (maxInteractionIndex - minInteractionIndex)){
                d['interactionIndex'] = (map.interaction_index - minInteractionIndex) / (maxInteractionIndex - minInteractionIndex) * 100
            }
            d['hotValue'] = (d.hotSearchIndex + d.buzzIndex + d.interactionIndex) / 3

            results.put(topic,d)
        }
        return JSON.toJSONString(results)
    }


    def getTopicList(String hotDate, String batchNo, boolean isRelevant = true) {
        def hotTime = LocalDateTime.now().format('yyyyMMdd')
        if (hotDate) hotTime = hotDate
        hotDate = hotTime.replaceAll("-", "")

        def results = []

        List<Map> data = Util.query(sqlConf, """
            select topic,platforms,topic_description,
                   ln(max(max_hot) + 1)          hot_index,
                   ln(count(1) + 1)              buzz_index,
                   ln(max(long_interaction) + 1) interaction_index,
                   count(1) as buzz
            from hot_topic.result_${hotDate}
            ${isRelevant? "where is_relevant = true": ""}
            group by topic,platforms,topic_description        
        """)

        // 改回原来发生事件逻辑
        List<Map> firstDate = Util.query(sqlConf, """
            select topic,min(date_published) from hot_topic.result_${hotDate} group by topic
        """)
        def entries = firstDate.collectEntries {
            it -> [it.topic, it.min]
        }


        def maxHotIndex = ((data.max { it.hot_index } ?: ['hot_index': 0])?.hot_index)?:0
        def maxBuzzIndex = ((data.max { it.buzz_index } ?: ['buzz_index': 0])?.buzz_index)?:0
        def maxInteractionIndex = ((data.max { it.interaction_index } ?: ['interaction_index': 0])?.interaction_index)?:0
        def minHotIndex = ((data.min { it.hot_index } ?: ['hot_index': 0])?.hot_index)?:0
        def minBuzzIndex = ((data.min { it.buzz_index } ?: ['buzz_index': 0])?.buzz_index)?:0
        def minInteractionIndex = ((data.min { it.interaction_index } ?: ['interaction_index': 0])?.interaction_index)?:0

        data.each { Map map ->
            def topic = map['topic']
            def firstTime = entries.get(topic)
            if (firstTime == null) {
                return
            }
            def d = [
                    //occurred_time 查询数据库最早的帖子发布时间
                    batchNo     : batchNo,
                    hotName     : topic,
                    occurredTime: firstTime.replaceAll(/^(\d{4}-\d{2}-\d{2})$/, '$1 00:00:00'),
                    hotDate     : hotTime.replaceAll(/v\d+_(\d{4})-?(\d{2})-?(\d{2})/, '$1-$2-$3'),
                    platforms   : map['platforms'] ? transformPlatform(JsonUtil.toList(map['platforms'])) : null,
                    hotValue    : 0, hotSearchIndex: 0, buzzIndex: 0, interactionIndex: 0
            ]
            if (map.hot_index != null && 0 != (maxHotIndex - minHotIndex)) {
                d['hotSearchIndex'] = (map.hot_index - minHotIndex) / (maxHotIndex - minHotIndex) * 100
            }
            if(map.buzz_index != null && 0 != (maxBuzzIndex - minBuzzIndex)){
                d['buzzIndex'] = (map.buzz_index - minBuzzIndex) / (maxBuzzIndex - minBuzzIndex) * 100
            }
            if(map.interaction_index != null && 0 != (maxInteractionIndex - minInteractionIndex)){
                d['interactionIndex'] = (map.interaction_index - minInteractionIndex) / (maxInteractionIndex - minInteractionIndex) * 100
            }
            d['hotValue'] = (d.hotSearchIndex + d.buzzIndex + d.interactionIndex) / 3
            results << d
        }

        results
    }


    def getTopicDetails(String hotDate, String hotName){
        def hotTime = LocalDateTime.now().format('yyyyMMdd')
        if (hotDate) hotTime = hotDate
        hotDate = hotTime.replaceAll("-", "")

        if (hotDate) hotDate = hotDate.replaceAll("-", "")

        List<Map> data = Util.query(sqlConf, """
            select platforms,topic,
                   count(CASE WHEN kw_platform = 'weibo' THEN 1 END) as weibo_count,
                   count(CASE WHEN kw_platform = 'douyin' THEN 1 END) as douyin_count,
                   count(CASE WHEN kw_platform = 'xiaohongshu' THEN 1 END) as xiaohongshu_count,
                   count(CASE WHEN kw_platform = 'weixin' THEN 1 END) as weixin_count,
                   count(1) as buzz,
                   count(CASE WHEN kw_gender = 'f' THEN 1 END) as gender_f_count,
                   count(CASE WHEN kw_gender = 'm' THEN 1 END) as gender_m_count,
                   count(CASE WHEN kw_age >= 18 and kw_age <= 24 THEN 1 END) as age_18_24_count,
                   count(CASE WHEN kw_age >= 25 and kw_age <= 30 THEN 1 END) as age_25_30_count,
                   count(CASE WHEN kw_age >= 31 and kw_age <= 35 THEN 1 END) as age_31_35_count,
                   count(CASE WHEN kw_age >= 36 and kw_age <= 40 THEN 1 END) as age_36_40_count,
                   count(CASE WHEN kw_age >= 41 and kw_age <= 50 THEN 1 END) as age_41_50_count,
                   count(CASE WHEN kw_age >= 41 and kw_age <= 50 THEN 1 END) as age_41_50_count,
                   count(CASE WHEN kw_age >= 51 THEN 1 END) as age_51_count
            from hot_topic.result_${hotDate} where topic = '${hotName.replaceAll("'", "''")}'
            group by platforms,topic        
        """)

        List<Map> thisTopicList = Util.query(sqlConf, """ select * from hot_topic.algorithm_result_${hotDate} where topic = '${hotName.replaceAll("'", "''")}' """)
        if(!thisTopicList || thisTopicList.size() == 0) return
        Map thisTopic = thisTopicList.get(0)
        def jsonArray = JSON.parseArray(thisTopic.get("post_ids").toString())
        def postDetail = []
        jsonArray.forEach {
            def thisPostList = Util.query(sqlConf, """ select tx_title, tx_content from hot_topic.mengniu_hot_topic_post_${hotDate} where kw_id = '${it}' """)
            if(thisPostList){
                def p = [
                        title  : thisPostList.get(0).get("tx_title"),
                        content: thisPostList.get(0).get("tx_content")
                ]
                postDetail.add(p)
            }
        }


        List<Map> provinces = Util.query(sqlConf, """
            SELECT kw_province, COUNT(1) AS count
            FROM hot_topic.result_${hotDate}
            WHERE topic = '${hotName.replaceAll("'", "''")}'
                and kw_province is not null
            GROUP BY kw_province
            ORDER BY count DESC
            LIMIT 10;
        """)

        List result = Util.query(sqlConf, """
            select  result_json 
            from hot_topic.log
            where data_time = '${hotDate}'
            order by id DESC
            limit 1;
        """)

        def map = data.get(0)
        def totalBuzz = map['buzz']

        def genderCount = map['gender_m_count'] + map['gender_f_count']
        def ageCount = map['age_18_24_count'] + map['age_25_30_count'] + map['age_31_35_count'] + map['age_36_40_count'] + map['age_41_50_count'] + map['age_51_count']
        def platformCount = map['douyin_count'] + map['weibo_count'] + map['xiaohongshu_count'] + map['weixin_count']
        String crowd = """- 性别分布：男（${getPct(map['gender_m_count'], genderCount)}）、 女（${getPct(map['gender_f_count'], genderCount)}）
- 年龄分布：18-24岁（${getPct(map['age_18_24_count'], ageCount)}）、25-30岁（${getPct(map['age_25_30_count'], ageCount)}）、31-35岁（${getPct(map['age_31_35_count'], ageCount)}）、36-40岁（${getPct(map['age_36_40_count'], ageCount)}）、41-50岁（${getPct(map['age_41_50_count'], ageCount)}）、51+岁（${getPct(map['age_51_count'], ageCount)}）"""


        def regionsList = []
        String regions = """TOP10城市分布为："""
        provinces.forEach {
            String product = """${it['kw_province']}（${getPct(it['count'], totalBuzz)}）、"""
            regions += product
            regionsList.add([name: it['kw_province'], ratio: it['count'] / totalBuzz])
        }

        String buzzRatio = """抖音（${getPct(map['douyin_count'], platformCount)}）、微博（${getPct(map['weibo_count'], platformCount)}）、小红书（${getPct(map['xiaohongshu_count'], platformCount)})、微信（${getPct(map['weixin_count'], platformCount)}）"""


        def relatedHot = Util.query(sqlConf, """
            select arr_topics
            from hot_topic.result_${hotDate}
            where topic = '${hotName.replaceAll("'", "''")}'
            group by arr_topics
            limit 10
        """).collect{it.arr_topics.toString() - '}' - '{'}.findAll{ !it.contains('null')}

        def marks = Util.query(sqlConf, """
            select b.topic topic,b.marks marks from (select * from hot_topic.topic_details where hot_date = 'v2_20250309')  a left join (select topic,STRING_AGG(distinct mark, ', ') AS marks from hot_topic.result_v2_20250309 group by topic) b on a.hot_name = b.topic where b.topic = '${hotName.replaceAll("'", "''")}'
        """).find {it.topic == hotName}?.marks

        def d = [
                hotName       : hotName,
                hotDate       : hotDate.replaceAll(/v\d+_(\d{4})-?(\d{2})-?(\d{2})/, '$1-$2-$3'),
                hotDescription: (thisTopic['topic_description']?:'').take(300),
                relatedHot    : relatedHot,
                is_relevant   : thisTopic.is_relevant,
                reason          : thisTopic.reason,
                marketing_approach : thisTopic.marketing_approach,
                relatedPosts  : postDetail,
                regions       : regionsList,
                crowd         : [
                        age   : [
                                [name: '18-24', ratio: ageCount && map['age_18_24_count'] && ageCount != 0 ? map['age_18_24_count'] / ageCount : null],
                                [name: '25-30', ratio: ageCount && map['age_25_30_count'] && ageCount != 0 ? map['age_25_30_count'] / ageCount : null],
                                [name: '31-35', ratio: ageCount && map['age_31_35_count'] && ageCount != 0 ? map['age_31_35_count'] / ageCount : null],
                                [name: '36-40', ratio: ageCount && map['age_36_40_count'] && ageCount != 0 ? map['age_36_40_count'] / ageCount : null],
                                [name: '41-50', ratio: ageCount && map['age_41_50_count'] && ageCount != 0 ? map['age_41_50_count'] / ageCount : null],
                                [name: '51+', ratio: ageCount && map['age_51_count'] && ageCount != 0 ? map['age_51_count'] / ageCount : null]
                        ],
                        gender: [
                                [name: '男', ratio: genderCount && map['gender_m_count'] && genderCount != 0 ? map['gender_m_count'] / genderCount : null],
                                [name: '女', ratio: genderCount && map['gender_f_count'] && genderCount != 0 ? map['gender_f_count'] / genderCount : null]
                        ]
                ],
                buzzRatio     : [
                        [name: '微博', ratio: platformCount && map['weibo_count'] && platformCount != 0 ? map['weibo_count'] / platformCount : null],
                        [name: '抖音', ratio: platformCount && map['douyin_count'] && platformCount != 0 ? map['douyin_count'] / platformCount : null],
                        [name: '小红书', ratio: platformCount && map['xiaohongshu_count'] && platformCount != 0 ? map['xiaohongshu_count'] / platformCount : null],
                        [name: '微信', ratio: platformCount && map['weixin_count'] && platformCount != 0 ? map['weixin_count'] / platformCount : null]
                ],
                regionsDesc   : regions.dropRight(1),
                crowdDesc     : crowd,
                buzzRatioDesc : buzzRatio,
                platforms     : map['platforms'] ? transformPlatform(JsonUtil.toList(map['platforms'])) : [],
                hotValue      : 0, hotSearchIndex: 0, buzzIndex: 0, interactionIndex: 0
        ]

        if(marks?.contains('red_specify')) d['platforms'].add('红书定向')

        if (result != null) {
            // 创建 JsonSlurper 实例
            def jsonSlurper = new JsonSlurper()
            def resultMap = jsonSlurper.parseText(result.get(0).get("result_json"))
            d['hotSearchIndex'] = resultMap.getAt(hotName).getAt("hotSearchIndex")
            d['buzzIndex'] = resultMap.getAt(hotName).getAt("buzzIndex")
            d['interactionIndex'] = resultMap.getAt(hotName).getAt("interactionIndex")
            d['hotValue'] = resultMap.getAt(hotName).getAt("hotValue")
        }

        d
    }


    String saveFinalResult(String hotDate, boolean isCleanData = true) {
        // 只有v2 需要存储
        def start = System.currentTimeMillis()
        final AtomicInteger queue = new AtomicInteger()
        if(!hotDate.startsWith('v2_')) return
        if(isCleanData) Util.execute(sqlConf, "delete from hot_topic.topic_details where hot_date = '${hotDate}'")
        def savedTopic = Util.query(sqlConf, "select hot_name from hot_topic.topic_details where hot_date = '${hotDate}'").collect { it.hot_name }
        getTopicList(hotDate, '').each {
            if(savedTopic.contains(it.hotName)) return
            queue.incrementAndGet()
            asyncExecutor.execute(() -> {
                try{
                    topicDetailsSupplement(hotDate, it.hotName, it.occurredTime)
                }catch (Exception e){
                    e.printStackTrace()
                }finally {
                    queue.decrementAndGet()
                }
            })
        }
        while (queue.get() > 0 && (System.currentTimeMillis() - start) < 1800000) {
            Thread.sleep(30000)
            println("queue size: ${queue.get()}; sleep 30s;")
        }
        BotUtil.send("$hotDate -> AI描述剩余未跑: ${queue.get()};" , "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=35c63f17-332f-44b3-b7b7-844b67d28bd6")

        return "success"
    }

    private Map topicDetailsSupplement(String hotDate, String hotName, String occurredTime) {
        def detail = getTopicDetails(hotDate, hotName)
        String regionsDesc = detail.regionsDesc
        String crowdDesc = detail.crowdDesc
        detail.regionsDesc = regionsDesc + '\n' + getAIDesc(regionsDesc)
        detail.crowdDesc = crowdDesc + '\n' + getAIDesc(crowdDesc)
//        detail.occurredTime = occurredTime
        // 存储到数据库
        Util.execute(sqlConf, """
                        insert into hot_topic.topic_details (hot_name,hot_date,detail)
                        values ('${hotName.replaceAll("'", "''")}','${detail.hotDate}','${JsonUtil.toJson(detail).replaceAll("'", "''")}')
                    """)

        detail
    }

    boolean log(String hotDate) {
        return Util.query(sqlConf, """
            select * from hot_topic.log
            where data_time = '${hotDate}' and status = 'finish'
        """)

    }


    Map getFinalResult(String hotDate, String hotName) {
        def details = Util.query(sqlConf, """
            select detail
            from hot_topic.topic_details
            where hot_name = '${hotName.replaceAll("'", "''")}' and hot_date = '${hotDate}'
        """)

        if (details.size() == 0) {
            return topicDetailsSupplement(hotDate, hotName, "")
        }
        def map = JsonUtil.toMap(details.get(0).get("detail"))
        map
    }

    private static String getAIDesc(String desc) {
        if(desc == null || desc.trim() == '') return ''
        def body = [
                systemPrompt: "请为以下社交平台上热点话题的声量分布给出洞察结果",
                userPrompts : [desc],
                model       : "doubao"
        ]
        def req = new Request.Builder()
                .url("https://llm.tarsocial.com/bridge/v2")
                .post(RequestBody.create(MediaType.parse("application/json"), JsonUtil.toJson(body)))
                .header("X-App", "mengniu_hot_topic")
                .build()
        def res = HttpUtil.requestString(req)
        JsonUtil.toMap(res.body).data
    }


    static List transformPlatform(List<String> platform) {
        List result = new ArrayList()
        if (platform == null) {
            return result
        }
        if (platform.contains("douyin"))
            result.add("抖音")
        if (platform.contains("weibo"))
            result.add("微博")
        if (platform.contains("xiaohongshu"))
            result.add("小红书")
        if (platform.contains("weixin"))
            result.add("微信")
        return result
    }

    static String getPct(def numerator, def denominator) {
        if (denominator && numerator && denominator != 0) {
            def number = numerator * 100 / denominator
            return """${number.round(2)}%"""
        }
        return "0%";
    }


}
