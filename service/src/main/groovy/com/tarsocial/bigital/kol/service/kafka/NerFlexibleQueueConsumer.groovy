package com.tarsocial.bigital.kol.service.kafka

import com.alibaba.fastjson.JSON
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.tarsocial.bigital.kol.common.domain.dto.EmotionPipelineDto
import com.tarsocial.bigital.kol.common.domain.dto.FlexibleMessageDto
import com.tarsocial.bigital.kol.common.domain.request.NerFlexibleCalculateRequest
import com.tarsocial.bigital.kol.service.util.KafkaUtil
import org.apache.commons.lang3.StringUtils
import org.apache.kafka.clients.consumer.Consumer
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.producer.Producer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Service

import javax.annotation.PostConstruct
import javax.annotation.Resource
import java.time.Duration
import java.util.concurrent.TimeUnit

@Service
class NerFlexibleQueueConsumer {

    private static final Logger logger = LoggerFactory.getLogger(NerFlexibleQueueConsumer.class)

    @Resource
    private RedisTemplate redisTemplate

    @Value('${spring.kafka.topic.flexible-rapid-in}')
    private String topicInHigh
    @Value('${spring.kafka.topic.flexible-rapid-out}')
    private String topicOutHigh
    @Value('${spring.kafka.consumer.group-id.flexible-high}')
    private String groupIdHigh

    private static Producer<String, String> producer = KafkaUtil.createProducer()
    private static Consumer<String, String> consumerHigh

    @PostConstruct
    public void init() {
        logger.info("NerFlexibleQueueConsumer init() ....")
        consumerHigh = KafkaUtil.createConsumer(topicOutHigh, groupIdHigh, 'latest', 1)
        startConsuming()
    }

    void push(List<FlexibleMessageDto> dataList, NerFlexibleCalculateRequest req) {
        for (FlexibleMessageDto messageDto : dataList) {
            String key = messageDto.getNo()
            List<Object> modelPipeline = new ArrayList<>();
            if (req.getEntModelPipeline() != null) {
                modelPipeline.add(req.getEntModelPipeline())
            }
            if (req.getRelModelPipeline() != null) {
                modelPipeline.add(req.getRelModelPipeline())
            }

            if (Boolean.TRUE == req.getSentiment()) {
                modelPipeline.add(new EmotionPipelineDto())
            }
            messageDto.setModel_pipeline(modelPipeline)
            String value = JSON.toJSONString(messageDto)
            try {
                KafkaUtil.push(producer, topicInHigh, key, value)
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    // 启动消费者，手动拉取消息并处理
    public void startConsuming() {
        new Thread(() -> {
            while (true) {
//                logger.info("consumerHigh poll ...")
                ConsumerRecords<String, String> records = consumerHigh.poll(Duration.ofMillis(2000))
                if (!records.isEmpty()) {
                    for (ConsumerRecord<String, String> record : records) {
                        try {
                            String no = JSON.parseObject(record.value()).getString("no");
                            if (no) {
                                ObjectMapper mapper = new ObjectMapper();
                                // Step 1: 解析JSON字符串为可编辑的ObjectNode
                                JsonNode rootNode = mapper.readTree(record.value());
                                ObjectNode objectNode = (ObjectNode) rootNode;
                                if (objectNode.has("tx_content")) {
                                    objectNode.remove("tx_content");
                                }
                                if (objectNode.has("tx_ocr")) {
                                    objectNode.remove("tx_ocr");
                                }
                                if (objectNode.has("tx_clean")) {
                                    objectNode.remove("tx_clean");
                                }
                                String modifiedJson = mapper.writeValueAsString(objectNode);
                                redisTemplate.opsForValue().set(no, modifiedJson, Duration.ofHours(6).getSeconds(), TimeUnit.SECONDS);
//                                logger.info("add redis NerFlexible no-> {} ", no)
                            }
                        } catch (Exception e) {
                            logger.error("Error processing record: {}", record, e);
                        }
                    }
                } else {
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        logger.error("Consumer interrupted during sleep!");
                        break;
                    }
                }
            }
        }).start();
    }


}
