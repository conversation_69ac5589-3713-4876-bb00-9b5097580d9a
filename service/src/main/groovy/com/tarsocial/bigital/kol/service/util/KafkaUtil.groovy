package com.tarsocial.bigital.kol.service.util

import org.apache.kafka.clients.consumer.Consumer
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.StringDeserializer
import org.apache.kafka.common.serialization.StringSerializer

import java.time.Duration

public class KafkaUtil {
    public static final String bootstrapServers = "10.66.63.122:9092,10.66.63.52:9092,10.66.63.133:9092"

    static Consumer<String, String> createConsumer(String topic, String group, String autoOffsetReset = 'latest', int maxPollRecords = 1000, String servers = bootstrapServers) {
        final Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, servers)
        props.put(ConsumerConfig.GROUP_ID_CONFIG, group);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "true")
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords + "")
//        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords)
        //earliest 当分区下有已提交的offset时，从提交的offset开始消费；无提交的offset时，从头开始消费。
        //latest 当分区下有已提交的offset时，从提交的offset开始消费；无提交的offset时，消费新产生的该分区下的数据。
        //none 当该topic下所有分区中存在未提交的offset时，抛出异常。
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, autoOffsetReset)

        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());


        // Create the consumer using props.
        final Consumer<String, String> consumer = new KafkaConsumer<>(props);

        // Subscribe to the topic.
        consumer.subscribe(Collections.singletonList(topic))
        return consumer;
    }

    static Producer<String, String> createProducer(String servers = bootstrapServers) {
        Properties props = new Properties()
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, servers)
        props.put(ProducerConfig.ACKS_CONFIG, "all")
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 100_000)
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33_554_432)
        props.put(ProducerConfig.RETRIES_CONFIG, 1)
        props.put(ProducerConfig.LINGER_MS_CONFIG, 3000) // 3s
        props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 3000) // 3s
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName())
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName())
        return new KafkaProducer(props)
    }

    static String assembleGroupId(String topic = "topic", String groupId = "default") {
        return "GID_${topic}_${groupId}".toString()
    }

    static push(Producer producer = createProducer(), String topic, String key, String data) {
        producer.send(new ProducerRecord(topic, key, data))
    }

    static void pushTestMsg(String topic, int size = 10000) {
        Producer<String, String> producer = createProducer()
        (0..size).each {
            producer.send(new ProducerRecord(topic, 'test' + System.currentTimeMillis(), "{\"topic\": \"test_out\", \"data\": \"${topic}_test${it}\"}".toString()))
        }
        producer.close()
    }


    static void main(String[] args) {
        pushTestMsg('ner_low2', 1000)

        Thread thread1 = new Thread(new Runnable() {
            @Override
            void run() {
                pushTestMsg('ner_high')
            }
        })
        thread1.start()
        Thread thread2 = new Thread(new Runnable() {
            @Override
            void run() {
                pushTestMsg('ner_low', 1000)
            }
        })
        thread2.start()
        Thread thread3 = new Thread(new Runnable() {
            @Override
            void run() {
                pushTestMsg('ner_low2', 1000)
            }
        })
        thread3.start()
        Thread thread4 = new Thread(new Runnable() {
            @Override
            void run() {
                pushTestMsg('ner_low3', 1000)
            }
        })
        thread4.start()





//        long i
//        final Consumer<String, String> consumer = createConsumer('ner_high', "groupB", 'earliest')
//        while (true) {
//            ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000))  //1000ms是超时等待时间
//            i += records.size()
//            println "$i records received."
//            if (!records || records.isEmpty()) break
//        }
//
//        consumer.close()
    }
}
