package com.tarsocial.bigital.kol.service.controller;

import com.tarsocial.bigital.kol.common.domain.dto.DemoDto;
import com.tarsocial.bigital.kol.common.domain.request.DemoRequest;
import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.service.service.DemoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/demo")
public class DemoController {

    @Resource
    DemoService demoService;

    @PostMapping("/hello")
    public BaseResponse<DemoDto> hello(@RequestBody DemoRequest request) {
        return new BaseResponse<>(demoService.demo(request));
    }


}
