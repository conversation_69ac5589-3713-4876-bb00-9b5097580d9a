package com.tarsocial.bigital.kol.service.constants;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public enum TaskTypeEnum {

    /**
     * 直播广场
     * userId
     */
    DY_LIVE_PLAZA(145L, "DY_LIVE_PLAZA", "抖音-直播广场", 94L, "douyin"),
    /**
     * 直播粉丝画像
     * kolId
     */
    DY_LIVE_FANS(146L, "DY_LIVE_FANS", "抖音-直播粉丝画像", 94L, "douyin"),
    /**
     * 直播观众画像
     * kolId
     */
    DY_LIVE_SPECTATOR(147L, "DY_LIVE_SPECTATOR", "抖音-直播观众画像", 94L, "douyin"),
    /**
     * 直播数据概览
     * kolId
     */
    DY_LIVE_DATA(148L, "DY_LIVE_DATA", "抖音-直播数据概览", 94L, "douyin"),
    /**
     * 直播用户详情
     * userId
     */
    KS_LIVE_USER(149L, "KS_LIVE_USER", "快手-直播用户详情", 106L, "kuaishou"),

    //kolId
    DY_LIVE_LIST(151L, "DY_LIVE_LIST", "抖音-星图更新直播间列表", 94L, "douyin"),


    /**
     * 以下是短视频任务
     */

    //userId
    DY_USER_NEW(150L, "DY_USER_NEW", "抖音-星图录入新用户", 94L, "douyin"),
    //kw_secUid sec_uid
    DY_USER(107L, "DY_USER", "抖音-更新用户信息", 94L, "douyin"),
    //kolId
    DY_KOL(164L, "DY_KOL", "抖音-达人概览", 94L, "douyin"),

    //userId
    BILI_USER(107L, "BILI_USER", "B站-更新用户信息", 110L, "bilibili"),
    //userId
    BILI_KOL(154L, "BILI_KOL", "B站-达人报价", 110L, "bilibili"),
    //userId
    BILI_HUAHUO(156L, "BILI_HUAHUO", "B站-花火用户信息", 110L, "bilibili"),

    //userId
    KS_KOL(160L, "KS_KOL", "快手-更新达人报价", 106L, "kuaishou"),
    //userId
    KS_USER(107L, "KS_USER", "快手-更新用户信息", 106L, "kuaishou"),

    //userId
    RED_USER(107L, "RED_USER", "红书-更新用户信息", 4L, "xiaohongshu"),
    //userId
    RED_PAID(159L, "RED_PAID", "红书-更新指定达人30天商单作品", 4L, "xiaohongshu"),
    //userId
    RED_KOL(160L, "RED_KOL", "红书-更新达人报价", 4L, "xiaohongshu"),

    //userId
    WEIBO_USER(107L, "WEIBO_USER", "微博-更新用户信息", 102L, "weibo"),
    //userId
    WEIBO_TASK_USER(157L, "WEIBO_TASK_USER", "微博-微任务用户信息", 102L, "weibo"),

    ;


    private final Long code;

    private final String name;

    private final String desc;

    /**
     * 创建夸克任务的 platformId
     */
    private final Long platformId;

    private final String platformCode;


    TaskTypeEnum(Long code, String name, String desc, Long platformId, String platformCode) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.platformId = platformId;
        this.platformCode = platformCode;
    }


    public static TaskTypeEnum getTaskTypeByName(String name) {
        for (TaskTypeEnum taskTypeEnum : TaskTypeEnum.values()) {
            if (taskTypeEnum.getName().equals(name)) {
                return taskTypeEnum;
            }
        }
        return null;
    }
}
