package com.tarsocial.bigital.kol.service.config.sign;


import com.google.common.collect.Lists;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RequestBodyWrapperFilter implements Filter {

    /**
     * list中的请求，走原始的     filterChain.doFilter(servletRequest, servletResponse);
     */
    private static final List<String> list = Lists.newArrayList("/flexible/calculate");


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        ServletRequest request = servletRequest;
        ServletResponse  response  = servletResponse;

        if (servletRequest instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
            if (containsAny(httpServletRequest.getRequestURI(), list)) {
                filterChain.doFilter(servletRequest, servletResponse);
                return;
            }
        }

        if (request instanceof HttpServletRequest)
            request = new ReadRequestWrapper((HttpServletRequest) request);
        if (request instanceof HttpServletResponse)
            response = new WriterResponseWrapper((HttpServletResponse) response);

        filterChain.doFilter(request, response);
    }

    // 判断目标字符串是否包含 List 中的任意一个元素
    public static boolean containsAny(String target, List<String> keywords) {
        for (String keyword : keywords) {
            if (target.contains(keyword)) {
                return true; // 一旦找到包含的元素就返回 true
            }
        }
        return false; // 如果没有一个元素匹配，则返回 false
    }

}
