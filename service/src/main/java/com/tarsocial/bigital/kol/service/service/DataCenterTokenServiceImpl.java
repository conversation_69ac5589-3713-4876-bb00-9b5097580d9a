package com.tarsocial.bigital.kol.service.service;

import com.tarsocial.bigital.kol.service.config.QuarkProperties;
import com.tarsocial.config.TokenService;
import com.tarsocial.dag.framework.auth.enums.LogingTypeEnum;
import com.tarsocial.dag.framework.auth.model.ServerLoginRequest;
import com.tarsocial.dag.framework.auth.model.ServerLoginResponse;
import com.tarsocial.dag.framework.auth.spi.ServerSpi;
import com.tarsocial.usercenter.web.common.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
@Slf4j
public class DataCenterTokenServiceImpl implements TokenService {

    @Resource
    private ServerSpi serverSpi;

    @Resource
    private RedisUtils redisUtils;


    @Resource
    private QuarkProperties quarkProperties;


    @Override
    public String getToken() {
        String key = "login-token-org-" + getOrganizationId();
        Object o = redisUtils.get(key);
        if (o != null) {
            return o.toString();
        } else {
            return refreshToken();
        }
    }


    public String refreshToken() {
        String key = "login-token-org-" + getOrganizationId();
        ServerLoginRequest serverLoginRequest = new ServerLoginRequest();
        serverLoginRequest.setPassword(quarkProperties.getPassword());
        serverLoginRequest.setUsername(quarkProperties.getUsername());
        serverLoginRequest.setLogType(LogingTypeEnum.INNER);
        serverLoginRequest.setAppName("datacenter");
        ServerLoginResponse authenticate = serverSpi.authenticate(serverLoginRequest);
        redisUtils.set(key, authenticate.getToken(), 1000 * 60 * 60 * 24 * 29L);
        return authenticate.getToken();
    }

    @Override
    public String getOrganizationId() {
        return quarkProperties.getOrganizationId();
    }
}
