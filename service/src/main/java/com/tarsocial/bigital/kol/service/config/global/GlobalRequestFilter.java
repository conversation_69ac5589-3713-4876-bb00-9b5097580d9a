package com.tarsocial.bigital.kol.service.config.global;

import com.tarsocial.bigital.kol.common.domain.entity.AccessInterfaceLog;
import com.tarsocial.bigital.kol.service.service.AccessInterfaceLogService;
import com.tarsocial.bigital.kol.service.util.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class GlobalRequestFilter implements HandlerInterceptor {

    public static final String TRACE_ID = "traceId";

    @Autowired
    AccessInterfaceLogService accessInterfaceLogService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String traceId = request.getHeader(TRACE_ID);
        if (!StringUtils.hasLength(traceId)) {
            traceId = UUIDUtil.uuid();
        }
        MDC.put(TRACE_ID, traceId);
        response.addHeader(TRACE_ID, traceId);

        request.setAttribute(RequestUtils.accessRecordTime, System.currentTimeMillis());
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        //防止内存泄露
        MDC.remove(TRACE_ID);
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        AccessInterfaceLog record = accessInterfaceLogService.record(request, response, ex);
        log.info("record access interface log: {}", record);
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }

}
