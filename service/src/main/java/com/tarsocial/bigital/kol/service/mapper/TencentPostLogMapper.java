package com.tarsocial.bigital.kol.service.mapper;

import com.tarsocial.bigital.kol.common.domain.entity.tencent.Task;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.TencentPostLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TencentPostLogMapper extends com.baomidou.mybatisplus.core.mapper.BaseMapper<TencentPostLog> {
    /**
     * 批量更新
     *
     * @param list 数据集合
     * @return 更新数
     */
    int batchSave(@Param("list") List<TencentPostLog> list);
}
