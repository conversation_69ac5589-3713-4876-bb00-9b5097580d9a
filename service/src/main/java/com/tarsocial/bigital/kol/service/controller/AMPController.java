package com.tarsocial.bigital.kol.service.controller;

import com.tarsocial.bigital.kol.common.domain.request.amp.AMPFilterRequest;
import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.common.domain.response.amp.AMPKolResponse;
import com.tarsocial.bigital.kol.common.domain.response.amp.AMPPostResponse;
import com.tarsocial.bigital.kol.service.service.AmpService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/amp")
public class AMPController {

    @Resource
    private AmpService ampService;

    @PostMapping("/postList")
    public BaseResponse<List<AMPPostResponse>> postList(@RequestBody @Validated AMPFilterRequest request) {
        return new BaseResponse<>(ampService.postList(request));
    }

    @PostMapping("/kolList")
    public BaseResponse<List<AMPKolResponse>> kolList(@RequestBody @Validated AMPFilterRequest request) {
        return new BaseResponse<>(ampService.kolList(request));
    }
}
