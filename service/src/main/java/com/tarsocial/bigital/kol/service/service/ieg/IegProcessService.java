package com.tarsocial.bigital.kol.service.service.ieg;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.PublicPriceUserDto;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentIegUserInfo;
import com.tarsocial.bigital.kol.common.domain.enums.PlatformEnum;
import com.tarsocial.bigital.kol.common.domain.enums.UserTaskStatusEnum;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.PublicPriceTaskCreateRequest;
import com.tarsocial.bigital.kol.common.domain.response.SubTaskListResponse;
import com.tarsocial.bigital.kol.common.domain.response.ieg.LiveTaskCreateResponse;
import com.tarsocial.bigital.kol.common.domain.response.ieg.TaskUserDetailResponse;
import com.tarsocial.bigital.kol.service.constants.BusinessTypeEnum;
import com.tarsocial.bigital.kol.service.service.DataCenterTokenServiceImpl;
import com.tarsocial.bigital.kol.service.service.tencent.ieg.TencentIegUserInfoService;
import com.tarsocial.bigital.kol.service.util.BotUtil;
import com.tarsocial.dag.task.model.enums.TaskStatusEnum;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.entity.QuarkUserInfo;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentIegUserLog;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.IegTaskCreateRequest;
import com.tarsocial.bigital.kol.service.client.QuarkDataTaskSpi;
import com.tarsocial.bigital.kol.service.constants.Constants;
import com.tarsocial.bigital.kol.service.constants.PlatformTaskEnum;
import com.tarsocial.bigital.kol.service.constants.TaskTypeEnum;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import com.tarsocial.bigital.kol.service.service.DataCenterProcessService;
import com.tarsocial.bigital.kol.service.service.tencent.ieg.TencentIegUserLogService;
import com.tarsocial.dag.task.model.request.SubTaskListRequest;
import com.tarsocial.dag.task.model.request.TaskCreateRequest;
import com.tarsocial.enums.OperationEnum;
import com.tarsocial.request.CommonKeyAndValue;
import com.tarsocial.request.DocSourceV1Request;
import com.tarsocial.request.FilterRequest;
import com.tarsocial.response.DocSourceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName IegProcessService
 * @date 2024年03月26日
 */
@Service
@Slf4j
public class IegProcessService {

    @Resource
    private QuarkDataTaskSpi quarkDataTaskSpi;

    @Resource
    private DataCenterProcessService dataCenterProcessService;

    @Resource
    private TencentIegUserLogService userLogService;

    @Resource
    private TencentIegUserInfoService tencentIegUserInfoService;

    @Resource
    @Qualifier("asyncExecutor")
    private Executor asyncExecutor;

    @Resource
    private ApplicationContext context;

    @Resource
    private QuarkTaskService quarkTaskService;

    public List<QuarkUserInfo> pushIegUser(IegTaskCreateRequest request) {
        log.info("pushIegUser req -> {}", JSON.toJSONString(request));
        // 检查userId是否为提交过任务的userId
        List<String> userIds = checkUserId(request.getIds(), request.getPlatform());
        return quarkTaskService.pullUser(userIds, request.getPlatform());
    }

    private List<String> checkUserId(List<String> ids, String platform) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        List<String> userIds = userLogService.userIdList(ids, platform, null);
        if (CollectionUtils.isEmpty(userIds)) {
            throw new BusinessException("未获取到任务记录的userId列表：【" + StringUtils.join(ids, "、") + "】");
        }
        ids.removeIf(userIds::contains);
        if (!CollectionUtils.isEmpty(ids) && ids.size() > 0) {
            throw new BusinessException("未获取到任务记录的userId列表：【" + StringUtils.join(ids, "、") + "】");
        }
        return userIds;
    }

    public LiveTaskCreateResponse addAllTask(List<String> userIds, String platform) {
        if (!CollectionUtils.isEmpty(userIds) && userIds.size() > 50) {
            throw new BusinessException("用户ID数量超过50个！！！");
        }
        LambdaQueryWrapper<TencentIegUserLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.gt(TencentIegUserLog::getCreateTime, LocalDate.now().minusMonths(1));
        logWrapper.in(TencentIegUserLog::getType, Lists.newArrayList(TaskTypeEnum.KS_LIVE_USER.getName(), TaskTypeEnum.DY_LIVE_PLAZA.getName()));
        int count = userLogService.list(logWrapper).size();
        BotUtil.send(context.getEnvironment().getActiveProfiles()[0] + " >> IEG任务创建\nplatform:" + platform + "\n数据量:" + userIds.size() + "\n近一个月累计数据量:" + count);
        // 过滤4天内创建过的用户id
        LocalDate fourDaysAgo = LocalDate.now().minusDays(4);
        List<String> failList = userLogService.userIdList(userIds, platform, fourDaysAgo);
        userIds.removeAll(failList);

        if (!CollectionUtils.isEmpty(userIds)) {
            // 创建夸克任务
            if (PlatformTaskEnum.KUAI_SHOU.getName().equals(platform)) {
                log.info("直播用户详情 create --> start");
                // 直播用户详情 user_id
                Long taskId = quarkTaskService.createTask(TaskTypeEnum.KS_LIVE_USER, userIds);
                // 保存userId数据
                userLogService.saveUserLog(userIds, platform, TaskTypeEnum.KS_LIVE_USER, taskId, BusinessTypeEnum.IEG_LIVE.getCode());
                log.info("直播用户详情 create --> end");
            } else {
                // 抖音星图直播广场 user_id
                log.info("直播广场 create --> start");
                Long taskId1 = quarkTaskService.createTask(TaskTypeEnum.DY_LIVE_PLAZA, userIds);
                // 保存userId数据
                userLogService.saveUserLog(userIds, platform, TaskTypeEnum.DY_LIVE_PLAZA, taskId1, BusinessTypeEnum.IEG_LIVE.getCode());
                log.info("直播广场 create --> end");
            }

            LambdaQueryWrapper<TencentIegUserInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(TencentIegUserInfo::getUserId, userIds);
            wrapper.eq(TencentIegUserInfo::getPlatform, platform);
            List<TencentIegUserInfo> list = tencentIegUserInfoService.list(wrapper);
            List<String> hasList = list.stream().map(TencentIegUserInfo::getUserId).collect(Collectors.toList());
            //修改用户最新状态
            if (!CollectionUtils.isEmpty(hasList)) {
                tencentIegUserInfoService.updateStatus(hasList, UserTaskStatusEnum.RUNNING.getCode());
                userIds.removeAll(hasList);
            }

            //保存新用户
            tencentIegUserInfoService.saveBatch(userIds.stream().map(userId -> {
                TencentIegUserInfo tencentIegUserInfo = new TencentIegUserInfo();
                tencentIegUserInfo.setUserId(userId);
                tencentIegUserInfo.setPlatform(platform);
                return tencentIegUserInfo;
            }).collect(Collectors.toList()));
        }

        return new LiveTaskCreateResponse(failList);
    }

    private Map<Long, Map<String, TencentIegUserLog>> getQuarkTaskResultMap(List<Long> taskIds) {
        //夸克任务结果
        Map<Long, Map<String, TencentIegUserLog>> map = new HashMap<>(1);
        // 获取子任务的状态
        taskIds.forEach(taskId -> {
            Map<String, TencentIegUserLog> userMap = new HashMap<>(1);
            Object o = quarkDataTaskSpi.subTaskDetail(buildSubTaskReq(taskId));
            SubTaskListResponse page = BeanUtil.toBean(o, SubTaskListResponse.class);
            if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRows())) {
                return;
            }

            page.getRows().forEach(x -> {
                //status= 成功or失败
                if (TaskStatusEnum.FINISH.name().equals(x.getStatus()) || TaskStatusEnum.FAIL.name().equals(x.getStatus())) {
                    TencentIegUserLog userLog = new TencentIegUserLog();
                    userLog.setStatus(x.getStatus());
                    userLog.setErrorDetail(x.getCallBackMsg());
                    userMap.put(x.getDetail(), userLog);
                }
            });
            map.put(taskId, userMap);
        });
        return map;
    }

    public void publicPriceTaskUpdate() {
        List<TencentIegUserLog> userLogs = userLogService.userLogNoFinish(BusinessTypeEnum.IEG_PUBLIC_PRICE.getCode());
        if (CollectionUtils.isEmpty(userLogs)) {
            return;
        }

        // 获取所有的任务ID
        List<Long> taskIds = userLogs.stream().map(TencentIegUserLog::getTaskId).distinct().filter(Objects::nonNull).collect(Collectors.toList());

        Map<Long, Map<String, TencentIegUserLog>> map = getQuarkTaskResultMap(taskIds);

        //更新任务状态
        userLogs.forEach(userLog -> {
            Map<String, TencentIegUserLog> userMap = map.get(userLog.getTaskId());
            if (userMap == null) {
                log.error("error {}", JSON.toJSONString(userLog));
                return;
            }
            TencentIegUserLog userStatusLog = userMap.get(userLog.getSubTaskId());
            if (userStatusLog == null) {
                return;
            }

            //更新状态
            userLog.setStatus(userStatusLog.getStatus());
            userLog.setUpdateTime(new Date());
            //失败信息
            if (TaskStatusEnum.FAIL.name().equals(userStatusLog.getStatus())) {
                userLog.setErrorDetail(userStatusLog.getErrorDetail());
            }
            userLogService.updateById(userLog);
        });
    }

    public void taskRetry() {
        // 拉取未完成状态 的user log 数据
        List<TencentIegUserLog> userLogs = userLogService.userLogNoFinish(BusinessTypeEnum.IEG_LIVE.getCode());
        if (CollectionUtils.isEmpty(userLogs)) {
            return;
        }
        // 获取所有的任务ID
        List<Long> taskIds = userLogs.stream().map(TencentIegUserLog::getTaskId).distinct().filter(Objects::nonNull).collect(Collectors.toList());

        Map<Long, Map<String, TencentIegUserLog>> map = getQuarkTaskResultMap(taskIds);

        //需要执行后续任务的用户
        List<String> postProcessList = new ArrayList<>(2000);

        //更新任务状态
        userLogs.forEach(userLog -> {
            Map<String, TencentIegUserLog> userMap = map.get(userLog.getTaskId());
            if (userMap == null) {
                log.error("error {}", JSON.toJSONString(userLog));
                return;
            }
            TencentIegUserLog userStatusLog = userMap.get(userLog.getSubTaskId());
            if (userStatusLog == null) {
                return;
            }

            //更新状态
            userLog.setStatus(userStatusLog.getStatus());
            userLog.setUpdateTime(new Date());
            //失败信息
            if (TaskStatusEnum.FAIL.name().equals(userStatusLog.getStatus())) {
                userLog.setErrorDetail(userStatusLog.getErrorDetail().substring(0, Math.min(userStatusLog.getErrorDetail().length(), 2000)));
            }
            userLogService.updateById(userLog);

            //抖音直播广场任务完成后 ,还需执行后续任务
            if (TaskTypeEnum.DY_LIVE_PLAZA.getName().equals(userLog.getType()) && TaskStatusEnum.FINISH.name().equals(userStatusLog.getStatus())) {
                postProcessList.add(userLog.getSubTaskId());
            }
            if (postProcessList.size() > 1000) {
                postProcess(postProcessList);
                postProcessList.clear();
            }
        });
        postProcess(postProcessList);
    }

    public void postProcess(List<String> userId) {
        if (CollectionUtils.isEmpty(userId)) {
            return;
        }
        log.info("抖音后续任务处理 userId --> {} ", JSON.toJSONString(userId));
        LambdaQueryWrapper<TencentIegUserInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TencentIegUserInfo::getUserId, userId);
        List<TencentIegUserInfo> list = tencentIegUserInfoService.list(wrapper);

        List<String> kolIds = list.stream().filter(info -> info.getKolId() != null).map(TencentIegUserInfo::getKolId).collect(Collectors.toList());

        //获取kolId
        List<String> userIdList = list.stream().filter(info -> info.getKolId() == null).map(TencentIegUserInfo::getUserId).collect(Collectors.toList());
        List<QuarkUserInfo> userInfos = quarkTaskService.pullUser(userIdList, PlatformTaskEnum.DOU_YIN.getName());
        if (!CollectionUtils.isEmpty(userInfos)) {
            userInfos.forEach(info -> {
                String kolId = info.getKolId();
                if (StringUtils.isNotEmpty(kolId)) {
                    kolIds.add(kolId);
                    UpdateWrapper<TencentIegUserInfo> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("user_id", info.getUserId());
                    updateWrapper.set("kol_id", kolId);
                    tencentIegUserInfoService.update(updateWrapper);
                }
            });
        }

        //使用kolId创建后续流程
        log.info("星图直播粉丝画像 create --> start");
        // 星图直播粉丝画像 kol_id
        Long taskId2 = quarkTaskService.createTask(TaskTypeEnum.DY_LIVE_FANS, kolIds);
        // 保存userId数据
        userLogService.saveUserLog(kolIds, PlatformTaskEnum.DOU_YIN.getName(), TaskTypeEnum.DY_LIVE_FANS, taskId2, BusinessTypeEnum.IEG_LIVE.getCode());
        log.info("星图直播粉丝画像 create --> end");

        log.info("星图直播观众画像 create --> start");
        // 星图直播观众画像 kol_id
        Long taskId3 = quarkTaskService.createTask(TaskTypeEnum.DY_LIVE_SPECTATOR, kolIds);
        // 保存userId数据
        userLogService.saveUserLog(kolIds, PlatformTaskEnum.DOU_YIN.getName(), TaskTypeEnum.DY_LIVE_SPECTATOR, taskId3, BusinessTypeEnum.IEG_LIVE.getCode());
        log.info("星图直播观众画像 create --> end");

        log.info("直播数据概览 create --> start");
        Long taskId4 = quarkTaskService.createTask(TaskTypeEnum.DY_LIVE_DATA, kolIds);
        // 保存userId数据
        userLogService.saveUserLog(kolIds, PlatformTaskEnum.DOU_YIN.getName(), TaskTypeEnum.DY_LIVE_DATA, taskId4, BusinessTypeEnum.IEG_LIVE.getCode());
        log.info("直播数据概览 create --> end");

        log.info("星图更新直播间列表 create --> start");
        Long taskId5 = quarkTaskService.createTask(TaskTypeEnum.DY_LIVE_LIST, kolIds);
        // 保存userId数据
        userLogService.saveUserLog(kolIds, PlatformTaskEnum.DOU_YIN.getName(), TaskTypeEnum.DY_LIVE_DATA, taskId5, BusinessTypeEnum.IEG_LIVE.getCode());
        log.info("星图更新直播间列表 create --> end");
    }

    /**
     * uid查询最新状态
     * 1)3天内创建的任务(无论夸克任务状态是什么都未完成) ---> 未完成
     * 2)夸克任务未完成                    ---> 未完成
     * 3)夸克任务失败
     * 3.1)2个月内成功过 ->  成功
     * 3.2)其他  ->  失败
     * 4)夸克任务成功
     * 4.1)   有数据->  成功
     * 4.2)   无数据->    未完成
     * <p>
     * //TODO  夸克一直超时的任务 重试
     *
     * @param request
     * @return
     */
    public TaskUserDetailResponse taskDetail(IegTaskCreateRequest request) {
        if (!CollectionUtils.isEmpty(request.getIds()) && request.getIds().size() > 100) {
            throw new BusinessException("用户ID数量超过100个！！！");
        }
        TaskUserDetailResponse result = new TaskUserDetailResponse();
        result.setPlatform(request.getPlatform());
        LambdaQueryWrapper<TencentIegUserInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TencentIegUserInfo::getUserId, request.getIds());
        wrapper.eq(TencentIegUserInfo::getPlatform, request.getPlatform());
        List<TencentIegUserInfo> list = tencentIegUserInfoService.list(wrapper);

        List<String> unFinishUserId = new ArrayList<>();
        List<TencentIegUserInfo> needQueryList = Lists.newArrayList();

        list.stream().forEach(it -> {
            // 获取当前时间的 LocalDate
            LocalDate currentLocalDate = LocalDate.now();
            // 计算3天前的日期
            LocalDate fourDaysAgo = currentLocalDate.minusDays(3);
            // 将 createTime 转换为 LocalDate
            LocalDate createTimeLocalDate = it.getLastCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            // 比较 createTime 是否在过去的3天内创建
            if (createTimeLocalDate.isAfter(fourDaysAgo) || createTimeLocalDate.isEqual(fourDaysAgo)) {
                unFinishUserId.add(it.getUserId());
            } else {
                needQueryList.add(it);
            }
        });

        //Key=userId or kolId, Value= userId
        Map<String, String> map = new HashMap<>();
        needQueryList.forEach(it -> {
            map.put(it.getUserId(), it.getUserId());
            map.put(it.getKolId(), it.getUserId());
        });

        //查询未完成的
        if (!map.keySet().isEmpty()) {
            if (PlatformEnum.DOUYIN.getCode().equals(request.getPlatform())) {
                //抖音直播广场未完成
                LambdaQueryWrapper<TencentIegUserLog> logWrapper = new LambdaQueryWrapper<>();
                logWrapper.in(TencentIegUserLog::getSubTaskId, needQueryList.stream().map(TencentIegUserInfo::getUserId).collect(Collectors.toList()));
                logWrapper.eq(TencentIegUserLog::getType, TaskTypeEnum.DY_LIVE_PLAZA.getName());
                logWrapper.notIn(TencentIegUserLog::getStatus, Lists.newArrayList(UserTaskStatusEnum.FINISH.name(), UserTaskStatusEnum.FAIL.name()));
                List<TencentIegUserLog> unFinishList = userLogService.list(logWrapper);
                unFinishList.forEach(it -> unFinishUserId.add(map.get(it.getSubTaskId())));

                //抖音后续3个任务都未完成  7天内
                LambdaQueryWrapper<TencentIegUserLog> logWrapper2 = new LambdaQueryWrapper<>();
                logWrapper2.in(TencentIegUserLog::getSubTaskId, needQueryList.stream().map(TencentIegUserInfo::getKolId).collect(Collectors.toList()));
                logWrapper2.in(TencentIegUserLog::getType, Lists.newArrayList(TaskTypeEnum.DY_LIVE_FANS.getName(), TaskTypeEnum.DY_LIVE_DATA.getName(), TaskTypeEnum.DY_LIVE_SPECTATOR.getName()));
                logWrapper2.notIn(TencentIegUserLog::getStatus, Lists.newArrayList(UserTaskStatusEnum.FINISH.name(), UserTaskStatusEnum.FAIL.name()));
                logWrapper2.gt(TencentIegUserLog::getCreateTime, LocalDate.now().minusDays(7));
                List<TencentIegUserLog> unFinishList2 = userLogService.list(logWrapper2);
                Map<String, List<TencentIegUserLog>> collect = unFinishList2.stream().collect(Collectors.groupingBy(TencentIegUserLog::getSubTaskId));
                collect.forEach((k, v) -> {
                    if (v.stream().map(TencentIegUserLog::getType).collect(Collectors.toSet()).size() == 3) {
                        unFinishUserId.add(map.get(k));
                    }
                });
            } else {
                //快手直播用户详情未完成
                LambdaQueryWrapper<TencentIegUserLog> logWrapper = new LambdaQueryWrapper<>();
                logWrapper.in(TencentIegUserLog::getSubTaskId, new ArrayList<>(map.keySet()));
                logWrapper.eq(TencentIegUserLog::getType, TaskTypeEnum.KS_LIVE_USER.getName());
                logWrapper.notIn(TencentIegUserLog::getStatus, Lists.newArrayList(UserTaskStatusEnum.FINISH.name(), UserTaskStatusEnum.FAIL.name()));
                List<TencentIegUserLog> unFinishList = userLogService.list(logWrapper);
                unFinishList.forEach(it -> unFinishUserId.add(map.get(it.getSubTaskId())));

            }
        }

        //查询失败的
        LambdaQueryWrapper<TencentIegUserLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.in(TencentIegUserLog::getSubTaskId, request.getIds());
        logWrapper.in(TencentIegUserLog::getType, Lists.newArrayList(TaskTypeEnum.DY_LIVE_PLAZA.getName(), TaskTypeEnum.KS_LIVE_USER.getName()));
        logWrapper.eq(TencentIegUserLog::getStatus, UserTaskStatusEnum.FAIL.name());
        List<TencentIegUserLog> failUserList = userLogService.list(logWrapper);
        List<String> failList = failUserList.stream().map(TencentIegUserLog::getSubTaskId).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(needQueryList)) {
            //5天前的未完成算失败
            LambdaQueryWrapper<TencentIegUserLog> logWrapper3 = new LambdaQueryWrapper<>();
            logWrapper3.in(TencentIegUserLog::getSubTaskId, needQueryList.stream().map(TencentIegUserInfo::getUserId).collect(Collectors.toList()));
            logWrapper3.eq(TencentIegUserLog::getStatus, "PROGRESS");
            logWrapper3.lt(TencentIegUserLog::getCreateTime, LocalDate.now().minusDays(5));
            List<TencentIegUserLog> failList2 = userLogService.list(logWrapper3);
            failList2.forEach(it -> failList.add(it.getSubTaskId()));

            //5天前未完成的抖音后续任务
            LambdaQueryWrapper<TencentIegUserLog> logWrapper4 = new LambdaQueryWrapper<>();
            logWrapper4.in(TencentIegUserLog::getSubTaskId, needQueryList.stream().map(TencentIegUserInfo::getKolId).collect(Collectors.toList()));
            logWrapper4.eq(TencentIegUserLog::getStatus, "PROGRESS");
            logWrapper4.lt(TencentIegUserLog::getCreateTime, LocalDate.now().minusDays(5));
            List<TencentIegUserLog> failList3 = userLogService.list(logWrapper4);
            failList3.forEach(it -> failList.add(map.get(it.getSubTaskId())));
        }


        //失败任务补救，2个月内成功过，算成功
        if (!CollectionUtils.isEmpty(failList)) {
            LambdaQueryWrapper<TencentIegUserLog> logWrapper2 = new LambdaQueryWrapper<>();
            logWrapper2.in(TencentIegUserLog::getSubTaskId, failList);
            logWrapper2.in(TencentIegUserLog::getType, Lists.newArrayList(TaskTypeEnum.DY_LIVE_PLAZA.getName(), TaskTypeEnum.KS_LIVE_USER.getName()));
            logWrapper2.eq(TencentIegUserLog::getStatus, UserTaskStatusEnum.FINISH.name());
            logWrapper2.gt(TencentIegUserLog::getCreateTime, LocalDate.now().minusMonths(2));
            List<TencentIegUserLog> successList = userLogService.list(logWrapper2);
            List<String> collect = successList.stream().map(TencentIegUserLog::getSubTaskId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                failList.removeAll(collect);
                unFinishUserId.removeAll(collect);
            }
        }

        result.setDetail(list.stream().map(it -> {
            TaskUserDetailResponse.UserStatusDetail userStatusDetail = new TaskUserDetailResponse.UserStatusDetail();
            userStatusDetail.setUid(it.getUserId());
            if (failList.contains(it.getUserId())) {
                userStatusDetail.setStatus(UserTaskStatusEnum.FAIL.getName());
            } else if (unFinishUserId.contains(it.getUserId())) {
                userStatusDetail.setStatus(UserTaskStatusEnum.RUNNING.getName());
            } else {
                //成功的需要查询数据，没数据算未完成
                IegTaskCreateRequest iegTaskCreateRequest = new IegTaskCreateRequest();
                iegTaskCreateRequest.setIds(Lists.newArrayList(it.getUserId()));
                iegTaskCreateRequest.setPlatform(it.getPlatform());
                List<QuarkUserInfo> quarkUserInfos = pushIegUser(iegTaskCreateRequest);
                //没有直播数据
                if (!CollectionUtils.isEmpty(quarkUserInfos) && quarkUserInfos.get(0).getObjectLiveStar() != null) {
                    userStatusDetail.setStatus(UserTaskStatusEnum.FINISH.getName());
                } else {
                    userStatusDetail.setStatus(UserTaskStatusEnum.RUNNING.getName());
                }
            }
            return userStatusDetail;
        }).collect(Collectors.toList()));
        return result;
    }


    public Long publicPriceCreateTask(PublicPriceTaskCreateRequest request) {
        String tenantName = null;
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest req = attributes.getRequest();
            tenantName = req.getHeader("user-name");
        }
        if (tenantName == null) {
            throw new BusinessException("授权失败，请重新获取token!");
        }

        log.info("刊例价 查询 -> {}", request.getUser_ids());

        String platform = PlatformEnum.getCodeByNo(request.getPlatform());

        List<String> userIdList = Arrays.asList(request.getUser_ids().split(","));

        Long taskId = null;
        TaskTypeEnum taskType = null;

        // 创建夸克任务
        if (PlatformTaskEnum.KUAI_SHOU.getName().equals(platform)) {
            log.info("直播用户详情 create --> start");
            // 直播用户详情 user_id
            taskId = quarkTaskService.createTask(TaskTypeEnum.KS_LIVE_USER, userIdList);
            taskType = TaskTypeEnum.KS_LIVE_USER;
            log.info("直播用户详情 create --> end");
        } else {
            // 抖音星图直播广场 user_id
            log.info("直播广场 create --> start");
            taskId = quarkTaskService.createTask(TaskTypeEnum.DY_LIVE_PLAZA, userIdList);
            taskType = TaskTypeEnum.DY_LIVE_PLAZA;
            log.info("直播广场 create --> end");
        }

        userLogService.saveUserLog(userIdList, platform, taskType, taskId, TaskStatusEnum.PROGRESS.name(), null, tenantName, BusinessTypeEnum.IEG_PUBLIC_PRICE.getCode());
        return taskId;
    }

    private void retryUserTask(List<TencentIegUserLog> retryList) {
        // 按任务类型分组
        Map<String, List<TencentIegUserLog>> map = retryList.stream().collect(Collectors.groupingBy(TencentIegUserLog::getType));
        map.forEach(this::taskIdUpdate);
    }

    private void taskIdUpdate(String key, List<TencentIegUserLog> value) {
        if (CollectionUtils.isEmpty(value)) {
            return;
        }
        List<List<TencentIegUserLog>> list = Lists.partition(value, 50);
        list.forEach(logs -> {
            List<String> userIds = logs.stream().map(TencentIegUserLog::getSubTaskId).collect(Collectors.toList());
            String platform = logs.get(0).getPlatform();

            if (!TaskTypeEnum.KS_LIVE_USER.getName().equals(key) && !TaskTypeEnum.DY_LIVE_PLAZA.getName().equals(key)) {
                userIds = logs.stream().map(TencentIegUserLog::getKolId).collect(Collectors.toList());
            }

            Long taskId = quarkTaskService.createTask(TaskTypeEnum.getTaskTypeByName(key), userIds);
            logs.forEach(x -> x.setTaskId(taskId));
            userLogService.updateStatus(logs);
        });
    }


    private SubTaskListRequest buildSubTaskReq(Long taskId) {
        SubTaskListRequest request = new SubTaskListRequest();
        request.setId(taskId);
        request.setKeyword("");
        request.setStatus(TaskStatusEnum.ALL);
        request.setPage(1);
        request.setSize(500);
        return request;
    }

    public List<PublicPriceUserDto> queryUser(Long taskId) {
        log.info("刊例价 夸克任务查询 -> {}", taskId);
        List<PublicPriceUserDto> result = new ArrayList<>();

        List<TencentIegUserLog> tencentIegUserLogs = userLogService.queryUserLogByTask(taskId);
        for (TencentIegUserLog userLog : tencentIegUserLogs) {
            PublicPriceUserDto resultDto = new PublicPriceUserDto();
            resultDto.setStatus(userLog.getStatus());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateString = sdf.format(userLog.getUpdateTime());
            resultDto.setUserId(userLog.getSubTaskId());
            resultDto.setPlatform(userLog.getPlatform());

            if (TaskStatusEnum.FINISH.name().equals(userLog.getStatus())) {
                List<QuarkUserInfo> quarkUserInfos = quarkTaskService.pullUser(Lists.newArrayList(userLog.getSubTaskId()), userLog.getPlatform());
                BeanUtils.copyProperties(quarkUserInfos.get(0), resultDto);
                resultDto.setRegisteredOnPlatform(!StringUtils.isEmpty(resultDto.getKolId()));
                resultDto.setObjectPriceInfos(quarkUserInfos.get(0).getObjectLiveStar().getObjectPriceInfos());
                resultDto.setUpdateTime(dateString);
            }
            result.add(resultDto);
        }
        return result;
    }
}
