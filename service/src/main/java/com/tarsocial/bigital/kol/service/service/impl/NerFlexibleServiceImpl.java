package com.tarsocial.bigital.kol.service.service.impl;


import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.tarsocial.bigital.kol.common.domain.dto.FlexibleMessageDto;
import com.tarsocial.bigital.kol.common.domain.dto.NerFlexibleDto;
import com.tarsocial.bigital.kol.common.domain.dto.NerFlexiblePostDto;
import com.tarsocial.bigital.kol.common.domain.entity.NerFlexibleEntity;
import com.tarsocial.bigital.kol.common.domain.enums.EntityTypeEnum;
import com.tarsocial.bigital.kol.common.domain.request.NerFlexibleCalculateRequest;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import com.tarsocial.bigital.kol.service.kafka.NerFlexibleQueueConsumer;
import com.tarsocial.bigital.kol.service.mapper.NerFlexibleMapper;
import com.tarsocial.bigital.kol.service.service.AlignEntityService;
import com.tarsocial.bigital.kol.service.service.NerFlexibleService;
import com.tarsocial.bigital.kol.service.util.FileUploadUtil;
import com.tarsocial.bigital.kol.service.util.QueryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Service
@Slf4j
public class NerFlexibleServiceImpl implements NerFlexibleService {

    @Resource
    private NerFlexibleMapper nerFlexibleMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private NerFlexibleQueueConsumer nerFlexibleQueueConsumer;

    @Qualifier("elasticsearchClient")
    @Resource
    private ElasticsearchClient client;

    @Resource
    private AlignEntityService alignEntityService;

    private ExecutorService executor = Executors.newFixedThreadPool(1);

    private static final int batchSize = 5000;

    private static final List<String> mustSource = Lists.newArrayList("kw_id", "tx_title", "tx_content", "date_publishedAt", "kw_platform", "tx_ocr", "tx_asr", "long_interaction");


    @Override
    public Map<String, List<String>> dict() {
        Map<String, List<String>> result = new TreeMap<>();
        result.put("united_ent", Lists.newArrayList("品牌", "品类", "产品", "包装", "场景", "人群", "成分", "发质", "肤质", "功效", "痛点", "香味", "菜式", "风味", "产品使用"));
        result.put("pg_ent", Lists.newArrayList("TA", "痛点", "功效", "Form", "成分", "场景", "品牌", "产品", "肤质", "发质", "香味", "包装", "产品使用"));
        result.put("lkk_ent", Lists.newArrayList("品牌", "产品", "品类", "场景", "包装", "人群", "菜式", "风味"));
        return result;
    }

    @Override
    public List<NerFlexibleEntity> list() {
        LambdaQueryWrapper<NerFlexibleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(NerFlexibleEntity::getCreateTime);
        return nerFlexibleMapper.selectList(wrapper);
    }

    private void processCalculate(NerFlexibleEntity flexibleEntity, NerFlexibleDto nerFlexibleDto, Workbook workbook, NerFlexibleCalculateRequest req, String originalFilename) {
        List<String> postIdList = null;
        nerFlexibleDto.setRequest(req);

        flexibleEntity.setTaskStatus("运行中");
        nerFlexibleMapper.updateById(flexibleEntity);

        //根据作品ID跑数
        if (CollectionUtils.isEmpty(req.getColumns())) {
            try {
                postIdList = readPostList(workbook.getSheetAt(0));
            } catch (Exception e) {
                log.error("read excel error", e);
            }

            if (postIdList == null || postIdList.isEmpty()) {
                log.error("read excel size 0");
                return;
            }

            nerFlexibleDto.setPostIdList(postIdList);
            nerFlexibleDto.setPostMap(new HashMap<>(postIdList.size() * 2));
            nerFlexibleDto.setResult(new HashMap<>(postIdList.size() * 2));

            int size = pushKafka(nerFlexibleDto, null);
            if (size == 0) {
                log.error("calculate 查询ES结果为空 {}", originalFilename);
                flexibleEntity.setTaskStatus("找不到帖子");
                nerFlexibleMapper.updateById(flexibleEntity);
                return;
            }
        } else {
            nerFlexibleDto.setHasPostId(false);
            pushByExcel(nerFlexibleDto, readExcelColumns(workbook.getSheetAt(0), req.getColumns(), 1));
        }


        log.info("已推送kafka，正在等待算法结果 {}", originalFilename);

        if (req.getEntModelPipeline() != null && req.getEntModelPipeline().getSchema() != null) {
            Map<String, Map<String, String>> entityMap = nerFlexibleDto.getEntityMap();
            req.getEntModelPipeline().getSchema().forEach(type -> {
                entityMap.put(type, alignEntityService.getAllEntityMapping("beauty", EntityTypeEnum.getCodeByName(type)));
            });
        }

        //处理算法结果
        List<String> unFinishPostIdList = processData(nerFlexibleDto);

        //重试补充一次
        if (CollectionUtils.isNotEmpty(unFinishPostIdList)) {
            nerFlexibleDto.setPostIdList(unFinishPostIdList);
            //重试
            if (CollectionUtils.isEmpty(req.getColumns())) {
                pushByPostId(nerFlexibleDto);
            } else {
                pushByExcel(nerFlexibleDto, readExcelColumns(workbook.getSheetAt(0), req.getColumns(), 1));
            }
            processData(nerFlexibleDto);
        }
        log.info("已处理完算法结果，正在上传文件 {}", originalFilename);
        //结果写回EXCEL
        String url = uploadResult(workbook, nerFlexibleDto, originalFilename);
        if (StringUtils.isNotEmpty(url)) {
            flexibleEntity.setDownloadUrl(url);
        }
        flexibleEntity.setTaskStatus(StringUtils.isNotEmpty(url) ? "完成" : "文件上传失败");
        nerFlexibleMapper.updateById(flexibleEntity);
    }

    private String uploadResult(Workbook workbook, NerFlexibleDto nerFlexibleDto, String originalFilename) {
        short lastCellNum = workbook.getSheetAt(0).getRow(0).getLastCellNum();
        Map<String, Integer> headerMap = new ArrayList<>(nerFlexibleDto.getWriteHeaderList()).stream().collect(Collectors.toMap(v -> v, v -> new ArrayList<>(nerFlexibleDto.getWriteHeaderList()).indexOf(v) + lastCellNum));
        //写入数据
        write(workbook.getSheetAt(0), nerFlexibleDto.getResult(), headerMap, nerFlexibleDto.isHasPostId());

        String fileName = "./result_" + originalFilename;
        FileOutputStream fileOut = null;
        try {
            // 写入新的 Excel 文件
            fileOut = new FileOutputStream(fileName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e) {
            log.error("write error", e);
        } finally {
            try {
                fileOut.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        String url = null;
        try {
            url = FileUploadUtil.fileUpload(fileName);
            log.info("文件已上传  url -> {}", url);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                Files.delete(Paths.get(fileName));
                log.info("原帖导出 Excel 文件已删除！");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return url;
    }

    @Override
    public void calculate(MultipartFile file, NerFlexibleCalculateRequest req) {
        log.info("calculate 开始模型计算 {},req -> {}", file.getOriginalFilename(), JSON.toJSONString(req));
        String originalFilename = file.getOriginalFilename();
        NerFlexibleDto nerFlexibleDto = new NerFlexibleDto();

        if (Boolean.TRUE.equals(req.getComment())) {
            nerFlexibleDto.setIndexName("comment_*v0");
        }

        Workbook workbook = null;
        try {
            // 获取文件的输入流
            InputStream inputStream = file.getInputStream();
            workbook = new XSSFWorkbook(inputStream);
        } catch (Exception e) {
            log.error("read excel error", e);
            throw new BusinessException("文件解析失败！");
        }


        NerFlexibleEntity flexibleEntity = new NerFlexibleEntity();
        flexibleEntity.setFileName(originalFilename);
        flexibleEntity.setModelSchema(String.join(",", req.getEntModelPipeline().getSchema()));
        flexibleEntity.setModelType(req.getEntModelPipeline().getModel_type());
        flexibleEntity.setSentiment(req.getSentiment());
        flexibleEntity.setStandard(req.getStandard());
        flexibleEntity.setTaskStatus("排队中");
        flexibleEntity.setComment(req.getComment());
        flexibleEntity.setAgo(req.getAgo());
        if (CollectionUtils.isNotEmpty(req.getColumns())) {
            flexibleEntity.setColumns(JSON.toJSONString(req.getColumns()));
        }
        nerFlexibleMapper.insert(flexibleEntity);

        Workbook finalWorkbook = workbook;

        processCalculate(flexibleEntity, nerFlexibleDto, finalWorkbook, req, originalFilename);
//        executor.execute(() -> processCalculate(flexibleEntity, nerFlexibleDto, finalWorkbook, req, originalFilename));
    }

    private List<String> readPostList(MultipartFile file) {
        AtomicInteger targetColumnIndex = new AtomicInteger(-1);
        List<String> postIdList = new ArrayList<>();

        try {
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                    for (Integer index : headMap.keySet()) {
                        if ("作品ID".equals(headMap.get(index).getStringValue())) {
                            targetColumnIndex.set(index);  // 设置目标列的索引
                        }
                    }
                    super.invokeHead(headMap, context);
                }

                @Override
                public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                    int index = targetColumnIndex.get();
                    String postId = rowData.get(index);
                    postIdList.add(postId);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                }
            }).sheet().doRead();
        } catch (Exception e) {
            log.error("NerFlexible read file error {}", e.getMessage(), e);
        }
        return postIdList;
    }


    /**
     * 查询ES并推送kafka
     *
     * @param postIdList
     */
    private int queryEsAndPushKafka(NerFlexibleDto dto, List<String> postIdList) {
        List<Query> must = new ArrayList<>();
        must.add(QueryUtil.terms("kw_id", postIdList));
        must.add(QueryUtil.terms("kw_platform", Lists.newArrayList("douyin", "xiaohongshu", "weixin")));
        if (Boolean.FALSE.equals(dto.getAgo())) {
            must.add(QueryUtil.range("date_publishedAt", "2024-01-01", null));
        }


        SearchRequest searchRequest = SearchRequest.of(b -> b.index(dto.getIndexName())
                .query(q -> q.bool(bq -> bq.must(must)))
                .source(s3 -> s3.filter(f -> f.includes(mustSource)))
                .size(10000)
        );

        SearchResponse<Map> searchResponse = null;
        try {
            searchResponse = client.search(searchRequest, Map.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        List<FlexibleMessageDto> list = new ArrayList<>();

        // 定义日期时间格式
        DateTimeFormatter originalFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter targetFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        List<Hit<Map>> hits = searchResponse.hits().hits();
        hits.forEach(it -> {
            Map source = it.source();
            String kw_id = getString(source, "kw_id");

            NerFlexiblePostDto nerFlexiblePostDto = new NerFlexiblePostDto();
            // 解析原始字符串为 LocalDateTime 对象
            try {
                if (source.get("date_publishedAt") != null) {
                    LocalDateTime dateTime = LocalDateTime.parse(source.get("date_publishedAt").toString(), originalFormatter);
                    nerFlexiblePostDto.setDate(dateTime.format(targetFormatter));
                }
            } catch (Exception e) {
                log.error("date_publishedAt fotmat error -> kw_id :{} , {}", kw_id, source.get("date_publishedAt").toString());
                nerFlexiblePostDto.setDate(source.get("date_publishedAt").toString());
            }

            if (getString(source, "long_interaction") != null) {
                nerFlexiblePostDto.setInteraction(Long.valueOf(getString(source, "long_interaction")));
            }

            dto.getPostMap().put(kw_id, nerFlexiblePostDto);

            FlexibleMessageDto flexibleMessageDto = new FlexibleMessageDto();
            flexibleMessageDto.setTx_title(getString(source, "tx_title"));
            flexibleMessageDto.setTx_asr(getString(source, "tx_asr"));
            flexibleMessageDto.setTx_ocr(getString(source, "tx_ocr"));
            flexibleMessageDto.setKw_id(kw_id);
            flexibleMessageDto.setTx_content(getString(source, "tx_content"));
            flexibleMessageDto.setNo(dto.getCurrentTimeMillis() + "_" + flexibleMessageDto.getKw_id());
            flexibleMessageDto.setBatch("ty_" + dto.getCurrentTimeMillis());
            list.add(flexibleMessageDto);
        });

        nerFlexibleQueueConsumer.push(list, dto.getRequest());
        return hits.size();
    }

    public static String getString(Map map, String key) {
        Object value = map.get(key);
        return value == null ? null : value.toString().replaceAll("\u0000", "").replaceAll("\\u0000", "");
    }


    private int getPostIdRowNum(Row titleRow) {
        int postIdCellNum = 0;
        for (int i = 0; i < titleRow.getLastCellNum(); i++) {
            if ("作品ID".equals(titleRow.getCell(i).getStringCellValue())) {
                postIdCellNum = i;
                break;
            }
        }
        return postIdCellNum;
    }

    private List<String> readContent(Sheet sheet) {
        List<String> postIdList = new ArrayList<>(sheet.getLastRowNum());
        int postIdCellNum = getPostIdRowNum(sheet.getRow(0));
        Iterator<Row> rowIterator = sheet.iterator();
        rowIterator.next();
        // 读取postId
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            Cell cell = row.getCell(postIdCellNum);
            if (cell != null) {
                postIdList.add(getPostId(cell.getStringCellValue()));
            }
        }
        return postIdList;
    }


    private List<String> readPostList(Sheet sheet) {
        List<String> postIdList = new ArrayList<>(sheet.getLastRowNum());
        int postIdCellNum = getPostIdRowNum(sheet.getRow(0));
        Iterator<Row> rowIterator = sheet.iterator();
        rowIterator.next();
        // 读取postId
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            Cell cell = row.getCell(postIdCellNum);
            if (cell != null) {
                postIdList.add(getPostId(cell.getStringCellValue()));
            }
        }
        return postIdList;
    }

    private String getPostId(String cellValue) {
        return cellValue.replaceAll("\t", "").trim();
    }

    private void write(Sheet sheet, Map<String, Map<String, Set<String>>> result, Map<String, Integer> headerMap, boolean hasPostId) {
        Iterator<Row> rowIterator = sheet.iterator();
        Row titleRow = rowIterator.next();

        headerMap.forEach((k, v) -> {
            Cell cell = titleRow.createCell(v);
            cell.setCellValue(k);
        });

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();

            String postId = (row.getRowNum() - 1) + "";
            if (hasPostId) {
                Cell cell1 = row.getCell(getPostIdRowNum(sheet.getRow(0)));
                if (cell1 != null) {
                    postId = getPostId(cell1.getStringCellValue());
                }
            }

            //数据写入
            Map<String, Set<String>> stringListMap = result.get(postId);
            if (stringListMap != null) {
                stringListMap.forEach((head, v) -> {
                    Integer offset = headerMap.get(head);
                    Cell cell = row.createCell(offset);
                    cell.setCellValue(String.join(",", v));
                });
            }
        }
    }


    /**
     * 读取 Excel 文件指定列的数据，并将每行数据按 headList 顺序拼接。
     *
     * @param headList       需要读取的列名列表，例如 ["A", "C", "E"]
     * @param skipHeaderRows 跳过表头的行数，如果第一行是数据则为0
     * @return 拼接后的字符串列表，每个字符串代表一行数据
     * @throws IOException 如果读取文件发生错误
     */
    public List<String> readExcelColumns(Sheet sheet, List<String> headList, int skipHeaderRows) {
        if (headList == null || headList.isEmpty()) {
            throw new IllegalArgumentException("headList 不能为空");
        }

        List<String> result = new ArrayList<>();

        // 将列字母转换为 0-based 索引
        List<Integer> columnIndices = headList.stream()
                .map(this::getColumnIndex)
                .collect(Collectors.toList());

        DataFormatter dataFormatter = new DataFormatter(); // 用于格式化单元格数据为字符串

        int rowNum = 0;
        for (Row row : sheet) {
            if (rowNum++ < skipHeaderRows) {
                continue; // 跳过表头行
            }

            StringBuilder rowData = new StringBuilder();
            boolean rowHasData = true; // 标记当前行是否有数据

            for (Integer colIdx : columnIndices) {
                Cell cell = row.getCell(colIdx, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
                String cellValue = "";
                if (cell != null) {
                    cellValue = dataFormatter.formatCellValue(cell).trim();
                    if (!cellValue.isEmpty()) {
                        rowHasData = true;
                    }
                }
                rowData.append(cellValue);
            }
            // 只有当行中至少有一个指定列有数据时才添加到结果集，或者根据需求调整
            if (rowHasData || !rowData.toString().isEmpty()) { // 如果希望空行也拼接（结果是空串），可以去掉 rowHasData 判断
                result.add(rowData.toString());
            }
        }

        return result;
    }

    /**
     * 将 Excel 列字母（如 "A", "B", "AA"）转换为 0-based 索引。
     *
     * @param columnLetter 列字母
     * @return 0-based 列索引
     */
    private int getColumnIndex(String columnLetter) {
        if (columnLetter == null || columnLetter.isEmpty()) {
            throw new IllegalArgumentException("列字母不能为空");
        }
        columnLetter = columnLetter.toUpperCase();
        int columnIndex = 0;
        for (int i = 0; i < columnLetter.length(); i++) {
            char c = columnLetter.charAt(i);
            if (c < 'A' || c > 'Z') {
                throw new IllegalArgumentException("无效的列字母: " + columnLetter);
            }
            columnIndex = columnIndex * 26 + (c - 'A' + 1);
        }
        return columnIndex - 1; // 0-based
    }


    /**
     * 处理算法结果
     *
     * @param postId
     * @param value
     * @param model
     */
    private void processResult(NerFlexibleDto nerFlexibleDto, String postId, String value, String model) {
        HashMap<String, Set<String>> resultDetailMap = new HashMap<>();
        Set<String> writeHeaderList = nerFlexibleDto.getWriteHeaderList();
        boolean needStandard = nerFlexibleDto.getRequest().getStandard();
        Map<String, Map<String, Set<String>>> result = nerFlexibleDto.getResult();
        Map<String, Map<String, String>> entityMap = nerFlexibleDto.getEntityMap();

        JSONObject jsonObject = JSON.parseObject(value);
        String modelValue = jsonObject.getString(model);
        if (StringUtils.isEmpty(modelValue)) {
            return;
        }

        if (StringUtils.isNotEmpty(jsonObject.getString("emotion"))) {
            String emotion = jsonObject.getString("emotion");
            try {
                JSONObject emotionJSON = JSON.parseObject(emotion);
                HashSet<String> scoreSet = new HashSet<>();
                scoreSet.add(emotionJSON.getString("score"));
                resultDetailMap.put("整贴情感.score", scoreSet);
                writeHeaderList.add("整贴情感.score");
                HashSet<String> percentSet = new HashSet<>();
                percentSet.add(emotionJSON.getString("percent"));
                resultDetailMap.put("整贴情感.percent", percentSet);
                writeHeaderList.add("整贴情感.percent");
            } catch (Exception e) {
                HashSet<String> set = new HashSet<>();
                set.add("新格式未能正确解析，请联系开发人员");
                resultDetailMap.put("整贴情感.score", set);
                writeHeaderList.add("整贴情感.score");
            }
        }

        try {
            if (modelValue.trim().startsWith("{")) {
                JSONObject jsonObject1 = JSON.parseObject(modelValue);
                Set<String> keySet = jsonObject1.keySet();

                keySet.forEach(it -> {
                    JSONArray jsonArray = jsonObject1.getJSONArray(it);
                    Set<String> set = jsonArray.stream()
                            .map(v -> JSON.parseObject(v.toString()).getString("text"))
                            .collect(Collectors.toSet());

                    addResult(resultDetailMap, writeHeaderList, it, set, needStandard, entityMap.get(it));
                });
            } else if (modelValue.trim().startsWith("[")) {
                JSONArray jsonArray = JSON.parseArray(modelValue);

                // 遍历 JSON 数组并填充 Map
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject item = jsonArray.getJSONObject(i);
                    String type = item.getString("type");
                    String entity = item.getString("entity");
                    HashSet<String> set = new HashSet<>();
                    set.add(entity);

                    addResult(resultDetailMap, writeHeaderList, type, set, needStandard, entityMap.get(type));
                }
            }
        } catch (Exception e) {
            log.error("又发现新的格式啦！！！ value -> {}", value);
        }

        result.put(postId, resultDetailMap);
    }


    private void addResult(HashMap<String, Set<String>> resultDetailMap, Set<String> writeHeaderList, String key, Set<String> set, boolean needStandard, Map<String, String> entityMap) {
        resultDetailMap.computeIfAbsent(key, k -> new HashSet<>()).addAll(set);
        writeHeaderList.add(key);

        if (needStandard) {
            HashSet<String> entity = new HashSet<>();
            //查标准实体
            set.forEach(it -> {
                if (entityMap.get(it) != null) {
                    entity.add(entityMap.get(it));
                }
            });
            resultDetailMap.computeIfAbsent("标准-" + key, k -> new HashSet<>()).addAll(entity);
            writeHeaderList.add("标准-" + key);
        }
    }

    private int pushByExcel(NerFlexibleDto dto, List<String> contentList) {
        List<FlexibleMessageDto> list = new ArrayList<>();
        List<String> postIdList = dto.getPostIdList();
        //第一次推送 初始化dto
        if (postIdList == null) {
            postIdList = new ArrayList<>(contentList.size());
            for (int i = 0; i < contentList.size(); i++) {
                FlexibleMessageDto flexibleMessageDto = new FlexibleMessageDto();
                flexibleMessageDto.setKw_id(i + "");
                flexibleMessageDto.setTx_content(contentList.get(i));
                flexibleMessageDto.setNo(dto.getCurrentTimeMillis() + "_" + flexibleMessageDto.getKw_id());
                flexibleMessageDto.setBatch("ty_" + dto.getCurrentTimeMillis());
                list.add(flexibleMessageDto);
                postIdList.add(i + "");
            }
            dto.setPostMap(new HashMap<>(postIdList.size() * 2));
            dto.setResult(new HashMap<>(postIdList.size() * 2));
            dto.setPostIdList(postIdList);
        } else {
            //重试补充推送
            for (String id : postIdList) {
                FlexibleMessageDto flexibleMessageDto = new FlexibleMessageDto();
                flexibleMessageDto.setKw_id(id);
                flexibleMessageDto.setTx_content(contentList.get(Integer.parseInt(id)));
                flexibleMessageDto.setNo(dto.getCurrentTimeMillis() + "_" + flexibleMessageDto.getKw_id());
                flexibleMessageDto.setBatch("ty_" + dto.getCurrentTimeMillis());
                list.add(flexibleMessageDto);
            }
        }
        nerFlexibleQueueConsumer.push(list, dto.getRequest());
        return postIdList.size();
    }

    private int pushByPostId(NerFlexibleDto dto) {
        int sum = 0;
        //查询ES and send kafka
        for (int i = 0; i < dto.getPostIdList().size(); i += batchSize) {
            // 获取当前批次的子列表
            int end = Math.min(i + batchSize, dto.getPostIdList().size());
            List<String> batch = dto.getPostIdList().subList(i, end);
            int size = queryEsAndPushKafka(dto, batch);
            sum += size;
        }
        return sum;
    }

    private int pushKafka(NerFlexibleDto dto, List<String> contentList) {
        //直接推送
        if (CollectionUtils.isNotEmpty(contentList)) {
            return pushByExcel(dto, contentList);
        }
        return pushByPostId(dto);
    }

    private List<String> processData(NerFlexibleDto nerFlexibleDto) {
        List<String> postIdList = nerFlexibleDto.getPostIdList();
        long currentTimeMillis = nerFlexibleDto.getCurrentTimeMillis();
        log.info("开始处理算法数据... lastSize：{} ", postIdList.size());
        int lastSize = 0;
        int retry = 0;
        while (!postIdList.isEmpty() && retry < 10) {
            boolean hasResult = false;
            Iterator<String> iterator = postIdList.iterator();
            while (iterator.hasNext()) {
                //本次轮询有结果
                String postId = iterator.next();
                String value = (String) redisTemplate.opsForValue().get(currentTimeMillis + "_" + postId);
                if (value != null) {
                    hasResult = true;
                    redisTemplate.delete(currentTimeMillis + "_" + postId);
                    processResult(nerFlexibleDto, postId, value, "pg_ent");
                    processResult(nerFlexibleDto, postId, value, "lkk_ent");
                    processResult(nerFlexibleDto, postId, value, "united_ent");

                    iterator.remove();
                }
            }

            try {
                Thread.sleep(6000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            //剩余postListSize无变化
            if (lastSize == postIdList.size()) {
                retry++;
            }

            if (hasResult) {
                lastSize = postIdList.size();
            }
            log.info("处理算法数据... lastSize：{} ， hasResult：{} ， retry：{}", lastSize, hasResult, retry);
        }

        if (!postIdList.isEmpty()) {
            log.error("存在没有处理结果的帖子 idList - >{}", JSON.toJSONString(postIdList));
            return postIdList;
        }
        return null;
    }
}
