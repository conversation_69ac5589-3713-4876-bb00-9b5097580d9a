package com.tarsocial.bigital.kol.service.util.tos;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "spring.huoshanoss")
@Configuration
@Data
public class HuoShanOSSProperties {
    private String endpoint;
    private String region;
    private String accessKey;
    private String secretKey;
    private Integer connectTimeoutMills;
    private String bucketName;
}
