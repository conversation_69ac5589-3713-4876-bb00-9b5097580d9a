package com.tarsocial.bigital.kol.service.util;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public class HttpsUtil {

    public static String doPost(String url, Object params, Map<String, String> header) {
        if (CollectionUtils.isEmpty(header)) {
            return doPost(url, params);
        }
        HttpHeaders headers = new HttpHeaders();
        header.forEach(headers::add);
        RestTemplate restTemplate = new RestTemplate();
        RequestEntity<String> requestEntity = RequestEntity.post(url).accept(MediaType.APPLICATION_JSON).headers(headers).body(JSON.toJSONString(params));
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        return response.getBody();
    }

    public static String doPost(String url, Object params) {
        log.info("postForObject url -> {}, params -> {}", url, params);
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
        JSONObject json = (JSONObject) JSON.toJSON(params);
        String response = restTemplate.postForObject(url, json, String.class);
        log.info("doPost url -> {} , resp->{}", url, response);
        return response;
    }

    public static String doGet(String url) {
        return doGet(url, null, null);
    }

    public static String doGet(String url, Map<String, String> header, String proxy) {
        log.info("doGet url -> {}， proxy -> {}", url, proxy);
        RequestConfig defaultRequestConfig = RequestConfig.custom()
                .setConnectTimeout(20000)
                .setSocketTimeout(20000)
                .setConnectionRequestTimeout(20000)
                .build();
        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpGet httpGet = new HttpGet(url);
        httpGet.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36");
        if (CollectionUtils.isNotEmpty(header)) {
            header.forEach(httpGet::addHeader);
        }
        if (StringUtils.isNotEmpty(proxy)) {
            String[] split = proxy.split(":");
            defaultRequestConfig = RequestConfig.copy(defaultRequestConfig).setProxy(new HttpHost(split[0], Integer.parseInt(split[1]))).build();
        }
        try {
            httpGet.setConfig(defaultRequestConfig);
            HttpResponse response = client.execute(httpGet);
            String resp = EntityUtils.toString(response.getEntity());
            log.info("doGet res:{}", resp);
            return resp;
        } catch (IOException e) {
            log.error("doGet error -> {}", e.getMessage(), e);
        }
        return null;
    }


}
