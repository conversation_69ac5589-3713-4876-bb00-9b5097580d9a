package com.tarsocial.bigital.kol.service.mapper;

import com.tarsocial.bigital.kol.common.domain.entity.tencent.Task;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TaskMapper extends com.baomidou.mybatisplus.core.mapper.BaseMapper<Task> {

    /**
     * 批量更新
     *
     * @param list 数据集合
     * @return 更新数
     */
    int batchSave(@Param("list") List<Task> list);
}
