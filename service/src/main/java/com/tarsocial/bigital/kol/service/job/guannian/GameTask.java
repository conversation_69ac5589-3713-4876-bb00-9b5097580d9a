package com.tarsocial.bigital.kol.service.job.guannian;

import com.tarsocial.bigital.kol.service.service.guangnian.GameDataProcessService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.bigital.kol.service.util.email.MailUtil;
import com.tarsocial.bigital.kol.service.util.email.SendMailBean;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.Executor;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class GameTask {

    @Resource
    private GameDataProcessService gameDataProcessService;

    @Resource
    @Qualifier("asyncExecutor")
    private Executor executor;

    @Resource
    private MailUtil mailUtil;

    @XxlJob("pushFunPlusPost")
    public void pushFunPlusPost() {
        Date now = new Date();
        Date start;
        Date end;
        if (Objects.equals(DateUtil.formatDate(now, "HH"), "17")) {
            // 获取当天的 10点 和 17点
            start = DateUtil.convertDate(DateUtil.formatDate(now, "yyyy-MM-dd") + " 10:00:00", "yyyy-MM-dd HH:mm:ss");
            end = DateUtil.convertDate(DateUtil.formatDate(now, "yyyy-MM-dd") + " 17:00:00", "yyyy-MM-dd HH:mm:ss");
        } else {
            end = DateUtil.convertDate(DateUtil.formatDate(now, "yyyy-MM-dd") + " 10:00:00", "yyyy-MM-dd HH:mm:ss");
            start = DateUtil.convertDate(DateUtil.formatDate(DateUtils.addDays(now, -1), "yyyy-MM-dd") + " 17:00:00", "yyyy-MM-dd HH:mm:ss");
        }
        SendMailBean bean = gameDataProcessService.postData(start, end);
        executor.execute(() -> mailUtil.sendAttachFileMail(bean));
    }
}
