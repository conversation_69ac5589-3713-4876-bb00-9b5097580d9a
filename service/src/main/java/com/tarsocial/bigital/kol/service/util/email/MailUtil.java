package com.tarsocial.bigital.kol.service.util.email;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MailUtil {

    @Resource
    private JavaMailSender javaMailSender;

    @Resource
    private TemplateEngine templateEngine;

    @Value("${spring.mail.username}")
    private String username;

    /**
     * 带附件邮件发送
     */
    public void sendAttachFileMail(SendMailBean sendMailBean) {
        try {
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            // true表示构建一个可以带附件的邮件对象
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);

            helper.setSubject(sendMailBean.getSubject());
            helper.setFrom(username, StringUtils.hasLength(sendMailBean.getUsername()) ? sendMailBean.getUsername() : "no-reply");
            helper.setTo(sendMailBean.getReceiver());
            helper.setCc(sendMailBean.getCcUser());
            helper.setSentDate(new Date());

            // 这里引入的是Template的Context
            Context context = new Context();
            // 设置模板中的变量
            sendMailBean.getTemplateMap().forEach(context::setVariable);
            // 第一个参数为模板的名称
            String process = templateEngine.process(TemplateTypeEnum.getTypeByCode(sendMailBean.getTemplateType()), context);

            if (sendMailBean.getHasAttachment().equals(true)) {
                // 第一个参数是自定义的名称，后缀需要加上，第二个参数是文件的位置
                helper.addAttachment(sendMailBean.getFileName() + ".xlsx", sendMailBean.getByteArrayResource());
            }
            // 第二个参数true表示这是一个html文本
            process = process.replaceAll("&lt;", "<");
            process = process.replaceAll("&gt;", ">");
            process = process.replaceAll("&#39;", "'");
            process = process.replaceAll("&quot;", "'");

            helper.setText(process, true);
            log.info("sendEmail start");
            javaMailSender.send(mimeMessage);
            log.info("sendEmail end");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
