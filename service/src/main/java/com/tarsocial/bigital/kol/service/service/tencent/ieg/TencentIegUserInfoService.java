package com.tarsocial.bigital.kol.service.service.tencent.ieg;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentIegUserInfo;
import com.tarsocial.bigital.kol.service.mapper.TencentIegUserInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
@Slf4j
public class TencentIegUserInfoService extends ServiceImpl<TencentIegUserInfoMapper, TencentIegUserInfo> {

    @Resource
    private TencentIegUserInfoMapper tencentIegUserInfoMapper;

    public void updateStatus(List<String> userIdList, String status) {
        UpdateWrapper<TencentIegUserInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("user_id", userIdList);
//        updateWrapper.set("last_status", status);
        updateWrapper.set("last_create_time", new Date());
        tencentIegUserInfoMapper.update(null, updateWrapper);
    }

}
