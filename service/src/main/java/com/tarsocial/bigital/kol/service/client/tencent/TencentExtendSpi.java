package com.tarsocial.bigital.kol.service.client.tencent;

import com.tarsocial.bigital.kol.common.domain.dto.tencent.PostReceiveRequest;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.TaskStatusRequest;
import com.tarsocial.bigital.kol.common.domain.request.tencent.TaskCreateRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "tencent-api", url = "${jv.server.url}", configuration = SignatureInterceptor.class)
public interface TencentExtendSpi {

    /**
     * 接收仟传文章数据接口
     * taskReceive
     *
     * @param request request
     * @return Object
     */
    @PostMapping("/topic/task/receive")
    Object taskReceive(@RequestBody PostReceiveRequest request);


    /**
     * 接收话题任务抓取完成标记接口
     * taskStatus
     *
     * @param request request
     * @return Object
     */
    @PostMapping("/topic/task/status")
    Object taskStatus(@RequestBody TaskStatusRequest request);
}
