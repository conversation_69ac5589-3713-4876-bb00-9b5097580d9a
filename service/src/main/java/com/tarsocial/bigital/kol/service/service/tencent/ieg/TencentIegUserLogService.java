package com.tarsocial.bigital.kol.service.service.tencent.ieg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentIegUserLog;
import com.tarsocial.bigital.kol.service.constants.TaskTypeEnum;
import com.tarsocial.bigital.kol.service.mapper.TencentIegUserLogMapper;
import com.tarsocial.dag.task.model.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @ClassName TaskService
 * @date 2024年03月26日
 */
@Service
@Slf4j
public class TencentIegUserLogService extends ServiceImpl<TencentIegUserLogMapper, TencentIegUserLog> {

    @Resource
    private TencentIegUserLogMapper userLogMapper;

    public List<TencentIegUserLog> queryUserLogByTask(Long taskId) {
        LambdaQueryWrapper<TencentIegUserLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TencentIegUserLog::getTaskId, taskId);
        return userLogMapper.selectList(wrapper);
    }

    public List<TencentIegUserLog> saveUserLog(List<String> userIds, String platform, TaskTypeEnum taskType, Long taskId, Integer businessType) {
        return saveUserLog(userIds, platform, taskType, taskId, TaskStatusEnum.PROGRESS.name(), null, null, businessType);
    }

    public List<TencentIegUserLog> saveUserLog(List<String> userIds, String platform, TaskTypeEnum taskType, Long taskId, String status, String errorMsg, String tenantName, Integer businessType) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        List<TencentIegUserLog> userLogs = Lists.newArrayList();
        try {
            String taskName = taskType.getDesc() + "-" + userIds.get(0);
            userIds.forEach(userId -> {
                TencentIegUserLog userLog = new TencentIegUserLog();
                userLog.setStatus(status);
                userLog.setSubTaskId(userId);
                userLog.setType(taskType.name());
                userLog.setTaskId(taskId);
                userLog.setPlatform(platform);
                userLog.setRetry(0);
                userLog.setTaskName(taskName);
                userLog.setTenantName(tenantName);
                userLog.setErrorDetail(errorMsg);
                userLog.setBusinessType(businessType);
                userLogs.add(userLog);
            });
            saveBatch(userLogs);
        } catch (Exception e) {
            log.error("saveUserLog error ", e);
            e.printStackTrace();
        }
        return userLogs;
    }

    public List<String> userIdList(List<String> ids, String platform, LocalDate gtCreateTime) {
        LambdaQueryWrapper<TencentIegUserLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TencentIegUserLog::getPlatform, platform).in(TencentIegUserLog::getSubTaskId, ids);
        if (gtCreateTime != null) {
            wrapper.gt(TencentIegUserLog::getCreateTime, gtCreateTime);
        }
        List<TencentIegUserLog> userLogs = userLogMapper.selectList(wrapper);
        return userLogs.stream().map(TencentIegUserLog::getSubTaskId).collect(Collectors.toList());
    }

    public List<String> userIdList(List<String> ids, String platform) {
        return userIdList(ids, platform, null);
    }

    public void updateTaskStatus(TencentIegUserLog userLog) {
        log.info("开始更新状态 ------》");
        userLogMapper.updateTaskStatus(userLog);
    }

    /**
     * 未完成的任务
     *
     * @return List
     */
    public List<TencentIegUserLog> userLogNoFinish(Integer businessType) {
        LambdaQueryWrapper<TencentIegUserLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(i -> i.isNull(TencentIegUserLog::getStatus).or().eq(TencentIegUserLog::getStatus, TaskStatusEnum.PROGRESS.name()))
                .eq(TencentIegUserLog::getBusinessType, businessType)
                .isNotNull(TencentIegUserLog::getTaskId)
                .lt(TencentIegUserLog::getRetry, 4);
        return userLogMapper.selectList(wrapper);
    }

    public void updateStatus(List<TencentIegUserLog> updateList) {
        userLogMapper.batchSave(updateList);
    }
}
