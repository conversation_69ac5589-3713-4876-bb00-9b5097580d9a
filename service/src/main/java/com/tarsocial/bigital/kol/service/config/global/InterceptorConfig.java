package com.tarsocial.bigital.kol.service.config.global;

import com.tarsocial.bigital.kol.service.config.sign.SignatureVerifyInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class InterceptorConfig implements WebMvcConfigurer {

    @Resource
    private GlobalRequestFilter globalRequestFilter;

    @Resource
    private SignatureVerifyInterceptor signatureVerifyInterceptor;

    /**
     * 配置需要验签的URL
     */
    @Value("${sign.intercept.list}")
    private String[] interceptList;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(globalRequestFilter).addPathPatterns("/**").order(Ordered.HIGHEST_PRECEDENCE);
        if (interceptList.length > 0) {
            // 接口验签
            registry.addInterceptor(signatureVerifyInterceptor).addPathPatterns(interceptList).order(Ordered.HIGHEST_PRECEDENCE);
        }
    }


}
