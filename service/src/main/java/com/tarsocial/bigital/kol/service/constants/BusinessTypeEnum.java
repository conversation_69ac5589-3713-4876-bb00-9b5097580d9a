package com.tarsocial.bigital.kol.service.constants;

import lombok.Getter;


@Getter
public enum BusinessTypeEnum {


    IEG_LIVE(1, "IEG-直播"),

    IEG_PUBLIC_PRICE(2, "IEG-刊例价"),

    REALTIME_KOL(3, "实时达人"),

    REALTIME_LIVE(4, "实时直播"),
    ;

    private final Integer code;

    private final String name;


    BusinessTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
