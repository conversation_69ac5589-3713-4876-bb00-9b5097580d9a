package com.tarsocial.bigital.kol.service.util.email;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.core.io.ByteArrayResource;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SendMailBean {

    /**
     * 邮件接收人
     */
    private String[] receiver;

    /**
     * 邮件主题
     */
    private String subject;

    /**
     * 邮件抄送人
     */
    private String[] ccUser;

    /**
     * 字节流文件
     */
    private ByteArrayResource byteArrayResource;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 邮件模板类型
     */
    private Integer templateType;

    /**
     * 有无附件
     */
    private Boolean hasAttachment = Boolean.FALSE;

    /**
     * 模板内数据
     */
    private Map<String, Object> templateMap = new HashMap<>();

    /**
     * 发件人名称
     */
    private String username;
}
