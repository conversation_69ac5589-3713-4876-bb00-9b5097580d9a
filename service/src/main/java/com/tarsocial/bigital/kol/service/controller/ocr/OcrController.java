package com.tarsocial.bigital.kol.service.controller.ocr;

import com.tarsocial.bigital.kol.common.domain.request.ocr.OcrRequest;
import com.tarsocial.bigital.kol.common.domain.entity.ocr.OcrPost;
import com.tarsocial.bigital.kol.service.service.ocr.OcrDataProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ocr")
@Slf4j
public class OcrController {

    @Resource
    private OcrDataProcessService ocrDataProcessService;

    @PostMapping("/post")
    public List<OcrPost> selectPostList(@RequestBody OcrRequest request) {
        return ocrDataProcessService.selectPostList(request.getIds(), request.getPlatform());
    }
}
