package com.tarsocial.bigital.kol.service.advice;

import com.tarsocial.dag.framework.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * 日志切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class LogAspect {

    @Pointcut("execution(* com.tarsocial.bigital.kol.service.controller.*.*(..))")
    public void restPointcut() {
        // 切点
    }


    @Before("restPointcut()")
    public void doBefore(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            StringBuilder params = new StringBuilder();
            try {
                for (Object arg : joinPoint.getArgs()) {
                    // MultipartFile参数打印文件名
                    if (arg instanceof MultipartFile) {
                        params.append(((MultipartFile) arg).getOriginalFilename());
                        continue;
                    }
                    params.append(JsonUtil.toJson(arg));
                }
                // 就一个打印参数，就不做特殊处理了
            } catch (Exception e) {
            }
            log.info("请求 IP：{}，请求路径：{}，请求参数：{}", request.getRemoteAddr(), request.getRequestURI(), params.toString());
        }
    }

}
