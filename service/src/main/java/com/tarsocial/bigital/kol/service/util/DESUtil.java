package com.tarsocial.bigital.kol.service.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> GoodLuck
 * @date 2023/12/4$ 11:20
 */
public class DESUtil {
    /**
     * Cipher类：
     * getInstance方法:
     * 加密算法：DES,DES3,AES,RSA
     * 模式：CBC(有向量模式)和ECB(无向量模式) 向量模式可以简单理解为偏移量，使用CBC需要定义一个IvParameterSpec对象
     * 填充模式: NoPadding: 加密内容不足8位用0补足8位  PKCS5Padding: 加密内容不足8位用余位数补足8位 如{197,97,97,97,97,97,2,2}
     * 参数按"算法/模式/填充模式
     * <p>
     * init方法参数：
     * Cipher.ENCRYPT_MODE(加密模式)和 Cipher.DECRYPT_MODE(解密模式)
     * key：SecretKeySpec和KeyGenerator 支持AES，DES，DES3三种加密算法创建密匙 KeyPairGenerator支持RSA加密算法创建密匙
     * params: 使用CBC模式时必须传入该参数 这边使用的是 IvParameterSpec
     * <p>
     * IvParameterSpec此类指定一个初始化向量 (IV)。使用 IV 的例子是反馈模式中的密码，如，CBC 模式中的 DES 和使用 OAEP 编码操作的 RSA 密码。
     */
    private static final String CIPHER_ALGORITHM = "DES/CBC/PKCS5Padding";
    /**
     * 不同加密算法所需要的值不一样 如：DES：8 AES:16
     */
    private static final int IV_SIZE = 8;
    public static final String KEY = "rck56453";
    private static final String KEY_CHARSET = "UTF-8";

    /**
     * @param input 加密字符串
     * @param key   密钥 不同加密算法所需要的值密钥Key长度不一样 如：8个字节（56位） AES: 密钥长度可以为128、192或256位，分组长度128位
     * @return
     */
    public static String aes256Enc(String input, String key) {
        byte[] keyBytes = key.getBytes();
        byte[] inputBytes = input.getBytes();
        byte[] ivBytes = new byte[IV_SIZE];

        try {
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "DES");
            IvParameterSpec iv = new IvParameterSpec(ivBytes);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, iv);
            byte[] encryptedBytes = cipher.doFinal(inputBytes);
            return bytesToHex(encryptedBytes).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 转换为16进制
     *
     * @param bytes
     * @return
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public static String decrypt(String cipherText, String key) {
        try {
            byte[] cipherBytes = hexToBytes(cipherText);
            byte[] keyBytes = key.getBytes(KEY_CHARSET);
            byte[] ivBytes = new byte[IV_SIZE];

            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "DES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] plainBytes = cipher.doFinal(cipherBytes);
            return new String(plainBytes, KEY_CHARSET);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] bytes = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            bytes[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return bytes;
    }

    public static String decryptKey(String cipherText) {
        try {
            byte[] cipherBytes = hexToBytes(cipherText);
            byte[] keyBytes = "rck56453".getBytes(KEY_CHARSET);
            byte[] ivBytes = new byte[IV_SIZE];

            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "DES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] plainBytes = cipher.doFinal(cipherBytes);
            return new String(plainBytes, KEY_CHARSET);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // 测试DES算法+CBC模式+PKCS5Padding填充模式结果
    public static void main(String[] args) {
        String encryptionStr = aes256Enc("abi-9he0jYt7", "rck56453");
        System.out.println("加密后:" + encryptionStr);
        // print：加密后:E583E178EB4DF1AD3AC3BCE687EB2C16
        String decryptStr = decrypt(encryptionStr, "rck56453");
        System.out.println("解密后:" + decryptStr);
        String decryptKey = decrypt(encryptionStr,KEY);
        System.out.println(decryptKey);
        // print：解密后:18866667777
    }
}
