package com.tarsocial.bigital.kol.service.service.tencent.ieg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.OauthClients;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.IegTokenRequest;
import com.tarsocial.bigital.kol.service.mapper.OauthClientsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @ClassName TaskService
 * @date 2024年03月26日
 */
@Service
@Slf4j
public class OauthClientsService extends ServiceImpl<OauthClientsMapper, OauthClients> {

    @Resource
    private OauthClientsMapper oauthClientsMapper;

    public OauthClients findUser(IegTokenRequest request) {
        LambdaQueryWrapper<OauthClients> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OauthClients::getClientId, request.getClient_id())
                .eq(OauthClients::getClientSecret, request.getClient_secret())
                .eq(OauthClients::getGrantTypes, request.getGrant_type()).last("limit 1");
        return oauthClientsMapper.selectOne(wrapper);
    }
}
