package com.tarsocial.bigital.kol.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.dto.mengniu.AlgorithmResultDto;
import com.tarsocial.bigital.kol.service.config.MinioFileProperties;
import com.tarsocial.bigital.kol.service.controller.MengniuHotTopicController;
import com.tarsocial.bigital.kol.service.service.DataCenterProcessService;
import com.tarsocial.bigital.kol.service.service.HotTopicJobService;
import com.tarsocial.bigital.kol.service.service.MengniuHotTopService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.bigital.kol.service.util.Util;
import com.tarsocial.enums.NullColumnEnum;
import com.tarsocial.enums.OperationEnum;
import com.tarsocial.enums.OrderTypeEnum;
import com.tarsocial.request.CommonKeyAndValue;
import com.tarsocial.request.DocSourceV1Request;
import com.tarsocial.request.FilterRequest;
import com.tarsocial.response.DocSourceResponse;
import com.tarsocial.vo.DocSourceVo;
import com.tarsocial.vo.FieldOrderVo;
import io.minio.DownloadObjectArgs;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

@Slf4j
@Service
public class HotTopicJobServiceImpl implements HotTopicJobService {

    private static final String FINISH = "finish";
    private static final String TABLE_PRE = "hot_topic.algorithm_result_";

    @Value("${mengniu.datasource.url}")
    private String url;
    @Value("${mengniu.datasource.user}")
    private String user;
    @Value("${mengniu.datasource.password}")
    private String password;

    @Resource
    private MinioFileProperties ossProperties;

    @Resource
    MengniuHotTopicController mengniuHotTopicController;

    /**
     * MinIO客户端
     */
    @Resource
    private MinioClient client;

    @Resource
    private MengniuHotTopService mengNiuHotTopService;

    @Resource
    private DataCenterProcessService dataCenterProcessService;


    private void initTable(String dataTime) throws SQLException {
        String dropSql = "DROP VIEW IF EXISTS hot_topic.result_" + dataTime + ";";

        String dropTableSql = String.format(
                "drop table IF EXISTS  %s;", TABLE_PRE + dataTime);

        String createTableSQL = String.format(
                "CREATE TABLE IF NOT EXISTS %s ( " +
                        "topic VARCHAR(255) PRIMARY KEY, " +
                        "platforms VARCHAR(255), " +
                        "topic_description TEXT, " +
                        "post_ids VARCHAR(500)," +
                        "post_list TEXT," +
                        "occurred_time VARCHAR(255)," +
                        "is_relevant BOOLEAN ," +
                        "reason TEXT, " +
                        "marketing_approach TEXT, " +
                        "post_json TEXT" +
                        ");"
                , TABLE_PRE + dataTime);

        executeSql(dropSql + dropTableSql + createTableSQL);
    }

    private void createView(String dataTime) throws SQLException {
        String sql = "LEFT JOIN (select *, jsonb_array_elements_text(post_list::jsonb) AS jsonb_array_elements_text from hot_topic.algorithm_result_" + dataTime +  ") b ON (a.kw_id::text IN (b.jsonb_array_elements_text::text))";
        String sql2 = "             mark,\n";
        if (!dataTime.startsWith("v2_")) {
            sql = "left join hot_topic.algorithm_result_" + dataTime + " b on b.topic = any (a.arr_topics)";
            sql2 = "";
        }

        String createSql = "create view hot_topic.result_" + dataTime + " as\n" +
                "(\n" +
                "select a.*, b.topic, b.platforms, b.topic_description, b.is_relevant\n" +
                "from (select kw_id,\n" +
                "             kw_platform,\n" +
                "             tx_content,\n" +
                "             tx_title,\n" +
                "             long_interaction,\n" +
                "             kw_hashtags,\n" +
                "             max_hot,\n" +
                "             kw_city,\n" +
                "             kw_province,\n" +
                "             kw_gender,\n" +
                "             kw_age,\n" +
                "             date_published,\n" +
                "             string_to_array(topics, ',') as arr_topics\n" +
                "      from hot_topic.mengniu_hot_topic_post_" + dataTime + ") as a\n" +
                sql +
                "         \n" +
                "where b.topic_description is not null and b.occurred_time is not null);";
        executeSql(createSql);
    }

    private void executeSql(String sql) throws SQLException {
        try (Connection conn = DriverManager.getConnection(url, user, password);
             Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
        }
    }

    public int saveData(InputStream inputStream, String tableName) throws IOException, SQLException {
        int size = 0;
        String insertSQL = String.format("INSERT INTO %s (topic, platforms, topic_description, post_ids, post_list, occurred_time, is_relevant, reason, marketing_approach, post_json ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", tableName);
        Connection conn = DriverManager.getConnection(url, user, password);
        PreparedStatement ps = conn.prepareStatement(insertSQL);
        conn.setAutoCommit(false); // 开始事务
        Set<String> reDuplicateTopic = new HashSet<String>();
        // 使用 BufferedReader 逐行读取 InputStream
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("read line tableName - > {} , line -> {}", tableName, line);
                size++;
                AlgorithmResultDto algorithmResultDto = JSON.parseObject(line, AlgorithmResultDto.class);
                if(reDuplicateTopic.contains(algorithmResultDto.getTopic_name())){
                    continue;
                }
                reDuplicateTopic.add(algorithmResultDto.getTopic_name());

                //最相关帖子
                List<String> post_ids = CollectionUtils.isEmpty(algorithmResultDto.getSamples()) ? algorithmResultDto.getPost_ids() : algorithmResultDto.getSamples();

                ps.setString(1, algorithmResultDto.getTopic_name());
                ps.setString(2, JSON.toJSONString(algorithmResultDto.getPlatform()));
                ps.setString(3, algorithmResultDto.getTopic_description());
                ps.setString(4, JSON.toJSONString(post_ids));
                ps.setString(5, JSON.toJSONString(algorithmResultDto.getPost_id_list()));
                ps.setString(6, "");
                ps.setBoolean(7, algorithmResultDto.getIs_relevant() != null && (algorithmResultDto.getIs_relevant() == 1));
                ps.setString(8, algorithmResultDto.getReason());
                ps.setString(9, algorithmResultDto.getMarketing_approach());
                ps.setString(10, "");

                //根据话题查询最早发生时间
//                String time = null;
//                try {
//                    time = queryOccurredTimeByHashTag(algorithmResultDto.getTopic_name());
//                } catch (Exception e) {
//                    log.error("获取话题最早发生时间失败...  topic -> {}", algorithmResultDto.getTopic_name());
//                }
                //查询相关帖子正文标题
                ps.addBatch();
            }
        }

        ps.executeBatch(); // 执行批量插入
        conn.commit(); // 提交事务
        return size;
    }


    @Override
    public void monitor() {
        //查询需要执行的任务
        String sql = "SELECT id, raw_file, result_file, status, data_time FROM hot_topic.log where status = 'algorithm_running' order by created_time DESC limit 1";

        try (Connection conn = DriverManager.getConnection(url, user, password);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            // 处理查询结果
            while (rs.next()) {
                Long id = rs.getLong("id");
                String status = rs.getString("status");
                String resultFile = rs.getString("result_file");
                String dataTime = rs.getString("data_time");
                if (!FINISH.equals(status)) {
                    executeTask(id, resultFile, dataTime);
                }
            }

        } catch (SQLException e) {
            log.error("【蒙牛Job】 查询任务失败 {}", e.getMessage(), e);
        }
    }

    @Override
    public void executeTask(Long id, String resultFile, String dateTime) throws SQLException {
        //表初始化
        initTable(dateTime);

        //数据插入
        InputStream stream = null;
        try {
            stream = client.getObject(
                    GetObjectArgs.builder()
                            .bucket(ossProperties.getBucketName())
                            .object(resultFile)
                            .build());
        } catch (Exception e) {
            log.error("读取minio InputStream 失败!   id -> {}", id);
        }

        if (stream == null) {
            return;
        }

        try {
            int size = saveData(stream, TABLE_PRE + dateTime);
            //修改状态
            if (size > 0) {
                createView(dateTime);
                String performance = mengNiuHotTopService.performance(dateTime);
                executeSql("UPDATE hot_topic.log SET status = 'finish', result_json = '" + performance.replaceAll("'", "''") + "' where id=" + id);
            }
        } catch (Exception e) {
            log.error("保存数据失败{}", e.getMessage(), e);
            executeSql("UPDATE hot_topic.log SET status = 'error', result_json = ' ' where id=" + id);
        } finally {
            try {
                stream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        mengNiuHotTopService.saveFinalResult(dateTime);
    }


    @Override
    public void downloadFile() {
        try {
            client.downloadObject(
                    DownloadObjectArgs.builder()
                            .bucket(ossProperties.getBucketName())
                            .object("HotTopicPost_20241107_test_description.jsonl")
                            .filename("./HotTopicPost_20241107_test_description.jsonl")
                            .build()
            );
        } catch (Exception e) {
            e.printStackTrace();
        }
    }




//    private String queryPostDetail(List<String> postIds) {
//        DocSourceV1Request request = new DocSourceV1Request();
//        request.setCount(10);
//        //空规则
//        request.setMainPartyId(95639L);
//        request.setFlatSource(true);
//        request.setArrayFlatSource(true);
//        request.setSourceFields(Lists.newArrayList("kw_id", "tx_content", "tx_title", "kw_platform"));
//        request.setPlatformIds(Arrays.asList(43L, 44L, 45L));
//        request.setKeepAlive(1);
//        request.setIndexType("post");
//
//
//        // 获取当前日期
//        Calendar calendar = Calendar.getInstance();
//        // 将日期设置为当前日期
//        calendar.setTime(new Date());
//        // 将日期调整为一个月前
//        calendar.add(Calendar.MONTH, -1);
//        // 开始时间和结束时间
//        request.setStartDate(DateUtil.getStartOfDay(calendar.getTime()));
//        request.setEndDate(DateUtil.getEndOfDay(new Date()));
//
//        //话题
//        List<FilterRequest> filters = Lists.newArrayList();
//        FilterRequest contentFilter = new FilterRequest();
//        contentFilter.setFilter(true);
//        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
//        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
//        commonKeyAndValue.setId(268L);
//        commonKeyAndValue.setValue(postIds);
//        ruleIds.add(commonKeyAndValue);
//        contentFilter.setRuleIds(ruleIds);
//        contentFilter.setOperationEnum(OperationEnum.equals);
//        filters.add(contentFilter);
//        request.setFilter(filters);
//        DocSourceResponse docSourceResponse = dataCenterProcessService.sourceV1(request);
//        List<DocSourceVo> rows = docSourceResponse.getRows();
//        List<Map<String, Object>> postDetail = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(rows)) {
//            rows.forEach(row -> postDetail.add(row.getSource()));
//        }
//        return JSON.toJSONString(postDetail);
//    }

    private String queryOccurredTimeByHashTag(String hashTag) {
        DocSourceV1Request request = new DocSourceV1Request();
        request.setCount(1);
        //空规则
        request.setMainPartyId(95639L);
        request.setFlatSource(true);
        request.setArrayFlatSource(true);
        request.setSourceFields(Lists.newArrayList("date_publishedAt", "kw_id", "kw_platform"));
        request.setPlatformIds(Arrays.asList(43L, 44L, 45L));
        request.setKeepAlive(1);
        request.setIndexType("post");


        //排序 发布时间ASC
        List<FieldOrderVo> orderBy = Lists.newArrayList();
        FieldOrderVo fieldOrderVo = new FieldOrderVo();
        fieldOrderVo.setName("date_publishedAt");
        fieldOrderVo.setOrderType(OrderTypeEnum.ASC);
        orderBy.add(fieldOrderVo);
        request.setOrderBy(orderBy);

        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        // 将日期设置为当前日期
        calendar.setTime(new Date());
        // 将日期调整为一个月前
        calendar.add(Calendar.MONTH, -1);
        // 开始时间和结束时间
        request.setStartDate(DateUtil.getStartOfDay(calendar.getTime()));
        request.setEndDate(DateUtil.getEndOfDay(new Date()));

        //话题
        List<FilterRequest> filters = Lists.newArrayList();
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setAllowNull(NullColumnEnum.EXCLUDE_NULL_COLUMN);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(310L);
        commonKeyAndValue.setValue(Lists.newArrayList(hashTag));
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.equals);
        filters.add(contentFilter);

        //排除评论
        FilterRequest contentFilter2 = new FilterRequest();
        contentFilter2.setFilter(true);
        contentFilter2.setAllowNull(NullColumnEnum.CONTAIN_NULL_COLUMN);
        List<CommonKeyAndValue> ruleIds2 = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue2 = new CommonKeyAndValue();
        commonKeyAndValue2.setId(312L);
        commonKeyAndValue2.setValue(Lists.newArrayList("评论"));
        ruleIds2.add(commonKeyAndValue2);
        contentFilter2.setRuleIds(ruleIds2);
        contentFilter2.setOperationEnum(OperationEnum.exclude);
        filters.add(contentFilter2);
        request.setFilter(filters);

        DocSourceResponse docSourceResponse = dataCenterProcessService.sourceV1(request);
        return docSourceResponse.getRows().get(0).getSource().get("date_publishedAt").toString();
    }

}
