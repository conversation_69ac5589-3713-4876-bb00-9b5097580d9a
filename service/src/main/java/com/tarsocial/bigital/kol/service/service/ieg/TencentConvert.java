package com.tarsocial.bigital.kol.service.service.ieg;

import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.BilibiliKolInfo;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.DouyinKolInfo;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.DouyinLiveInfo;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.KuaishouKolInfo;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.KuaishouLiveInfo;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.QuarkPostDto;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.RedKolInfo;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.WeiboKolInfo;
import com.tarsocial.bigital.kol.common.domain.entity.QuarkUserEntity;
import com.tarsocial.bigital.kol.common.domain.enums.PlatformEnum;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TencentConvert {

    /**
     * 短视频达人
     *
     * @param quarkUserInfo
     * @param platform
     * @param updateTime
     * @param userPos
     * @return
     */
    public static Object QuarkUserInfoConvert(QuarkUserEntity quarkUserInfo, String platform, String updateTime, List<QuarkPostDto> userPos) {
        if (quarkUserInfo == null) {
            return null;
        }

        if (PlatformEnum.DOUYIN.getCode().equals(platform)) {
            return toDouyinKolInfo(quarkUserInfo, updateTime, userPos);
        } else if (PlatformEnum.KUAISHOU.getCode().equals(platform)) {
            return toKuaishouKolInfo(quarkUserInfo, updateTime, userPos);
        } else if (PlatformEnum.BILIBILI.getCode().equals(platform)) {
            return toBilibiliKolInfo(quarkUserInfo, updateTime, userPos);
        } else if (PlatformEnum.WEIBO.getCode().equals(platform)) {
            return toWeiboKolInfo(quarkUserInfo, updateTime);
        } else if (PlatformEnum.XIAOHONGSHU.getCode().equals(platform)) {
            return toRedKolInfo(quarkUserInfo, updateTime, userPos);
        }
        return null;
    }


    /**
     * 直播达人
     *
     * @param quarkUserInfo
     * @param platform
     * @param updateTime
     * @return
     */
    public static Object QuarkUserInfoConvert(QuarkUserEntity quarkUserInfo, String platform, String updateTime) {
        if (quarkUserInfo == null) {
            return null;
        }

        if (PlatformEnum.DOUYIN.getCode().equals(platform)) {
            return toDouyinLiveInfo(quarkUserInfo, updateTime);
        } else if (PlatformEnum.KUAISHOU.getCode().equals(platform)) {
            return toKuaishouLiveInfo(quarkUserInfo, updateTime);
        }
        return null;
    }

    public static BilibiliKolInfo toBilibiliKolInfo(QuarkUserEntity quarkUserInfo, String updateTime, List<QuarkPostDto> userPos) {
        BilibiliKolInfo bilibiliKolInfo = new BilibiliKolInfo();
        bilibiliKolInfo.setNickname(quarkUserInfo.getNickname());
        bilibiliKolInfo.setUserId(quarkUserInfo.getUserId());
        bilibiliKolInfo.setFollowersCount(quarkUserInfo.getFollowersCount());
        bilibiliKolInfo.setUserTags(quarkUserInfo.getTags());


        QuarkUserEntity.ObjectStar objectStar = quarkUserInfo.getObjectStar();
        if (objectStar != null) {
            bilibiliKolInfo.setIsSettled(objectStar.getIsSettled());

            QuarkUserEntity.ObjectPriceInfos objectPriceInfos = objectStar.getObjectPriceInfos();
            if (objectPriceInfos != null) {
                bilibiliKolInfo.setPricePost(objectPriceInfos.getPricePost());
                bilibiliKolInfo.setPriceForward(objectPriceInfos.getPriceForward());
                bilibiliKolInfo.setPriceCustomer(objectPriceInfos.getPriceCustomer());
                bilibiliKolInfo.setPriceEmbedding(objectPriceInfos.getPriceEmbedding());
            }

            QuarkUserEntity.ObjectStarSpreadInfo objectSpreadInfo = objectStar.getObjectSpreadInfo();
            if (objectSpreadInfo != null) {
                QuarkUserEntity.ObjectN180d objectN180d = objectSpreadInfo.getObjectN180d();
                if (objectN180d != null) {
                    bilibiliKolInfo.setLikeMedian(objectN180d.getLikeMedian());
                    bilibiliKolInfo.setCommentMedian(objectN180d.getCommentMedian());
                    bilibiliKolInfo.setCollectMedian(objectN180d.getCollectMedian());
                    bilibiliKolInfo.setPlayMedian(objectN180d.getPlayMedian());
                    bilibiliKolInfo.setDanmuMedian(objectN180d.getDanmuMedian());
                }
            }
        }

        bilibiliKolInfo.setUpdateTime(updateTime);

        List<BilibiliKolInfo.PaidPostInfo> postInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userPos)) {
            userPos.forEach(post -> {
                BilibiliKolInfo.PaidPostInfo paidPostInfo = new BilibiliKolInfo.PaidPostInfo();
                paidPostInfo.setIsPaid(post.getIsPaid());
                paidPostInfo.setPostId(post.getId());
                paidPostInfo.setReadCount(post.getReadCount());
                paidPostInfo.setCoverUrl(post.getCoverUrl());
                paidPostInfo.setTitle(post.getTitle());
                paidPostInfo.setUrl(post.getUrl());
                paidPostInfo.setDuration(post.getDuration());
                paidPostInfo.setPublishTime(post.getPublishedAt());
                paidPostInfo.setRepostsCount(post.getRepostsCount());
                paidPostInfo.setCommentCount(post.getCommentsCount());
                paidPostInfo.setLikeCount(post.getLikeCount());
                paidPostInfo.setCollectCount(post.getCollectCount());
                paidPostInfo.setCoinCount(post.getCoinCount());
                paidPostInfo.setDanmuCount(post.getDanmuCount());
                //转发+评论+点赞+收藏+投币
                paidPostInfo.setInteraction(getLong(post.getRepostsCount()) + getLong(post.getCommentsCount()) + getLong(post.getLikeCount()) + getLong(post.getCollectCount()) + getLong(post.getCoinCount()));
                postInfoList.add(paidPostInfo);
            });
        }
        bilibiliKolInfo.setPostList(postInfoList);
        return bilibiliKolInfo;
    }

    public static WeiboKolInfo toWeiboKolInfo(QuarkUserEntity quarkUserInfo, String updateTime) {
        WeiboKolInfo weiboKolInfo = new WeiboKolInfo();
        weiboKolInfo.setNickname(quarkUserInfo.getNickname());
        weiboKolInfo.setUserId(quarkUserInfo.getUserId());
        weiboKolInfo.setFollowersCount(quarkUserInfo.getFollowersCount());
        weiboKolInfo.setUserTags(quarkUserInfo.getTags());


        QuarkUserEntity.ObjectStar objectStar = quarkUserInfo.getObjectStar();
        if (objectStar != null) {
            weiboKolInfo.setIsSettled(objectStar.getIsSettled());
            weiboKolInfo.setN90dCpm(objectStar.getExpectedCpm());
            weiboKolInfo.setN90dCpe(objectStar.getExpectedCpe());
            weiboKolInfo.setN90dRepostsCpm(objectStar.getExpectedCpmRepost());
            weiboKolInfo.setN90dRepostsCpe(objectStar.getExpectedCpeRepost());
            QuarkUserEntity.ObjectPriceInfos objectPriceInfos = objectStar.getObjectPriceInfos();
            if (objectPriceInfos != null) {
                weiboKolInfo.setPricePost(objectPriceInfos.getPricePost());
                weiboKolInfo.setPriceForward(objectPriceInfos.getPriceForward());
                weiboKolInfo.setPriceVideo(objectPriceInfos.getPrice1To20s());
                weiboKolInfo.setPriceImage(objectPriceInfos.getPriceImageArticle());
            }
            QuarkUserEntity.ObjectStarSpreadInfo objectSpreadInfo = objectStar.getObjectSpreadInfo();
            if (objectSpreadInfo != null) {
                QuarkUserEntity.ObjectN90d objectN30d = objectSpreadInfo.getObjectN30d();
                if (objectN30d != null) {
                    weiboKolInfo.setN30dReleaseMedian(objectN30d.getPostCount());
                    weiboKolInfo.setN30dReadMedian(objectN30d.getReadMedian());
                    weiboKolInfo.setN30dLikeMedian(objectN30d.getLikeMedian());
                    weiboKolInfo.setN30dCommentMedian(objectN30d.getCommentMedian());
                    weiboKolInfo.setN30dRepostsMedian(objectN30d.getRepostMedian());
                }
                QuarkUserEntity.ObjectN90d objectN90d = objectSpreadInfo.getObjectN90d();
                if (objectN90d != null) {
                    weiboKolInfo.setN90dReleaseMedian(objectN90d.getPostCount());
                    weiboKolInfo.setN90dReadMedian(objectN90d.getReadMedian());
                    weiboKolInfo.setN90dLikeMedian(objectN90d.getLikeMedian());
                    weiboKolInfo.setN90dCommentMedian(objectN90d.getCommentMedian());
                    weiboKolInfo.setN90dRepostsMedian(objectN90d.getRepostMedian());
                }
            }
        }
        weiboKolInfo.setUpdateTime(updateTime);
        return weiboKolInfo;
    }

    public static KuaishouKolInfo toKuaishouKolInfo(QuarkUserEntity quarkUserInfo, String updateTime, List<QuarkPostDto> userPos) {
        KuaishouKolInfo kuaishouKolInfo = new KuaishouKolInfo();
        kuaishouKolInfo.setNickname(quarkUserInfo.getNickname());
        kuaishouKolInfo.setPlatformUserId(quarkUserInfo.getPlatformUserId());
        kuaishouKolInfo.setUserId(quarkUserInfo.getUserId());
        QuarkUserEntity.ObjectStar objectStar = quarkUserInfo.getObjectStar();
        kuaishouKolInfo.setFollowersCount(quarkUserInfo.getFollowersCount());
        kuaishouKolInfo.setUserTags(quarkUserInfo.getTags());
        kuaishouKolInfo.setIndustryTags(quarkUserInfo.getIndustryTags());

        if (objectStar != null) {
            kuaishouKolInfo.setIsSettled(objectStar.getIsSettled());
            kuaishouKolInfo.setMcnName(objectStar.getMcnName());
            kuaishouKolInfo.setCpm(objectStar.getExpectedCpm());
            kuaishouKolInfo.setExpectedPlayNum(objectStar.getExpectedPlayNum());
            kuaishouKolInfo.setInteractRate(objectStar.getInterateRate());
            kuaishouKolInfo.setPlayOverRate(objectStar.getPlayOverRate());

            QuarkUserEntity.ObjectPriceInfos objectPriceInfos = objectStar.getObjectPriceInfos();
            if (objectPriceInfos != null) {
                kuaishouKolInfo.setPrice1To20s(objectPriceInfos.getPrice1To20s());
                kuaishouKolInfo.setPrice21To60s(objectPriceInfos.getPrice21To60s());
                kuaishouKolInfo.setPrice60sPlus(objectPriceInfos.getPrice60sPlus());
            }

            QuarkUserEntity.ObjectStarSpreadInfo objectSpreadInfo = objectStar.getObjectSpreadInfo();
            if (objectSpreadInfo != null) {
                QuarkUserEntity.ObjectN180d objectN180d = objectSpreadInfo.getObjectN180d();
                if (objectN180d != null) {
                    kuaishouKolInfo.setPlayMedian(objectN180d.getPlayMedian());
                }

                objectSpreadInfo.getObjectN30dPerson();
            }
        }


        //TODO  待确认
//        kuaishouKolInfo.setN30dLikeAvg(0.0D);
//        kuaishouKolInfo.setN30dCommentAvg(0.0D);
//        kuaishouKolInfo.setN30dShareAvg(0.0D);
//        kuaishouKolInfo.setN90dLikeAvg(0.0D);
//        kuaishouKolInfo.setN90dCommentAvg(0.0D);
//        kuaishouKolInfo.setN90dShareAvg(0.0D);

        kuaishouKolInfo.setUpdateTime(updateTime);


        List<KuaishouKolInfo.PaidPostInfo> postInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userPos)) {
            userPos.forEach(post -> {
                KuaishouKolInfo.PaidPostInfo paidPostInfo = new KuaishouKolInfo.PaidPostInfo();
                paidPostInfo.setIsPaid(post.getIsPaid());
                paidPostInfo.setPostId(post.getId());
                paidPostInfo.setReadCount(post.getReadCount());
                paidPostInfo.setCoverUrl(post.getCoverUrl());
                paidPostInfo.setTitle(post.getTitle());
                paidPostInfo.setUrl(post.getUrl());
                paidPostInfo.setDuration(post.getDuration());
                paidPostInfo.setPublishTime(post.getPublishedAt());
                paidPostInfo.setRepostsCount(post.getRepostsCount());
                paidPostInfo.setCommentCount(post.getCommentsCount());
                paidPostInfo.setLikeCount(post.getLikeCount());
                paidPostInfo.setCollectCount(post.getCollectCount());
                //转发+评论+点赞+收藏
                paidPostInfo.setInteraction(getLong(post.getRepostsCount()) + getLong(post.getCommentsCount()) + getLong(post.getLikeCount()) + getLong(post.getCollectCount()));
                postInfoList.add(paidPostInfo);
            });
        }
        kuaishouKolInfo.setPostList(postInfoList);
        return kuaishouKolInfo;
    }

    public static RedKolInfo toRedKolInfo(QuarkUserEntity quarkUserInfo, String updateTime, List<QuarkPostDto> userPos) {
        RedKolInfo redKolInfo = new RedKolInfo();
        redKolInfo.setNickname(quarkUserInfo.getNickname());
        redKolInfo.setPlatformUserId(quarkUserInfo.getPlatformUserId());
        redKolInfo.setUserId(quarkUserInfo.getUserId());
        QuarkUserEntity.ObjectStar objectStar = quarkUserInfo.getObjectStar();
        redKolInfo.setFollowersCount(quarkUserInfo.getFollowersCount());
        redKolInfo.setUserTags(quarkUserInfo.getTags());
        redKolInfo.setIndustryTags(quarkUserInfo.getIndustryTags());


        if (objectStar != null) {
            redKolInfo.setIsSettled(objectStar.getIsSettled());
            redKolInfo.setMcnId(objectStar.getMcnId());
            redKolInfo.setMcnName(objectStar.getMcnName());
            redKolInfo.setCpm(objectStar.getExpectedCpm());
            redKolInfo.setCpr(objectStar.getExpectedClick());
            redKolInfo.setPicCpm(objectStar.getExpectedPicCpm());
            redKolInfo.setPicCpr(objectStar.getExpectedPicPlay());

            QuarkUserEntity.ObjectPriceInfos objectPriceInfos = objectStar.getObjectPriceInfos();
            if (objectPriceInfos != null) {
                redKolInfo.setPriceVideo(objectPriceInfos.getPrice21To60s());
                redKolInfo.setPriceImage(objectPriceInfos.getPriceImageArticle());
            }

            QuarkUserEntity.ObjectStarSpreadInfo objectSpreadInfo = objectStar.getObjectSpreadInfo();
            if (objectSpreadInfo != null) {
                QuarkUserEntity.ObjectN90d objectN30d = objectSpreadInfo.getObjectN30d();
                redKolInfo.setN30dImpMedian(objectN30d.getImpMedian());
                redKolInfo.setN30dInteractionMedian(objectN30d.getInteractionMedian());
                redKolInfo.setN30dReadMedian(objectN30d.getReadMedian());
                redKolInfo.setN30dLikeMedian(objectN30d.getLikeMedian());
                redKolInfo.setN30dCommentMedian(objectN30d.getCommentMedian());
                redKolInfo.setN30dCollectMedian(objectN30d.getCollectMedian());
                redKolInfo.setN30dInteractionRate(objectN30d.getInteractionRate());
                redKolInfo.setN30dPlayOverRate(objectN30d.getPlayOverRate());
            }
        }

        redKolInfo.setUpdateTime(updateTime);

        List<RedKolInfo.PaidPostInfo> postInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userPos)) {
            userPos.forEach(post -> {
                RedKolInfo.PaidPostInfo paidPostInfo = new RedKolInfo.PaidPostInfo();
                paidPostInfo.setIsPaid(post.getIsPaid());
                paidPostInfo.setPostId(post.getId());
                paidPostInfo.setReadCount(post.getReadCount());
                paidPostInfo.setCoverUrl(post.getCoverUrl());
                paidPostInfo.setTitle(post.getTitle());
                paidPostInfo.setUrl(post.getUrl());
                paidPostInfo.setDuration(post.getDuration());
                paidPostInfo.setPublishTime(post.getPublishedAt());
                paidPostInfo.setRepostsCount(post.getRepostsCount());
                paidPostInfo.setCommentCount(post.getCommentsCount());
                paidPostInfo.setLikeCount(post.getLikeCount());
                paidPostInfo.setCollectCount(post.getCollectCount());
                //转发+评论+点赞+收藏
                paidPostInfo.setInteraction(getLong(post.getRepostsCount()) + getLong(post.getCommentsCount()) + getLong(post.getLikeCount()) + getLong(post.getCollectCount()));
                postInfoList.add(paidPostInfo);
            });
        }
        redKolInfo.setPostList(postInfoList);
        return redKolInfo;
    }


    public static DouyinKolInfo toDouyinKolInfo(QuarkUserEntity quarkUserInfo, String updateTime, List<QuarkPostDto> userPos) {
        DouyinKolInfo douyinKolInfo = new DouyinKolInfo();
        douyinKolInfo.setNickname(quarkUserInfo.getNickname());
        douyinKolInfo.setPlatformUserId(quarkUserInfo.getPlatformUserId());
        douyinKolInfo.setUserId(quarkUserInfo.getUserId());
        douyinKolInfo.setVerifiedType(quarkUserInfo.getVerifiedType());
        douyinKolInfo.setFollowersCount(quarkUserInfo.getFollowersCount());
        douyinKolInfo.setUserTags(quarkUserInfo.getTags());
        douyinKolInfo.setIndustryTags(quarkUserInfo.getIndustryTags());

        QuarkUserEntity.ObjectStar objectStar = quarkUserInfo.getObjectStar();
        if (objectStar != null) {
            douyinKolInfo.setIsSettled(objectStar.getIsSettled());
            douyinKolInfo.setMcnId(objectStar.getMcnId());
            douyinKolInfo.setMcnName(objectStar.getMcnName());
            douyinKolInfo.setExpectedPlayNum(objectStar.getExpectedPlayNum());


            QuarkUserEntity.ObjectPriceInfos objectPriceInfos = objectStar.getObjectPriceInfos();
            if (objectPriceInfos != null) {
                douyinKolInfo.setPrice1To20s(objectPriceInfos.getPrice1To20s());
                douyinKolInfo.setPrice21To60s(objectPriceInfos.getPrice21To60s());
                douyinKolInfo.setPrice60sPlus(objectPriceInfos.getPrice60sPlus());
            }


            QuarkUserEntity.ObjectStarSpreadInfo objectSpreadInfo = objectStar.getObjectSpreadInfo();
            if (objectSpreadInfo != null) {
                douyinKolInfo.setCpm(0.0D);
                douyinKolInfo.setCpm_1_20(0.0D);
                douyinKolInfo.setCpm_21_60(0.0D);
                douyinKolInfo.setCpm_60(0.0D);
                douyinKolInfo.setCpe(0.0D);
                douyinKolInfo.setCpe_1_20(0.0D);
                douyinKolInfo.setCpe_21_60(0.0D);
                douyinKolInfo.setCpe_60(0.0D);
                douyinKolInfo.setReadMedian(0L);
                douyinKolInfo.setPlayOverRate(0.0D);
                douyinKolInfo.setPlayOverRateBeyondRate(0.0D);
                douyinKolInfo.setInteractionRate(0.0D);
                douyinKolInfo.setInteractionBeyondRate(0.0D);
                douyinKolInfo.setN30dPlayMedian(0L);
                douyinKolInfo.setN30dPlayOverRate(0.0D);
                douyinKolInfo.setN30dInteractionRate(0.0D);
                douyinKolInfo.setN90dPlayMedian(0L);
                douyinKolInfo.setN90dPlayOverRate(0.0D);
                douyinKolInfo.setN90dInteractionRate(0.0D);
            }
        }

        douyinKolInfo.setUpdateTime(updateTime);


        List<DouyinKolInfo.PaidPostInfo> postInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userPos)) {
            userPos.forEach(post -> {
                DouyinKolInfo.PaidPostInfo paidPostInfo = new DouyinKolInfo.PaidPostInfo();
                paidPostInfo.setIsPaid(post.getIsPaid());
                paidPostInfo.setPostId(post.getId());
                paidPostInfo.setReadCount(post.getReadCount());
                paidPostInfo.setCoverUrl(post.getCoverUrl());
                paidPostInfo.setTitle(post.getTitle());
                paidPostInfo.setUrl(post.getUrl());
                paidPostInfo.setDuration(post.getDuration());
                paidPostInfo.setPublishTime(post.getPublishedAt());
                paidPostInfo.setRepostsCount(post.getRepostsCount());
                paidPostInfo.setCommentCount(post.getCommentsCount());
                paidPostInfo.setLikeCount(post.getLikeCount());
                paidPostInfo.setCollectCount(post.getCollectCount());
                //转发+评论+点赞+收藏
                paidPostInfo.setInteraction(getLong(post.getRepostsCount()) + getLong(post.getCommentsCount()) + getLong(post.getLikeCount()) + getLong(post.getCollectCount()));
                postInfoList.add(paidPostInfo);
            });
        }
        douyinKolInfo.setPostList(postInfoList);
        return douyinKolInfo;
    }

    public static long getLong(Long value) {
        if (value == null) {
            return 0;
        }
        return value;
    }


    /**
     * 快手直播
     *
     * @param quarkUserInfo
     * @param updateTime
     * @return
     */
    public static KuaishouLiveInfo toKuaishouLiveInfo(QuarkUserEntity quarkUserInfo, String updateTime) {
        KuaishouLiveInfo kuaishouLiveInfo = new KuaishouLiveInfo();
        kuaishouLiveInfo.setNickname(quarkUserInfo.getNickname());
        kuaishouLiveInfo.setKolId(quarkUserInfo.getKolId());
        kuaishouLiveInfo.setGender(quarkUserInfo.getGender());
        kuaishouLiveInfo.setVerifiedType(quarkUserInfo.getVerifiedType());
        kuaishouLiveInfo.setFollowersCount(quarkUserInfo.getFollowersCount());
        kuaishouLiveInfo.setUserTags(quarkUserInfo.getTags());
        kuaishouLiveInfo.setIndustryTags(quarkUserInfo.getIndustryTags());

        QuarkUserEntity.ObjectStar objectStar = quarkUserInfo.getObjectStar();
        if (objectStar != null) {
            kuaishouLiveInfo.setMcnName(objectStar.getMcnName());
            kuaishouLiveInfo.setIsLivePlaza(objectStar.getIsSettled());
        }

        QuarkUserEntity.ObjectLiveStar objectLiveStar = quarkUserInfo.getObjectLiveStar();
        if (objectLiveStar != null) {
            kuaishouLiveInfo.setExpectancyPlayCnt(objectLiveStar.getExpectancyPlayCnt());
            kuaishouLiveInfo.setExpectancyMaxOnlineCnt(objectLiveStar.getExpectancyMaxOnlineCnt());
            kuaishouLiveInfo.setExpectancyCPM(objectLiveStar.getExpectancyCPM());
            kuaishouLiveInfo.setWeeklyLiveCnt(objectLiveStar.getWeeklyLiveCnt());
            kuaishouLiveInfo.setExpectancyPcuCnt(objectLiveStar.getExpectancyPcuCnt());


            List<QuarkUserEntity.ObjectLatestLiveRooms> objectLatestLiveRooms = objectLiveStar.getObjectLatestLiveRooms();
            if (!CollectionUtils.isEmpty(objectLatestLiveRooms)) {
                List<KuaishouLiveInfo.ObjectLatestLiveRooms> rooms = Lists.newArrayList();
                objectLatestLiveRooms.forEach(room -> {
                    KuaishouLiveInfo.ObjectLatestLiveRooms objRoom = new KuaishouLiveInfo.ObjectLatestLiveRooms();
                    objRoom.setTitle(room.getTitle());
                    //TODO  存疑字段
                    objRoom.setRoomId(room.getId());
                    objRoom.setCoverUrl(room.getCoverUrl());
                    objRoom.setPublishedAt(room.getPublishedAt());
                    objRoom.setDuration(room.getDuration());
                    objRoom.setWatchCount(room.getWatchUCount());
                    rooms.add(objRoom);
                });
                kuaishouLiveInfo.setObjectLatestLiveRooms(rooms);
            }

            QuarkUserEntity.ObjectPriceInfos objectPriceInfos = objectLiveStar.getObjectPriceInfos();
            if (objectPriceInfos != null) {
                kuaishouLiveInfo.setPriceBrandPromotionByHour(objectPriceInfos.getPriceBrandPromotionByHour());
                kuaishouLiveInfo.setPriceBrandPromotionByDay(objectPriceInfos.getPriceBrandPromotionByDay());
                kuaishouLiveInfo.setPriceBrandPromotionByGoods(objectPriceInfos.getPriceBrandPromotionByGoods());

            }


            QuarkUserEntity.ObjectSpreadInfo objectSpreadInfo = objectLiveStar.getObjectSpreadInfo();
            if (objectSpreadInfo != null) {
                QuarkUserEntity.ObjectN30d objectN30d = objectSpreadInfo.getObjectN30d();
                if (objectN30d != null) {
                    kuaishouLiveInfo.setLiveCnt(objectN30d.getLiveCnt());
                    kuaishouLiveInfo.setWatchUCnt(objectN30d.getWatchUCnt());
                }
            }

        }

        kuaishouLiveInfo.setUpdateTime(updateTime);
        return kuaishouLiveInfo;
    }

    /**
     * 抖音直播
     *
     * @param quarkUserInfo
     * @param updateTime
     * @return
     */
    public static DouyinLiveInfo toDouyinLiveInfo(QuarkUserEntity quarkUserInfo, String updateTime) {
        DouyinLiveInfo douyinLiveInfo = new DouyinLiveInfo();
        douyinLiveInfo.setNickname(quarkUserInfo.getNickname());
        douyinLiveInfo.setKolId(quarkUserInfo.getKolId());
        douyinLiveInfo.setGender(quarkUserInfo.getGender());
        douyinLiveInfo.setVerifiedType(quarkUserInfo.getVerifiedType());
        douyinLiveInfo.setFollowersCount(quarkUserInfo.getFollowersCount());
        douyinLiveInfo.setUserTags(quarkUserInfo.getTags());
        douyinLiveInfo.setIndustryTags(quarkUserInfo.getIndustryTags());

        QuarkUserEntity.ObjectStar objectStar = quarkUserInfo.getObjectStar();
        if (objectStar != null) {
            douyinLiveInfo.setMcnName(objectStar.getMcnName());
            douyinLiveInfo.setIsLivePlaza(objectStar.getIsSettled());
        }
        QuarkUserEntity.ObjectLiveStar objectLiveStar = quarkUserInfo.getObjectLiveStar();
        if (objectLiveStar != null) {
            QuarkUserEntity.ObjectPriceInfos objectPriceInfos = objectLiveStar.getObjectPriceInfos();
            if (objectPriceInfos != null) {
                douyinLiveInfo.setPriceBrandPromotionByHour(objectPriceInfos.getPriceBrandPromotionByHour());
                douyinLiveInfo.setPriceBrandPromotionByDay(objectPriceInfos.getPriceBrandPromotionByDay());
            }

            QuarkUserEntity.ObjectSpreadInfo objectSpreadInfo = objectLiveStar.getObjectSpreadInfo();
            if (objectSpreadInfo != null) {
                QuarkUserEntity.ObjectN30d objectN30d = objectSpreadInfo.getObjectN30d();
                if (objectN30d != null) {
                    douyinLiveInfo.setLiveCnt(objectN30d.getLiveCnt());
                    douyinLiveInfo.setWatchUCnt(objectN30d.getWatchUCnt());
                    douyinLiveInfo.setWatchCnt(objectN30d.getWatchCnt());
                    douyinLiveInfo.setHandleCtr(objectN30d.getHandleCtr());
                    douyinLiveInfo.setHandleCrv(objectN30d.getHandleCrv());
                    douyinLiveInfo.setSnowflakeCtr(objectN30d.getSnowflakeCtr());
                    douyinLiveInfo.setWindmillCtr(objectN30d.getWindmillCtr());
                    douyinLiveInfo.setAcu(objectN30d.getAcu());
                    douyinLiveInfo.setPcu(objectN30d.getPcu());
                    douyinLiveInfo.setAcuPercent(objectN30d.getAcuPercent());
                    douyinLiveInfo.setCtr(objectN30d.getCtr());
                    douyinLiveInfo.setCommentRate(objectN30d.getCommentRate());
                    douyinLiveInfo.setLikeRate(objectN30d.getLikeRate());
                    douyinLiveInfo.setAvgWatchDuration(objectN30d.getAvgWatchDuration());
                    douyinLiveInfo.setFansWatchRate(objectN30d.getFansWatchRate());
                }
            }

            List<QuarkUserEntity.ObjectLatestLiveRooms> objectLatestLiveRooms = objectLiveStar.getObjectLatestLiveRooms();
            if (!CollectionUtils.isEmpty(objectLatestLiveRooms)) {
                List<DouyinLiveInfo.ObjectLatestLiveRooms> rooms = Lists.newArrayList();
                objectLatestLiveRooms.forEach(room -> {
                    DouyinLiveInfo.ObjectLatestLiveRooms objRoom = new DouyinLiveInfo.ObjectLatestLiveRooms();
                    objRoom.setTitle(room.getTitle());
                    objRoom.setAcu(room.getAcu());
                    objRoom.setPcu(room.getPcu());
                    objRoom.setCommentRate(room.getCommentRate());
                    objRoom.setLikeRate(room.getLikeRate());
                    objRoom.setDuration(room.getDuration());
                    objRoom.setPublishedAt(room.getPublishedAt());
                    objRoom.setCoverUrl(room.getCoverUrl());
                    objRoom.setRoomId(room.getId());
                    objRoom.setWatchUCount(room.getWatchUCount());
                    objRoom.setWatchCount(room.getReadCount());
                    objRoom.setCtr(room.getCtr());
                    rooms.add(objRoom);
                });
                douyinLiveInfo.setObjectLatestLiveRooms(rooms);
            }

        }

        douyinLiveInfo.setUpdateTime(updateTime);
        return douyinLiveInfo;
    }
}
