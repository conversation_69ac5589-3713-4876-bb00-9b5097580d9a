package com.tarsocial.bigital.kol.service.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.AnalyzeRequest;
import org.elasticsearch.client.indices.AnalyzeResponse;
import org.elasticsearch.client.indices.AnalyzeResponse.AnalyzeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * @Author: goodluck
 * @createTime: 2024/4/9 16:34
 * @Description: TODO
 */
@Slf4j
@Component
public class AnalyseDistanceUtil {

    @Autowired
    public AnalyseDistanceUtil(@Qualifier(value = "clientHighLevel") RestHighLevelClient clientQuark) {
        restHighLevelClient = clientQuark;
    }

    private static RestHighLevelClient restHighLevelClient;

    static TermAndPosition position;

    // 外部类的静态成员变量
    private static String outerTerm = "";
    private static int outerPosition = 0;

    class CommonConstants {
        /**
         * ik_max_word
         */
        public static final String IK_MAX_WORD = "ik_max_word";

        /**
         * smart分词
         */
        public static final String IK_SMART = "ik_smart";

        /**
         * smart分词
         */
        public static final String SPECICY_SYMBOL_STRING = "~";
        /**
         * 没有匹配到结果
         */
        public static final int NO_RESULT = -999999;
    }

    @Data
    public static class TermAndPosition {
        /**
         * 关键词
         */
        private String term;
        /**
         * 下标位置
         */
        private int position;

        // 构造方法
        public TermAndPosition() {
            // 在内部类的构造方法中访问外部类的静态成员变量
            this.term = outerTerm;
            this.position = outerPosition;
        }
    }

    private static final String REGEX_STRING = "~(-?\\d+)";
    /**
     * 转义冒号
     */
    private static final String ESCAPE_COLON = "\"";
    /**
     * 分隔符
     */
    private static final String SEPARATOR_STRING = "~";


    public static boolean isOnlySpaces(String str) {
        // 定义匹配只包含空格的正则表达式
        String regex = "\\s+";
        // 使用正则表达式匹配字符串
        return str.matches(regex);
    }

    public static int containsSpecialChar(String text) {
        if (!text.contains(CommonConstants.SPECICY_SYMBOL_STRING)) {
            return CommonConstants.NO_RESULT;
        }
        Pattern pattern = Pattern.compile(REGEX_STRING);
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            String match = matcher.group(1);
            return Integer.parseInt(match);
        }
        return CommonConstants.NO_RESULT;
    }

    private static int comparisonOfResults(TermAndPosition ikResult, TermAndPosition smartResult) {
        //快速返回
        return Math.abs(ikResult.getPosition() - smartResult.getPosition());
    }

    public static String[] multiAnalyzeDifWithEs(String[] params) {
        if (params == null || params.length == 0) {
            return new String[]{};
        }
        //先拆分
        List<String> response = Lists.newArrayList();
        for (String text : params) {
            StringBuilder buffer = new StringBuilder();
            buffer.append(ESCAPE_COLON);
            if (StrUtil.isEmpty(text) || isOnlySpaces(text)) {
                response.add(text);
                continue;
            }
            try {
                //2023-04-26 麻烦明天调整一下，max-smart大于0的时候，差需要+1
                //如果不包含特殊字符串返回-1，包含返回的是绝对值;
                int specialNum = containsSpecialChar(text);
                if (specialNum != CommonConstants.NO_RESULT) {
                    //干掉特殊符号
                    String pattern = "~-?\\d+";
                    // 将模式匹配的部分替换成空字符串
                    String result = text.replaceAll(pattern, "｜");
                    String[] parts = result.split("｜");
                    int count = 0;
                    for (String part : parts) {
                        String trim = part.trim();
                        int difference = getDifference(trim);
                        buffer.append(trim).append(" ");
                        count += difference;
                    }
                    // 删除最后一个空格符
                    buffer.deleteCharAt(buffer.length() - 1);
                    buffer.append(ESCAPE_COLON);
                    if ((count + specialNum) > 0) {
                        buffer.append(SEPARATOR_STRING).append(count + specialNum);
                    }
                    response.add(buffer.toString());
                } else {
                    String trim = text.trim();
                    int difference = getDifference(trim);
                    if (difference == 0) {
                        buffer.append(trim).append("\"");
                        response.add(buffer.toString());
                        continue;
                    }
                    buffer.append(trim).append("\"").append("~" + difference);
                    response.add(buffer.toString());
                }
            } catch (IOException e) {
                log.error("call elasticsearch analyze error {}", e.getMessage());
                throw new RuntimeException(e);
            }
        }
        return response.toArray(new String[0]);
    }


    public static int getDifference(String text) throws IOException {

        TermAndPosition smartResult = getAnalyzeText(CommonConstants.IK_SMART, text, "");
        TermAndPosition ikResult = getAnalyzeText(CommonConstants.IK_MAX_WORD, text, smartResult.getTerm());
        int difference = comparisonOfResults(ikResult, smartResult);
        return difference > 0 ? difference + 1 : difference;
    }


    //public static TermAndPosition getAnalyzeText(String tokenizer, String text, String lastPositionName) throws IOException {
    //    TermAndPosition position = new TermAndPosition();
    //    if (StrUtil.isEmpty(text)) {
    //        return position;
    //    }
    //    AnalyzeRequest request = AnalyzeRequest.withGlobalAnalyzer(tokenizer, text);
    //
    //    AnalyzeResponse analyze = restHighLevelClient.indices().analyze(request, RequestOptions.DEFAULT);
    //    if (CollUtil.isEmpty(analyze.getTokens())) {
    //        position.setTerm("");
    //        position.setPosition(0);
    //    } else {
    //        AnalyzeResponse.AnalyzeToken analyzeToken = null;
    //        if (StrUtil.isNotEmpty(lastPositionName)) {
    //            List<AnalyzeToken> tokens = analyze.getTokens();
    //            if (CollUtil.isNotEmpty(tokens)) {
    //                for (AnalyzeToken token : tokens) {
    //                    if (token.getTerm().equals(lastPositionName)) {
    //                        analyzeToken = token;
    //                    }
    //                }
    //            }
    //        } else {
    //            analyzeToken = analyze.getTokens().get(analyze.getTokens().size() - 1);
    //        }
    //        if (analyzeToken != null) {
    //            position.setPosition(analyzeToken.getPosition());
    //            position.setTerm(analyzeToken.getTerm());
    //        }
    //    }
    //    return position;
    //}


    public static TermAndPosition getAnalyzeText(String tokenizer, String text, String lastPositionName) throws IOException {
        TermAndPosition position = new TermAndPosition();
        if (StrUtil.isEmpty(text)) {
            return position;
        }
        AnalyzeRequest request = AnalyzeRequest.withGlobalAnalyzer(tokenizer, text);

        AnalyzeResponse analyze = restHighLevelClient.indices().analyze(request, RequestOptions.DEFAULT);
        if (CollUtil.isEmpty(analyze.getTokens())) {
            position.setTerm("");
            position.setPosition(0);
        } else {
            AnalyzeResponse.AnalyzeToken analyzeToken = null;
            if (StrUtil.isNotEmpty(lastPositionName)) {
                List<AnalyzeToken> tokens = analyze.getTokens();
                if (CollUtil.isNotEmpty(tokens)) {
                    for (AnalyzeToken token : tokens) {
                        if (token.getTerm().equals(lastPositionName)) {
                            analyzeToken = token;
                        }
                    }
                }
            } else {
                analyzeToken = analyze.getTokens().get(analyze.getTokens().size() - 1);
            }
            if (analyzeToken != null) {
                position.setPosition(analyzeToken.getPosition());
                position.setTerm(analyzeToken.getTerm());
            }
        }
        return position;
    }

}
