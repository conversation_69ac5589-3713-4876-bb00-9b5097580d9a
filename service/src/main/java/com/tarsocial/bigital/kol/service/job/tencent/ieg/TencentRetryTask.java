package com.tarsocial.bigital.kol.service.job.tencent.ieg;


import com.tarsocial.bigital.kol.service.service.ieg.IegProcessService;
import com.tarsocial.bigital.kol.service.service.ieg.RealtimeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Executor;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TencentRetryTask {

    @Resource
    private IegProcessService iegProcessService;

    @Resource
    private RealtimeService realtimeService;

    @Resource
    @Qualifier("asyncExecutor")
    private Executor asyncExecutor;

    @XxlJob("taskRetry")
    public void taskRetry() {
        asyncExecutor.execute(() -> iegProcessService.taskRetry());
    }

    @XxlJob("publicPriceTaskUpdate")
    public void publicPriceTaskUpdate() {
        asyncExecutor.execute(() -> iegProcessService.publicPriceTaskUpdate());
    }

    @XxlJob("perMinuteJOB")
    public void perMinuteJOB() {
        asyncExecutor.execute(() -> realtimeService.perMinuteJOB());
    }

}
