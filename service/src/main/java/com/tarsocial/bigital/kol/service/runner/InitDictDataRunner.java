package com.tarsocial.bigital.kol.service.runner;

import com.tarsocial.bigital.kol.common.domain.entity.AbiDictEntity;
import com.tarsocial.bigital.kol.common.domain.entity.PepsicoDictEntity;
import com.tarsocial.bigital.kol.service.service.DictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/13
 */
@Component
@Slf4j
public class InitDictDataRunner implements CommandLineRunner {

    @Resource
    DictService dictService;

    public static Set<PepsicoDictEntity> DICT_DATA_BY_TYPE = new HashSet<>();
    public static List<AbiDictEntity> DICT_DATA_ABI = new ArrayList<>();


    @Override
    public void run(String... args) {
        DICT_DATA_BY_TYPE = dictService.listAllByType();
        DICT_DATA_ABI = dictService.listAllAbi();
        log.info("加载 PepsiCo count = {} ------", DICT_DATA_BY_TYPE.size());
    }


    public void refresh() {
        log.info("------refresh 加载租户表数据------");
    }
}
