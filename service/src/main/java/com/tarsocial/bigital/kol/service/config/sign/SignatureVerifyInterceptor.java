package com.tarsocial.bigital.kol.service.config.sign;

import com.tarsocial.bigital.kol.common.domain.entity.tencent.App;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import com.tarsocial.bigital.kol.service.service.tencent.AppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SignatureVerifyInterceptor implements HandlerInterceptor {

    @Resource
    private AppService appService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        SignEntity sign = SignUtils.dislodge(request);
        log.info("request sign verify : {}", sign);

        if (!sign.usedSignature()) {
            throw new BusinessException("请求验签 未签名请求：" + request.getRequestURL());
        }

        App app = appService.selectAppByKey(sign.getAppId());

        if (null == app) {
            throw new BusinessException("请求验签 客户端不存在：" + request.getRequestURL());
        }

        String signCheck = sign.countersign(app.getSecret());

        if (!sign.getSign().equals(signCheck)) {
            throw new BusinessException("请求验签 签名不通过, 接口 : " + request.getRequestURL() + ", 参数：" + sign);
        }

        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

}
