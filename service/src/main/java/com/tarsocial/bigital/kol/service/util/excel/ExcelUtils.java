package com.tarsocial.bigital.kol.service.util.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel工具类
 *
 * <AUTHOR>
 **/
@Slf4j
public class ExcelUtils {

    /**
     * 读取Excel操作
     *
     * @return 包含Excel数据的list集合
     */
    public static <T> List<T> readExcel(MultipartFile file, Class<T> tClass, int sheetNo, int rowNum) {
        InputStream inputStream = null;
        try {
            inputStream = new BufferedInputStream(file.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        ExcelListener excelListener = new ExcelListener();
        List<T> list = new ArrayList<>();
        EasyExcel.read(inputStream)
                //反射获取类型
                .head(tClass)
                //读取的excel的sheet索引
                .sheet(sheetNo)
                // 起始行
                .headRowNumber(rowNum)
                // 注册监听器
                .registerReadListener(excelListener).doRead();
        return excelListener.getList();
    }

    /**
     * 将数据写入Excel
     *
     * @param response  http响应
     * @param clazz     使用的目标对象
     * @param fileName  下载时文件的名字
     * @param sheetName 写入到文件时对应的sheet的名称
     * @param dataList  要写入的数据的集合
     */
    public static void writeToExcel(HttpServletResponse response, Class clazz, String fileName, String sheetName, List dataList) {
        try {
            //处理浏览器下载/导出文件时中文错误显示问题
            fileName = new String(fileName.getBytes(), "ISO-8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        ServletOutputStream outputStream = null;
        //设置响应内容类型（内容类型可以和编码一起设置）
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        //设置响应头（inline ：将文件内容直接显示在页面；attachment：弹出对话框让用户下载）
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName + ".xlsx");
        try {
            outputStream = response.getOutputStream();
            EasyExcel.write(outputStream, clazz).sheet(sheetName).doWrite(dataList);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 读取 Excel(多个 sheet)
     *
     * @param file  文件
     * @param clazz 实体类映射，继承 BaseRowModel 类
     * @return Excel 数据 list
     */
    public static <T> List<T> readExcel(MultipartFile file, Class<T> clazz) {
        return readExcel(file, clazz, 0, 1);
    }

    /**
     * 导出
     */
    public static void writeExcel(HttpServletResponse response, List<? extends Object> data, String fileName, String sheetName, Class clazz) throws Exception {
        //表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        //设置表头居中对齐
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        //内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置内容靠左对齐
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        EasyExcel.write(getOutputStream(fileName, response), clazz).excelType(ExcelTypeEnum.XLSX).sheet(sheetName).registerWriteHandler(horizontalCellStyleStrategy).doWrite(data);
    }

    private static OutputStream getOutputStream(String fileName, HttpServletResponse response) throws Exception {
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        return response.getOutputStream();
    }

}
