package com.tarsocial.bigital.kol.service.util;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.json.JsonData;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class QueryUtil {

    private QueryUtil() {
    }

    public static Query exists(String field) {
        return Query.of(q1 -> q1.exists(e1 -> e1.field(field)));
    }

    public static <T> Query rangeEqu(String field, T gte, T lte) {
        return range(field, null, gte, null, lte);
    }

    public static <T> Query range(String field, T gt, T lt) {
        return range(field, gt, null, lt, null);
    }

    public static <T> Query range(String field, T gt, T gte, T lt, T lte) {
        return Query.of(q -> q.range(
                q2 -> {
                    RangeQuery.Builder query = q2.field(field);
                    if (gt != null) {
                        query.gt(JsonData.of(gt));
                    }
                    if (gte != null) {
                        query.gte(JsonData.of(gte));
                    }
                    if (lt != null) {
                        query.lt(JsonData.of(lt));
                    }
                    if (lte != null) {
                        query.lte(JsonData.of(lte));
                    }
                    return query;
                }
        ));
    }

    public static Query term(String field, Long value) {
        return Query.of(q -> q.term(q2 -> q2.field(field).value(value)));
    }

    public static Query term(String field, Integer value) {
        return Query.of(q -> q.term(q2 -> q2.field(field).value(value)));
    }

    public static Query term(String field, Double value) {
        return Query.of(q -> q.term(q2 -> q2.field(field).value(value)));
    }

    public static Query term(String field, String value) {
        return Query.of(q -> q.term(q2 -> q2.field(field).value(value)));
    }

    public static Query term(String field, Boolean value) {
        return Query.of(q -> q.term(q2 -> q2.field(field).value(value)));
    }

    public static <T> Query terms(String field, List<T> value) {
        return Query.of(q -> q.terms(q2 -> q2.field(field).terms(q3 -> q3.value(value.stream().map(it -> {
            FieldValue fieldValue;
            if (it instanceof Long) {
                fieldValue = FieldValue.of((Long) it);
            } else if (it instanceof Double) {
                fieldValue = FieldValue.of((Double) it);
            } else if (it instanceof Boolean) {
                fieldValue = FieldValue.of((Boolean) it);
            } else if (it instanceof Integer) {
                fieldValue = FieldValue.of((Integer) it);
            } else {
                fieldValue = FieldValue.of(it.toString());
            }
            return fieldValue;
        }).collect(Collectors.toList())))));
    }


    public static BoolQuery.Builder bool() {
        return new BoolQuery.Builder();
    }

    public static Query should(List<Query> list) {
        return bindQuery(list, bool()::should);
    }

    public static Query must(List<Query> list) {
        return bindQuery(list, bool()::must);
    }

    public static Query mustNot(List<Query> list) {
        return bindOneQuery(list, bool()::mustNot);
    }

    public static List<Query> addQuery(List<Query> list, Query query) {
        if (null != query) {
            list.add(query);
        }
        return list;
    }

    public static Query bindQuery(List<Query> list, Function<List<Query>, BoolQuery.Builder> fn) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return (1 == list.size()) ? list.get(0) : bindOneQuery(list, fn);
    }

    public static Query bindOneQuery(List<Query> list, Function<List<Query>, BoolQuery.Builder> fn) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return Query.of(q -> q.bool(fn.apply(list).build()));
    }

    public static SortOptions sortBy(String field) {
        return SortOptions.of(s -> s.field(f -> f.field(field)));
    }

    public static SortOptions sortBy(String field, boolean isAsc) {
        if (isAsc) {
            return SortOptions.of(s -> s.field(f -> f.field(field).order(SortOrder.Asc)));
        } else {
            return SortOptions.of(s -> s.field(f -> f.field(field).order(SortOrder.Desc)));
        }
    }

    public static Query boolMustNot(List<Query> mustNotQueries) {
        if (CollectionUtils.isEmpty(mustNotQueries)) {
            return null;
        }

        BoolQuery.Builder builder = bool().must(mustNotQueries);
        return Query.of(q -> q.bool(builder.build()));
    }


    /**
     * @param field
     * @param order Asc/Desc
     * @return
     */
    public static SortOptions sortBy(String field, String order) {
        return SortOptions.of(s -> s.field(f -> f.field(field).order(SortOrder.valueOf(order))));
    }

    public static Query search(String field, String... value) {
        return Query.of(q -> q.queryString(v -> v
                .fields(Arrays.asList(value))
                .query(field)

                .analyzer("ik_smart")
        ));
    }

    public static Query regexp(String field, String value) {
        String regexp = ".*" + value + ".*";
        return Query.of(q -> q.regexp(v -> v.field(field).value(regexp)));

    }

    public static Query queryPlainWord(List<String> fields, String keyword) {
        return Query.of(q -> q
                .queryString(qu -> qu
                        .fields(fields)
                        .query(wholeWord(keyword))
                        .analyzer("ik_smart")
                )
        );
    }

    public static String wholeWord(String keyword) {
        String include = "\"";
        return StringUtils.isEmpty(keyword) ? keyword : include + keyword + include + wordGap(keyword);
    }

    public static String wordGap(String keyword) {
        //调整为精准匹配 2024-04-01
//        return StringUtils.isEmpty(keyword) ? "" : "~".concat(String.valueOf(keyword.length()));
        String[] params = new String[1]; // 创建一个大小为3的字符串数组
        params[0] = keyword;
        String[] strings = AnalyseDistanceUtil.multiAnalyzeDifWithEs(params);
        if(strings != null && strings.length >0){
            System.out.println("原数据:"+strings[0]);
            if(strings[0].length() > (keyword.length()+2)){
                String[] split = strings[0].split("\""+keyword+"\"");
                if(split != null && split.length > 1){
                    String str = split[1];
                    String[] split1 = str.split("~");
                    String s = split1[1];
                    // 判断字符串是否全部由数字组成
                    if (s.matches("\\d+")) {
                        // 字符串是纯数字，将其转换为整数并加一
                        int num = Integer.parseInt(s);
                        if(num > 1) {
                            num--;
                            return "\"" + keyword + "\"" + "~" + num;
                        }
                    }
                }
            }
            return strings[0];
        }
        return "";
    }
}
