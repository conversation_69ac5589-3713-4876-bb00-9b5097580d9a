package com.tarsocial.bigital.kol.service.exception;

/**
 * <AUTHOR>
 */
public class BusinessException extends RuntimeException {


    /**
     * 参数验证错误
     */
    public static final int VALIDATE_ERROR = 10001;




    private Integer statusCode;


    public BusinessException(String message) {
        super(message);
        setStatusCode(500);
    }

    public BusinessException(String message, int statusCode) {
        super(message);
        setStatusCode(statusCode);
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public int getStatusCode() {
        return statusCode;
    }
}
