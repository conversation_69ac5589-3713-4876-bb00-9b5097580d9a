package com.tarsocial.bigital.kol.service.advice;

import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.common.domain.response.ResponseCode;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.postgresql.util.PSQLException;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.xml.bind.ValidationException;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
@RestControllerAdvice(basePackages = "com.tarsocial.bigital.kol.service.controller")
public class ControllerAdvice {

    @ExceptionHandler(Exception.class)
    public BaseResponse<String> handle(Exception e) {
        if ("org.apache.catalina.connector.ClientAbortException".equals(e.getClass().getName())
                || "java.io.IOException: Broken pipe".equals(e.getClass().getName())) {
            log.error("found ClientAbortException, ignore it.");
            return null;
        }
        log.error("exception", e);
        return BaseResponse.error(e.getMessage());
    }


    @ExceptionHandler(BusinessException.class)
    public BaseResponse<String> handle(BusinessException e) {
        log.error("business exception", e);
        return new BaseResponse<>(e.getStatusCode(), e.getMessage());
    }

    @ExceptionHandler(ValidationException.class)
    public BaseResponse<String> handle(ValidationException e) {
        log.error("business exception", e);
        return new BaseResponse<>(ResponseCode.VALIDATE_ERROR.getCode(), ResponseCode.VALIDATE_ERROR.getMessage());
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public BaseResponse<String> handle(MissingServletRequestParameterException e) {
        log.error("business exception", e);
        return new BaseResponse<>(ResponseCode.PARAM_ERROR.getCode(), ResponseCode.PARAM_ERROR.getMessage());
    }

    @ExceptionHandler(PSQLException.class)
    public BaseResponse<String> handle(PSQLException e) {
        log.error("business exception", e);
        return new BaseResponse<>(ResponseCode.PARAM_ERROR.getCode(), ResponseCode.PARAM_ERROR.getMessage());
    }

}
