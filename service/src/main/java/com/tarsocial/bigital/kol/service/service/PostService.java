package com.tarsocial.bigital.kol.service.service;

import com.tarsocial.bigital.kol.common.domain.dto.PostDetailDto;
import com.tarsocial.bigital.kol.common.domain.entity.PepsicoDictEntity;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/3/14
 */
public interface PostService {

    /**
     * 导出数据
     * @param dictWords 条件
     * @param dyRule 抖音规则
     * @param xhsRule 小红书规则
     * @return 导出结果
     */
    List<PostDetailDto> originalPost(Set<PepsicoDictEntity> dictWords, Long dyRule, Long xhsRule);


    int originalPostByPlatform(List<String> platformList, String time, String time1);


    void exportData();


}
