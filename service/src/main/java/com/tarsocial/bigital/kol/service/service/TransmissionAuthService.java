package com.tarsocial.bigital.kol.service.service;

import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.OauthClients;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.IegTokenRequest;
import com.tarsocial.bigital.kol.common.domain.response.ieg.IegTokenResponse;
import com.tarsocial.bigital.kol.service.service.tencent.ieg.OauthClientsService;
import com.tarsocial.usercenter.api.ItemResponse;
import com.tarsocial.usercenter.model.web.AuthenticationRequestModel;
import com.tarsocial.usercenter.model.web.AuthenticationResponseModel;
import com.tarsocial.usercenter.web.service.UserCenterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@Service
@Slf4j
public class TransmissionAuthService {

    @Resource
    private UserCenterService userCenterService;

    @Resource
    private OauthClientsService oauthClientsService;

    public Object getToken(IegTokenRequest request, HttpServletResponse response) {
        // 获取ieg 用户表信息
        OauthClients user = oauthClientsService.findUser(request);
        try {
            // 调用用户中心登录接口
            AuthenticationRequestModel model = new AuthenticationRequestModel();
            model.setUsername(user.getUserId());
            model.setPassword("PHimLeUc22/SgfVNC0fidk+jG7EpHgdymQCoLC/ia1U4k30kSM/22dKYwl1pmSbneGQdiyAaJL/x8OCvvhoxJhNyOpShtT+cxFe+Jy8bZKf99lyBdnhdJ1ykfwJYERDmI7plapLMJeDlU2RTSf32t99iVmDN8A0IN84r5gvoyag=");
            ItemResponse<AuthenticationResponseModel> tokenModel = userCenterService.login(model, response);
            IegTokenResponse token = new IegTokenResponse();
            token.setAccessToken(tokenModel.getData().getAccessToken());
            token.setExpiresIn(60 * 60 * 2);
            return token;
        } catch (Exception e) {
            return null;
        }
    }


}
