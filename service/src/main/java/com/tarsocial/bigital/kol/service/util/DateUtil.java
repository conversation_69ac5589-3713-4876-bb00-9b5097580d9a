package com.tarsocial.bigital.kol.service.util;

import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @Description:
 * @Author: ty
 * @CreateDate: 2023/3/24
 */
public class DateUtil {

    private DateUtil() {
    }

    /**
     * 按周分割日期
     *
     * @param start yyyy-mm-dd
     * @param end   yyyy-mm-dd
     */
    public static List<HashMap<String, String>> splitDateByWeek(String start, String end) {

        LocalDate s = LocalDate.parse(start, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER));
        LocalDate e = LocalDate.parse(end, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER));

        ArrayList<HashMap<String, String>> result = new ArrayList<>();

        while (s.compareTo(e) <= 0) {
            LocalDate monday = s.with(DayOfWeek.MONDAY);
            LocalDate sunday = s.with(DayOfWeek.SUNDAY);
            HashMap<String, String> map = new HashMap<>();
            map.put("begin", monday.format(DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER)));
            map.put("end", sunday.format(DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER)));
            result.add(map);
            s = sunday.plusDays(1);
        }
        return result;
    }

    /**
     * 按天分割日期
     *
     * @param start yyyy-mm-dd
     * @param end   yyyy-mm-dd
     */
    public static List<String> splitDateByDay(String start, String end) {
        LocalDate s = LocalDate.parse(start, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER));
        LocalDate e = LocalDate.parse(end, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER));
        ArrayList<String> result = new ArrayList<>();
        while (s.compareTo(e) <= 0) {
            result.add(s.format(DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER)));
            s = s.plusDays(1);
        }
        return result;
    }

    /**
     * 按天分割日期
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<LocalDate> splitDateByDay(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }
        return dates;
    }

    /**
     * 按月分割
     *
     * @return
     */
    public static List<String> splitDateByMonth(String start, String end) {

        LocalDate s = LocalDate.parse(start, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER)).withDayOfMonth(1);
        LocalDate e = LocalDate.parse(end, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER));
        ArrayList<String> result = new ArrayList<>();
        while (s.compareTo(e) <= 0) {
            result.add(s.format(DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER)));
            s = s.plusMonths(1);
        }
        return result;
    }

    public static Map<String, String> getLastDays(int n) {
        Map<String, String> result = new HashMap<>(2);

        // 获取当前日期时间
        LocalDate currentDateTime = LocalDate.now().minusDays(1);

        // 获取近三十天的开始时间
        LocalDate startDate = currentDateTime.minusDays(n - 1);
        String startDateString = formatLocalDate(startDate);

        // 获取当前日期时间的结束时间
        String endDateString = formatLocalDate(currentDateTime);

        result.put("start", startDateString);
        result.put("end", endDateString);

        return result;
    }

    /**
     * 获取传入时间的0:0:0
     */
    public static Date getDayStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取近几天时间范围
     */
    public static Map<String, Date> getBetweenDay(Date now, int day) {
        HashMap<String, Date> dateMap = new HashMap<>();
        dateMap.put("start", getDayStart(DateUtils.addDays(now, day)));
        dateMap.put("end", getDayEnd(DateUtils.addDays(now, -1)));
        return dateMap;
    }

    /**
     * 获取当天的几点
     */
    public static Date getHour(Date date, int hour) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 字符串转日期
     */
    public static Date convertDate(String s, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        LocalDateTime dateTime = LocalDateTime.parse(s, formatter);
        long milleSecond = dateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        return new Date(milleSecond);
    }

    /**
     * 日期转字符串
     */
    public static String formatDate(Date date, String format) {
        if (Objects.isNull(date)) {
            return null;
        }
        DateTimeFormatter dateTimeFormat = DateTimeFormatter.ofPattern(format, Locale.CHINA);
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).format(dateTimeFormat);
    }

    /**
     * 获取传入时间的23:59:59
     */
    public static Date getDayEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    public static Map<String, String> calculateMoM(String startDateStr, String endDateStr, String periodType) {
        LocalDate startDate = LocalDate.parse(startDateStr, DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate endDate = LocalDate.parse(endDateStr, DateTimeFormatter.ISO_LOCAL_DATE);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        Map<String, String> result = new HashMap<>();
        Period period = Period.between(startDate, endDate);
        if ("day".equals(periodType)) {
            // 计算 day 范围，即开始时间和结束时间相差的时间范围
            result.put("start", formatLocalDate(startDate.minusDays(period.getDays() + 1)));
            result.put("end", formatLocalDate(startDate.minusDays(1)));
        } else if ("month".equals(periodType)) {
            // 计算 month 范围，即开始时间和结束时间相差的时间范围
            startDate = startDate.minusMonths(period.getMonths() + 1);
            endDate = endDate.minusMonths(period.getMonths()).withDayOfMonth(1).minusDays(1);
            ;
            result.put("start", startDate.format(formatter));
            result.put("end", endDate.format(formatter));
        }

        return result;
    }

    public static void main(String[] args) {
        String startDate = "2023-09-16";
        String endDate = "2023-10-15";
        String periodType = "day"; // 或 "month"

        Map<String, String> result = calculateMoM(startDate, endDate, periodType);
        if (result != null) {
            System.out.println("Last Start Date: " + result.get("start"));
            System.out.println("Last End Date: " + result.get("end"));
        } else {
            System.out.println("Error occurred during date calculation.");
        }
    }


    public static String formatLocalDateTime(Date dateTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(dateTime);
    }

    public static String formatLocalDateTime(LocalDateTime dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }


    public static String formatLocalDate(LocalDate dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return dateTime.format(formatter);
    }

    public static String formatLocalDate2(LocalDate dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return dateTime.format(formatter);
    }

    public static String extractDate(String dateString) {
        if (dateString == null) {
            return null;
        }
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");

        try {
            Date date = inputFormat.parse(dateString);
            return outputFormat.format(date);
        } catch (Exception e) {

            return null;
        }
    }

    public static String formatLocalDate(LocalDate dateTime, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return dateTime.format(formatter);
    }


    public static Integer getDaysInLastYear() {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 获取上一年的年份
        int lastYear = today.getYear() - 1;

        // 创建上一年的 Year 对象
        Year year = Year.of(lastYear);

        // 返回上一年的天数，365 或 366
        return year.length();
    }


    /**
     * 时间位移
     *
     * @param dateString
     * @param days
     * @return
     */
    public static String offsetDate(String dateString, long days) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER);
        LocalDate localDate = LocalDate.parse(dateString, formatter);
        return localDate.plusDays(days).format(formatter);
    }

    /**
     * 时间gap : day
     *
     * @param start
     * @param end
     * @return
     */
    public static long daysBetween(String start, String end) {
        LocalDate s = LocalDate.parse(start, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER));
        LocalDate e = LocalDate.parse(end, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER));
        return ChronoUnit.DAYS.between(s, e) + 1;
    }

    /**
     * 时间gap : day
     *
     * @param start start
     * @param end   end
     * @return long
     */
    public static long daysBetween(Date start, Date end) {
        return ChronoUnit.DAYS.between(convertLocalDate(start), convertLocalDate(end));
    }

    public static LocalDate convertLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDate();
    }

    /**
     * 时间相差
     *
     * @param start
     * @param end
     * @return
     */
    public static long daysDiff(String start, String end) {
        LocalDate s = LocalDate.parse(start, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER));
        LocalDate e = LocalDate.parse(end, DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER));
        return s.toEpochDay() - e.toEpochDay();
    }


    public static Date parseDateString(String dateString) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return dateFormat.parse(dateString);
        } catch (ParseException e) {
            System.out.println("Error parsing date: " + e.getMessage());
            return null;
        }
    }

    public static Date parseDateString1(String dateString) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        try {
            return dateFormat.parse(dateString);
        } catch (ParseException e) {
            System.out.println("Error parsing date: " + e.getMessage());
            return null;
        }
    }

    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    // 获取当天结束时间
    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    public static LocalDate parse(String date) {
        return LocalDate.parse(date);
    }
}
