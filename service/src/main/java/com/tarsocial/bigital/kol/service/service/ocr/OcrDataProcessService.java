package com.tarsocial.bigital.kol.service.service.ocr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.entity.Post;
import com.tarsocial.bigital.kol.common.domain.entity.ocr.OcrPost;
import com.tarsocial.bigital.kol.common.domain.enums.QuarkPlatform;
import com.tarsocial.bigital.kol.service.constants.Constants;
import com.tarsocial.bigital.kol.service.service.DataCenterProcessService;
import com.tarsocial.bigital.kol.service.service.guangnian.GameDataProcessService;
import com.tarsocial.bigital.kol.service.util.EsDataUtil;
import com.tarsocial.enums.OperationEnum;
import com.tarsocial.enums.OrderTypeEnum;
import com.tarsocial.request.CommonKeyAndValue;
import com.tarsocial.request.DocSourceV1Request;
import com.tarsocial.request.FilterRequest;
import com.tarsocial.response.DocSourceResponse;
import com.tarsocial.vo.FieldOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;


/**
 * <AUTHOR>
 * @ClassName GameDataProcessService
 * @date 2024年04月23日
 */
@Service
@Slf4j
public class OcrDataProcessService {

    @Resource
    private DataCenterProcessService dataCenterProcessService;

    @Resource
    private GameDataProcessService gameDataProcessService;


    public List<OcrPost> selectPostList(List<String> ids, String platform) {
        DocSourceV1Request docSourceV1Request = doDealRequest(ids, platform, 95551L);
        DocSourceResponse response = dataCenterProcessService.sourceV1(docSourceV1Request);
        List<OcrPost> list = Lists.newArrayList();
        response.getRows().stream().filter(x -> Objects.nonNull(x) && !CollectionUtils.isEmpty(x.getSource())).forEach(x -> list.add(processPost(x.getSource())));
        return list;
    }

    private static FilterRequest getSimpleFilterPostIds(List<String> ids) {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setAllowNull(null);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(268L);
        commonKeyAndValue.setValue(ids);
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.equals);
        return contentFilter;
    }

    public DocSourceV1Request doDealRequest(Date start, Date end, Long mainId, String topicName) {

        DocSourceV1Request request = new DocSourceV1Request();
        request.setCount(10000);

        request.setMainPartyId(mainId);
        // 排序字段
        List<FieldOrderVo> orderBy = Lists.newArrayList();
        FieldOrderVo fieldOrderVo = new FieldOrderVo();
        fieldOrderVo.setName("date_publishedAt");
        fieldOrderVo.setOrderType(OrderTypeEnum.DESC);

        orderBy.add(fieldOrderVo);
        request.setOrderBy(orderBy);
        // 需要展示字段
        request.setFlatSource(false);
        // 开始时间和结束时间
        request.setStartDate(start);
        request.setEndDate(end);

        request.setArrayFlatSource(true);

        request.setPlatformIds(Collections.singletonList(QuarkPlatform.DOUYIN.getCode()));
        //过滤条件
        List<FilterRequest> filters = Lists.newArrayList();
        filters.add(gameDataProcessService.getSimpleFilterComments());
        // filters.add(getSimpleFilterTopicName(topicName));
        filters.add(getFilterContentType(Collections.singletonList("视频")));
        filters.add(getFilterUID());
        filters.add(getFilterUserPlatform());
        filters.add(getFilterUserUrl());

        filters.add(getSimpleFilterContent(topicName));
        request.setFilter(filters);
        request.setKeepAlive(5);
        request.setIndexType("post");
        return request;
    }

    private FilterRequest getFilterUserUrl() {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setAllowNull(null);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(236L);
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.exist);
        return contentFilter;
    }

    private FilterRequest getFilterUserPlatform() {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setAllowNull(null);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(223L);
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.exist);
        return contentFilter;
    }

    private FilterRequest getFilterUID() {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setAllowNull(null);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(220L);
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.exist);
        return contentFilter;
    }

    public FilterRequest getFilterContentType(List<String> contentType) {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setAllowNull(null);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(313L);
        commonKeyAndValue.setValue(contentType);
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.equals);
        return contentFilter;
    }

    private FilterRequest getSimpleFilterContent(String topicName) {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setAllowNull(null);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(277L);
        commonKeyAndValue.setValue(Collections.singletonList(topicName));
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.contain);
        return contentFilter;
    }


    private FilterRequest getSimpleFilterTopicName(String topicName) {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setAllowNull(null);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(310L);
        commonKeyAndValue.setValue(Collections.singletonList(topicName));
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.equals);
        return contentFilter;
    }

    public DocSourceV1Request doDealRequest(List<String> ids, String platform, Long mainId) {
        DocSourceV1Request request = new DocSourceV1Request();
        request.setCount(10000);
        request.setMainPartyId(mainId);

        // 需要展示字段
        request.setFlatSource(false);
        // 开始时间和结束时间
        request.setStartDate(DateUtils.addDays(new Date(), -365));
        request.setEndDate(new Date());

        request.setArrayFlatSource(true);

        if (StringUtils.hasLength(platform)) {
            request.setPlatformIds(Collections.singletonList(Constants.QuarkPlatformType.getPlatformCodeByValue(platform)));
        } else {
            request.setPlatformIds(Constants.QuarkPlatformType.getAllPlatformCode());
        }
        //过滤条件
        List<FilterRequest> filters = Lists.newArrayList();
        filters.add(getSimpleFilterPostIds(ids));
        filters.add(gameDataProcessService.getSimpleFilterComments());
        request.setFilter(filters);
        request.setKeepAlive(1);
        request.setIndexType("post");
        return request;
    }

    private OcrPost processPost(Map<String, Object> source) {
        OcrPost ocrPost = new OcrPost();
        Post post = JSON.parseObject(JSON.toJSONString(source), Post.class);

        log.info("post: " + JSON.toJSONString(post));

        ocrPost.setKwId(post.getId())
                .setKwPlatform(post.getPlatform())
                .setKwUrl(post.getUrl())
                .setKwCoverUrl(post.getCoverUrl())
                .setTxContent(post.getContent())
                .setKwContentType(post.getContentType())
                .setKwPicUrl(post.getPicUrl());

        if (Constants.QuarkPlatformType.WEIBO.getValue().equals(post.getPlatform())) {
            String kwVideoUrl = EsDataUtil.existData(source.get("kw_videoUrl"));
            ocrPost.setKwVideoUrl(Objects.nonNull(kwVideoUrl) ? Collections.singletonList(kwVideoUrl) : null);
        } else {
            ocrPost.setKwVideoUrl(Objects.nonNull(source.get("kw_videoUrl")) ? JSONArray.parseArray(JSON.toJSONString(source.get("kw_videoUrl")), String.class) : null);
        }

        List<Post.ObjectOcrRowData> ocrRowData = post.getObjectOcrRowData();
        if (!CollectionUtils.isEmpty(ocrRowData)) {
            List<OcrPost.ObjectOcrRowData> list = Lists.newArrayList();
            ocrRowData.forEach(x -> {
                OcrPost.ObjectOcrRowData data = new OcrPost.ObjectOcrRowData();
                data.setTxText(x.getText()).setLongTime(x.getTime());
                list.add(data);
            });
            ocrPost.setObjectOcrRowdata(list);
        }

        List<Post.ObjectAsrRowData> asrRowData = post.getObjectAsrRowData();
        if (!CollectionUtils.isEmpty(asrRowData)) {
            List<OcrPost.ObjectAsrRowData> list = Lists.newArrayList();
            asrRowData.forEach(x -> {
                OcrPost.ObjectAsrRowData data = new OcrPost.ObjectAsrRowData();
                data.setTxText(x.getText()).setLongStartTime(x.getStartTime()).setLongEndTime(x.getEndTime());
                list.add(data);
            });
            ocrPost.setObjectAsrRowdata(list);
        }
        return ocrPost;
    }
}
