package com.tarsocial.bigital.kol.service.service;

import com.tarsocial.bigital.kol.common.domain.request.amp.AMPFilterRequest;
import com.tarsocial.bigital.kol.common.domain.response.amp.AMPKolResponse;
import com.tarsocial.bigital.kol.common.domain.response.amp.AMPPostResponse;

import java.util.List;

public interface AmpService {

    List<AMPPostResponse> postList(AMPFilterRequest request);

    List<AMPKolResponse> kolList(AMPFilterRequest request);

}
