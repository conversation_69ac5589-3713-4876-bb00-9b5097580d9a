package com.tarsocial.bigital.kol.service.controller.tencent;

import cn.hutool.core.bean.BeanUtil;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.Task;
import com.tarsocial.bigital.kol.common.domain.request.tencent.TaskCreateRequest;
import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.service.job.tencent.jv.TencentTask;
import com.tarsocial.bigital.kol.service.service.tencent.DataProcessService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/backup")
public class BackupController {


    @Resource
    private DataProcessService dataProcessService;

    @Resource
    private TencentTask tencentTask;

    @PostMapping("/task/run")
    public BaseResponse<String> taskCreate(@RequestBody TaskCreateRequest request) {
        Task task = BeanUtil.toBean(request, Task.class);
        task.setEndTime(DateUtil.getDayEnd(task.getEndTime()));
        dataProcessService.pushTencentPost(task, task.getStartTime(), task.getEndTime());
        return new BaseResponse<>("ok");
    }

    @PostMapping("/push/post")
    public BaseResponse<String> pushTencentPost() {
        tencentTask.pushTencentPost();
        return new BaseResponse<>("ok");
    }
}
