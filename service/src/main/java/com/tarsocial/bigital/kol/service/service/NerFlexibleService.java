package com.tarsocial.bigital.kol.service.service;

import com.tarsocial.bigital.kol.common.domain.entity.NerFlexibleEntity;
import com.tarsocial.bigital.kol.common.domain.request.NerFlexibleCalculateRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface NerFlexibleService {

    void calculate(MultipartFile file, NerFlexibleCalculateRequest req);

    List<NerFlexibleEntity> list();

    Map<String,List<String>> dict();

}
