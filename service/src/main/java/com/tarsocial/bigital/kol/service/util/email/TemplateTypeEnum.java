package com.tarsocial.bigital.kol.service.util.email;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum TemplateTypeEnum {
    /**
     * GAME_POST
     */
    GAME_POST(1, "gamePost.html"),
    ;

    private final Integer code;

    private final String type;

    TemplateTypeEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public static String getTypeByCode(Integer code) {
        for (TemplateTypeEnum templateTypeEnum : TemplateTypeEnum.values()) {
            if (templateTypeEnum.getCode().equals(code)) {
                return templateTypeEnum.getType();
            }
        }
        return "sendCode.html";
    }

}
