package com.tarsocial.bigital.kol.service.config.thread;

import org.slf4j.MDC;

import java.util.Map;
import java.util.Objects;

/**
 * 短期、一次性任务执行，支持MDC透传
 * <AUTHOR>
 */
public class MdcRunnable implements Runnable {

    private final Runnable runnable;

    private transient final Map<String, String> contextMap = MDC.getCopyOfContextMap();

    public MdcRunnable(Runnable runnable) {
        this.runnable = runnable;
    }

    @Override
    public void run() {
        if (Objects.nonNull(contextMap)) {
            MDC.setContextMap(contextMap);
        }
        try {
            runnable.run();
        } finally {
            MDC.clear();
        }
    }

}
