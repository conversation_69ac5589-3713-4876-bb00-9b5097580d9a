package com.tarsocial.bigital.kol.service.service.tencent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.TencentPostBean;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.TencentPostLog;
import com.tarsocial.bigital.kol.service.mapper.TencentPostLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @ClassName TaskService
 * @date 2024年03月26日
 */
@Service
@Slf4j
public class TencentPostLogService extends ServiceImpl<TencentPostLogMapper, TencentPostLog> {

    @Resource
    private TencentPostLogMapper tencentPostLogMapper;

    public List<TencentPostLog> findPostByTopic(List<String> ids, String topicKey) {
        LambdaQueryWrapper<TencentPostLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TencentPostLog::getTopicKey, topicKey).in(TencentPostLog::getPostId, ids);
        return tencentPostLogMapper.selectList(wrapper);
    }

    public void savePostLog(List<TencentPostBean> postBeanList, String topicKey) {
        if (CollectionUtils.isEmpty(postBeanList)) {
            return;
        }
        List<TencentPostLog> postList = Lists.newArrayList();
        postBeanList.forEach(x -> {
            TencentPostLog postLog = new TencentPostLog();
            postLog.setTopicKey(topicKey);
            postLog.setPostId(x.getContentId());
            postLog.setPlatform(x.getPlatform());
            postLog.setPostPublished(x.getPublishTime());
            postLog.setPostUpdated(x.getUpdateTime());

            postList.add(postLog);
        });

        tencentPostLogMapper.batchSave(postList);
    }
}
