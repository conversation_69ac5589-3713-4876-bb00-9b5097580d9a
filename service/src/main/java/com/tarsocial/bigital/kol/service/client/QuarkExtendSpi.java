package com.tarsocial.bigital.kol.service.client;

import com.tarsocial.config.TokenConfiguration;
import com.tarsocial.dag.framework.feign.config.DagFeignClientConfiguration;
import com.tarsocial.spi.ExtendSpi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "carslan-insight-data-center" ,url="${quark.serverUrl}", configuration = {TokenConfiguration.class,
        DagFeignClientConfiguration.class})
public interface QuarkExtendSpi extends ExtendSpi {
}
