package com.tarsocial.bigital.kol.service.util;

import org.apache.commons.codec.digest.DigestUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> GoodLuck
 * @date 2023/10/19$ 15:51
 */

public class DataUtil {
    private static Pattern LINE_PATTERN = Pattern.compile("_(\\w)");
    private static String RED_URL = "https://www.xiaohongshu.com/user/profile/";
    private static String WEIBO_URL = "https://www.weibo.com/u/";
    private static String BILIBILI_URL = "https://space.bilibili.com/";

    /**
     * 保留几位小数
     *
     * @param value 值
     * @param scale 小数位
     * @return 结果
     */
    public static double setScale(double value, int scale) {
        BigDecimal decimal = BigDecimal.valueOf(value);
        BigDecimal roundedValue = decimal.setScale(scale, RoundingMode.HALF_UP);
        return roundedValue.doubleValue();
    }

    public static String setScale(Long param1, Long param2, int scale) {
        final Double v = divInt(param1, param2);
        if (v == null) {
            return "0";
        }
        final BigDecimal bigDecimal = BigDecimal.valueOf(v).setScale(scale, RoundingMode.HALF_UP);

        return String.valueOf(bigDecimal.doubleValue());

//        if (param1 == 0) {
//            return "0.0";
//        }
//        BigDecimal decimal = BigDecimal.valueOf(param1 / param2);
//        BigDecimal roundedValue = decimal.setScale(scale, RoundingMode.HALF_UP);
//        return String.valueOf(roundedValue.doubleValue());
    }

    /**
     * 保留几位小数
     *
     * @param value 值
     * @param scale 小数位
     * @return 结果
     */
    public static double setScale(Long value, int scale) {
        BigDecimal decimal = BigDecimal.valueOf(value);
        BigDecimal roundedValue = decimal.setScale(scale, RoundingMode.HALF_UP);
        return roundedValue.doubleValue();
    }

    public static Double divInt(Long a, Long b) {
        if (b == null || b == 0) {
            return null;
        }
        if (a == null) {
            return null;
        }
        BigDecimal b1 = new BigDecimal(a);
        BigDecimal b2 = new BigDecimal(b);
        return b1.divide(b2, 6, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 四舍五入保留几位小数
     *
     * @param d     原始数据
     * @param scale 几位小数
     * @return
     */
    public static Double roundHalfUp(Double d, int scale) {
        if (d == null) {
            return null;
        }
        BigDecimal b = new BigDecimal(d.toString());
        return b.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * @param d     原始数据
     * @param type  BigDecimal.ROUND_HALF_UP 类型
     * @param scale 几位小数
     * @return
     */
    public static Double round(Double d, int type, int scale) {
        BigDecimal b = new BigDecimal(d.toString());
        return b.setScale(scale, type).doubleValue();
    }

    /**
     * 下划线转驼峰
     */
    public static String lineToHump(String str) {
        str = str.toLowerCase();
        Matcher matcher = LINE_PATTERN.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();

    }


    public static String formatDouble(Double value) {
        if (value != null) {
            // 将double格式化为保留两位小数
            return String.format("%.2f", value);
        }
        return null;
    }

    public static <S> ArrayList<S> dataSupplement(List<S> dataList, Class<S> s, Map<Field, List<String>> dimensionMap, Map<Field, Object> map) {
        ArrayList<S> res = new ArrayList<>();
        dimensionMap.forEach((field0, dimensionValues) -> {
            dimensionValues.forEach(mat -> {
                List<S> collect = dataList.stream().filter(obj -> {
                    Object value0 = null;
                    try {
                        field0.setAccessible(true);
                        value0 = field0.get(obj);
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                    return value0 != null && value0.equals(mat);
                }).collect(Collectors.toList());

                if (collect.isEmpty()) {
                    try {
                        S s1 = s.newInstance();
                        Field[] fields = s.getDeclaredFields();
                        for (Field field : fields) {
                            field.setAccessible(true);
                            if (field.equals(field0)) {
                                field.set(s1, mat);
                            } else if (map.get(field) != null) {
                                field.set(s1, map.get(field));
                            } else {
                                if ("java.lang.Integer".equals(field.getGenericType().getTypeName())) {
                                    field.set(s1, 0);
                                } else if ("java.lang.Double".equals(field.getGenericType().getTypeName())) {
                                    field.set(s1, 0.0d);
                                } else {
                                    field.set(s1, null);
                                }
                            }
                        }
                        res.add(s1);
                    } catch (InstantiationException | IllegalAccessException e) {
                        e.printStackTrace();
                    }
                } else {
                    res.add(collect.get(0));
                }
            });
        });
        return res;
    }

    /**
     * 统计list集合中各元素出现的次数
     *
     * @param list 待统计list
     * @return k:数据类型 v：该元素出现的次数
     */
    public static <T> Map<T, Integer> countOfTimes(List<T> list) {
        HashMap<T, Integer> map = new HashMap<>();
        list.forEach(s -> map.put(s, Objects.isNull(map.get(s)) ? 1 : map.get(s) + 1));
        return map;
    }

    /**
     * 将一组数据平均分成n组
     *
     * @param source 要分组的数据源
     * @param n      平均分成n组
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> averageAssign(List<T> source, int n) {
        List<List<T>> result = new ArrayList<>();
        //先计算出余数
        int remainder = source.size() % n;
        //然后是商
        int number = source.size() / n;
        //偏移量
        int offset = 0;
        for (int i = 0; i < n; i++) {
            List<T> value;
            if (remainder > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remainder--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }

    /**
     * @description: 取中位数
     * <AUTHOR>
     * @date ${DATE} ${TIME}
     */
    public static <T extends Comparable<T>> T findMedian(List<T> list) {
        if (list.isEmpty()) {
            return null;
        }

        // 首先对列表进行排序
        Collections.sort(list);

        int size = list.size();
        if (size % 2 == 1) {
            // 奇数个元素，中位数是中间的元素
            return list.get(size / 2);
        } else {
            // 偶数个元素，中位数是中间两个元素的平均值
            T mid1 = list.get(size / 2 - 1);
            T mid2 = list.get(size / 2);
            return calculateAverage(mid1, mid2);
        }
    }


    public static <T extends Comparable<? super T>> double calculateMedian(List<T> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("List cannot be null or empty");
        }

        Collections.sort(list);

        int size = list.size();
        int middle = size / 2;

        if (size % 2 == 0) {
            // 偶数个元素，返回中间两个元素的平均值
            T value1 = list.get(middle - 1);
            T value2 = list.get(middle);
            return (toDouble(value1) + toDouble(value2)) / 2.0;
        } else {
            // 奇数个元素，返回中间元素
            return toDouble(list.get(middle));
        }
    }

    private static <T> double toDouble(T value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else {
            throw new IllegalArgumentException("Unsupported type for conversion to double");
        }
    }


    public static double calculateAverage(List<Long> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("List is null or empty.");
        }

        long sum = 0;
        for (Long num : list) {
            sum += num;
        }

        return (double) sum / list.size();
    }


    private static <T extends Comparable<T>> T calculateAverage(T a, T b) {
        // 计算两个元素的平均值
        if (a instanceof Integer) {
            return (T) Integer.valueOf(((Integer) a + (Integer) b) / 2);
        } else if (a instanceof Double) {
            return (T) Double.valueOf(((Double) a + (Double) b) / 2);
        } else if (a instanceof Long) {
            return (T) Long.valueOf(((Long) a + (Long) b) / 2);
        }
        // 这里可以根据需要添加其他数据类型的支持
        return null;
    }

    /**
     * @description: 将Map中的值乘以指定的乘数
     * <AUTHOR>
     */
    public static Map<String, String> multiplyMapValuesWithDecimalPlaces(Map<String, String> inputMap, double multiplier, int decimalPlaces) {
        if (inputMap == null || multiplier == 0.0) {
            return inputMap; // 如果输入为空或乘数为0，则返回原始Map
        }

        Map<String, String> result = new HashMap<>();

        for (Map.Entry<String, String> entry : inputMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // 尝试将值解析为双精度浮点数
            try {
                double doubleValue = Double.parseDouble(value);
                // 将值乘以乘数，并以字符串形式存储
                double multipliedValue = doubleValue * multiplier;
                String formattedValue = String.format("%." + decimalPlaces + "f", multipliedValue);
                result.put(key, formattedValue);
            } catch (NumberFormatException e) {
                // 如果无法解析为双精度浮点数，则保留原始值
                result.put(key, value);
            }
        }

        return result;
    }


    /**
     * @param inputMap 输入Map
     * @return 百分比Map
     * @description: 将Map中的值转换为百分比
     */
    public static Map<String, String> convertToPercentage(Map<String, String> inputMap) {
        Map<String, String> result = new HashMap<>();

        if (inputMap == null || inputMap.isEmpty()) {
            return result;
        }

        // 计算总和
        double total = 0.0;
        for (String value : inputMap.values()) {
            try {
                double numericValue = Double.parseDouble(value);
                total += numericValue;
            } catch (NumberFormatException e) {
                // 忽略无法解析为数字的值
            }
        }

        if (total == 0.0) {
            // 避免除以0的情况
            return result;
        }

        // 使用 DecimalFormat 来格式化占比
        DecimalFormat decimalFormat = new DecimalFormat("0.0000");

        // 计算占比并转换为字符串
        for (Map.Entry<String, String> entry : inputMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            try {
                double numericValue = Double.parseDouble(value);
                double percentage = (numericValue / total);
                String percentageStr = decimalFormat.format(percentage);
                result.put(key, percentageStr);
            } catch (NumberFormatException e) {
                // 如果无法解析为数字，则忽略
            }
        }

        return result;
    }

    public static <T> boolean allNull(List<T> list) {
        if (list == null) {
            return true;
        }

        for (T element : list) {
            if (element != null) {
                return false;
            }
        }

        return true;
    }


    public static String concatenateQuantumId(String platform, String id) {
        return platform + "_" + id;
    }

    public static int getCityOrder(String city) {
        // 根据城市名称返回相应的顺序，可以根据实际需求定义
        switch (city) {
            case "一":
                return 1;
            case "二":
                return 2;
            case "三":
                return 3;
            case "四":
                return 4;
            case "五":
                return 5;
            case "六":
                return 6;
            case "七":
                return 7;
            case "八":
                return 8;
            case "九":
                return 9;
            default:
                return 0; // 默认情况
        }
    }

    /**
     * 判断字符串是否是中文
     *
     * @param str 字符串
     * @return boolean
     */
    public static boolean containsChineseCharacter(String str) {
        if (str == null) {
            return false;
        }
        for (char ch : str.toCharArray()) {
            if (ch >= '\u4e00' && ch <= '\u9fa5') {
                return true;
            }
        }
        return false;
    }


    public static List<String> processKolUrls(List<String> kolUrls) {
        List<String> processedUrls = new ArrayList<>();

        for (String url : kolUrls) {
            if (url.contains("www.")) {
                // 如果包含www.，则移除它
                processedUrls.add(url.replace("www.", ""));
            } else {
                // 如果不包含www.，则在//之后加上www.
                int index = url.indexOf("//");
                if (index != -1) {
                    processedUrls.add(url.substring(0, index + 2) + "www." + url.substring(index + 2));
                } else {
                    // 如果没有//，则直接在开头加上www.
                    processedUrls.add("www." + url);
                }
            }
        }

        return processedUrls;
    }

    public static String processKolUrl(String kolUrl) {
        if (kolUrl.contains("www.")) {
            // 如果包含www.，则移除它
            return kolUrl.replace("www.", "");
        } else {
            // 如果不包含www.，则在//之后加上www.
            int index = kolUrl.indexOf("//");
            if (index != -1) {
                return kolUrl.substring(0, index + 2) + "www." + kolUrl.substring(index + 2);
            } else {
                // 如果没有//，则直接在开头加上www.
                return "www." + kolUrl;
            }
        }
    }

    /**
     * @param urlList URL列表
     * @return 最后一个部分列表
     * @description: 提取URL中的最后一个部分
     */
    public static List<String> extractLastSegment(List<String> urlList) {
        List<String> result = new ArrayList<>();

        for (String url : urlList) {
            // 使用split("/")将URL分割成多个部分，并取最后一个部分
            String[] segments = url.split("/");
            String lastSegment = segments[segments.length - 1];

            // 将提取的最后一个部分添加到结果列表中
            result.add(lastSegment);
        }

        return result;
    }


    public static String generateUrl(String platformName, String id) {
        switch (platformName) {
            case "douyin":
                return null;
            case "xiaohongshu":
                return RED_URL + id;
            case "weibo":
                return WEIBO_URL + id;
            case "bilibili":
                return BILIBILI_URL + id;
            case "weixin":
                return null;

            default:
                return null; // 默认情况
        }
    }

    public static String idMd5(String platformName, String id) {
        if (platformName.equals("douyin")) {
            return DigestUtils.md5Hex(id) + "|" + DigestUtils.md5Hex(id + "fqk2ioj4lz78d3gywcmtexsnvu6b5ap1");
        }
        if (platformName.equals("xiaohongshu")) {
            return DigestUtils.md5Hex(id) + "|" + DigestUtils.md5Hex(id + "fqk2ioj4lz78d3gywcmtexsnvu6b5ap1");
        }
        return null;
    }


    public static String getString(Map map, String key) {
        Object value = map.get(key);
        return value == null ? null : value.toString().replaceAll("\u0000", "").replaceAll("\\u0000", "");
    }


    public static Long getLong(Map map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }

}
