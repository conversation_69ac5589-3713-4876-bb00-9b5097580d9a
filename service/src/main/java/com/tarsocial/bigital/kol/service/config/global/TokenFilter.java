package com.tarsocial.bigital.kol.service.config.global;

import com.alibaba.fastjson.JSON;
import com.tarsocial.bigital.kol.service.config.sign.ReadRequestWrapper;
import com.tarsocial.bigital.kol.service.config.sign.SignUtils;
import com.tarsocial.bigital.kol.service.config.sign.WriterResponseWrapper;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TokenFilter extends OncePerRequestFilter {

    private static final String secret = "hellosummerbootstartersecurityhellosummerbootstartersecurityhellosummerbootstartersecurity";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        log.info("contentType -> {}, uri->{} ", request.getContentType(), request.getRequestURI());
        if (request.getRequestURI().contains("/ner/flexible/calculate")) {
            filterChain.doFilter(request, response);
            return;
        }

        String method = request.getMethod();
        request.setCharacterEncoding("UTF-8");
        ReadRequestWrapper wrapper = new ReadRequestWrapper(request);
        WriterResponseWrapper writer = new WriterResponseWrapper(response);

        try {
            String token = wrapper.getHeader("Access-Token");
            putAuthorization(token, wrapper);

            String body = SignUtils.readBody(wrapper);
            log.info("json --- > {}", JSON.parseObject(body));
            if (!StringUtils.hasLength(body) || !"POST".equals(method)) {
                filterChain.doFilter(wrapper, writer);
                return;
            }
            if (token == null || token.length() == 0) {
                token = JSON.parseObject(body).getString("token");
                putAuthorization(token, wrapper);
            }

            filterChain.doFilter(wrapper, writer);


        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage());
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setContentType("application/json;charset=UTF-8");
            response.getOutputStream().write(JSON.toJSONString("系统异常，请稍后重试").getBytes());
            // 获取响应内容
            byte[] content = writer.getOutputStream().toString().getBytes();
            // 写回响应
            response.getOutputStream().write(content);
        }
    }

    private void putAuthorization(String token, ReadRequestWrapper wrapper) {
        if (token != null && token.length() > 0) {
            try {
                Claims claims = Jwts.parser().setSigningKey(secret).parseClaimsJws(token).getBody();
                wrapper.addHeader("user-name", claims.getSubject());
                if (claims.get("tenantId") != null) {
                    wrapper.addHeader("tenant-id", claims.get("tenantId").toString());
                }
                if (claims.get("userId") != null) {
                    wrapper.addHeader("user-id", claims.get("userId").toString());
                }
                wrapper.addHeader("Authorization", "Bearer " + token);
            } catch (Exception e) {
                log.error("Token解析错误: {}", e.getMessage());
                throw new BusinessException("Token验证失败", 401);
            }
        }
    }
}
