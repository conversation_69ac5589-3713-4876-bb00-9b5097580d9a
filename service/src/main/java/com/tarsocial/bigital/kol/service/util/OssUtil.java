package com.tarsocial.bigital.kol.service.util;


import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectResult;
import com.tarsocial.bigital.kol.service.config.AbiDevOssProperties;
import com.tarsocial.bigital.kol.service.config.AbiProdOssProperties;
import com.tarsocial.bigital.kol.service.config.PepsicoDevOssProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;

/**
 * 云对象存储 工具类
 *
 * <AUTHOR>
 * @since 2024/3/14
 */

@Slf4j
@Component
public class OssUtil {

    @Resource
    private AbiDevOssProperties properties;


    /**
     * 获取阿里云OSS客户端对象
     */
    public OSS getOosClient() {
        return new OSSClientBuilder().build(properties.getEndpoint(), properties.getAccessKeyId(), properties.getAccessKeySecret());
    }


    public String uploadAbiDevFile(File file, String date) {
        log.info("start oss :{}", LocalDateTime.now());
        OSS oosClient = getOosClient();
        try {
            // 拼接上传路径
            String filename = properties.getBucketName() + "/table/post/" + date + "/" + file.getName();
            // 上传
            final PutObjectResult putObjectResult = oosClient.putObject(properties.getBucket(), filename, file);
            log.info("response etag:{}", putObjectResult.getETag());
            // 返回上传的文件的访问地址
            return "https://" + properties.getBucketName() + "." + properties.getEndpoint() + "/" + filename;

        } catch (Exception e) {
            log.info("文件上传失败: {}", e.getMessage());
            return null;
        } finally {
            // 关闭 oss
            if (oosClient != null) {
                oosClient.shutdown();
            }
        }


    }

    public String uploadPepsicoDevFile(File file, String ossFileName,PepsicoDevOssProperties pepsicoDevOssProperties) {

        OSS oosClient = new OSSClientBuilder().build(pepsicoDevOssProperties.getEndpoint(), pepsicoDevOssProperties.getAccessKeyId(), pepsicoDevOssProperties.getAccessKeySecret());

        log.info("start oss :{}", LocalDateTime.now());
        try {
            // 拼接上传路径
            String filename = pepsicoDevOssProperties.getBucketName() + "/" + ossFileName;
            // 上传
            final PutObjectResult putObjectResult = oosClient.putObject(pepsicoDevOssProperties.getBucket(), filename, file);
            log.info("response etag:{}", putObjectResult.getETag());
            // 返回上传的文件的访问地址
            return "https://" + pepsicoDevOssProperties.getBucketName() + "." + pepsicoDevOssProperties.getEndpoint() + "/" + filename;

        } catch (Exception e) {
            log.info("文件上传失败: {}", e.getMessage());
            return null;
        } finally {
            // 关闭 oss
            if (oosClient != null) {
                oosClient.shutdown();
            }
        }


    }

    public String uploadAbiProdFile(File file, String date, AbiProdOssProperties abiProdOssProperties) {

        OSS oosClient = new OSSClientBuilder().build(abiProdOssProperties.getEndpoint(), abiProdOssProperties.getAccessKeyId(), abiProdOssProperties.getAccessKeySecret());
        try {
            // 拼接上传路径
            String filename = abiProdOssProperties.getBucketName() + "/table/post/" + date + "/" + file.getName();
            // 上传
            final PutObjectResult putObjectResult = oosClient.putObject(abiProdOssProperties.getBucket(), filename, file);
            log.info("response etag:{}", putObjectResult.getETag());
            // 返回上传的文件的访问地址
            return "https://" + abiProdOssProperties.getBucketName() + "." + abiProdOssProperties.getEndpoint() + "/" + filename;
        } catch (Exception e) {
            log.info("文件上传失败: {}", e.getMessage());
            return null;
        } finally {
            // 关闭 oss
            if (oosClient != null) {
                oosClient.shutdown();
            }
        }
    }


}
