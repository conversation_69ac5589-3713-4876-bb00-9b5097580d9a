package com.tarsocial.bigital.kol.service.haierController.hair;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tarsocial.bigital.kol.common.domain.request.hair.HairExportKolResponse;
import com.tarsocial.bigital.kol.common.domain.request.hair.GetPlatformPriceTaskDataRequest;
import com.tarsocial.bigital.kol.common.domain.request.hair.GetPlatformPriceTaskDataResponse;
import com.tarsocial.bigital.kol.common.domain.request.hair.HaiErSubmitPriceResponse;
import com.tarsocial.bigital.kol.common.domain.request.hair.HairExportKolResponse;
import com.tarsocial.bigital.kol.common.domain.request.hair.HairMonitorBaseMetric;
import com.tarsocial.bigital.kol.common.domain.request.hair.HairMonitorDataRequest;
import com.tarsocial.bigital.kol.common.domain.request.hair.HairMonitorKolTaskRequest;
import com.tarsocial.bigital.kol.common.domain.request.hair.HairMonitorResponseV2;
import com.tarsocial.bigital.kol.common.domain.request.hair.HairMonitorTaskRequest;
import com.tarsocial.bigital.kol.common.domain.request.hair.HairMonitorTaskResponse;
import com.tarsocial.bigital.kol.common.domain.request.hair.HairMonitorTrendMetric;
import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.common.domain.response.HairBaseResponse;
import com.tarsocial.bigital.kol.common.domain.response.HairBaseResponse;
import com.tarsocial.bigital.kol.common.domain.response.HairBaseResponseV1;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import com.tarsocial.bigital.kol.service.util.JsonUtil;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title IHairController
 * @date 2024/12/27 15:07
 * @description 「IHairController」
 */
@RestController
@RequestMapping("/haier")
@Slf4j
public class HaiErController {

    // private final static String host = "https://api-kol.tarsocial.com/";
    private final static String host = "http://localhost:16777/";
    private final static String submit = "hair/export/post";
    private final static String getTaskResultList = "/hair/task/detail";
    private final static String kolDetail = "/hair/kol/detail";
    private final static String kolSubmit = "hair/kol/submit";
    private final static String kolPrice = "hair/query/kol/price";
    private final static String SHIPINHAO = "shipinhao";

    /**
     * 后侧提交任务
     */
    @PostMapping("/submitPostMonitorTask")
    public HairBaseResponseV1<Long> postList(@RequestBody @Validated HairMonitorTaskRequest request) {
        String platform = request.getPlatform();
        String postUrl = request.getPostUrl();
        if (StrUtil.isEmpty(postUrl)) {
            throw new BusinessException("链接不能为空");
        }
        if (StrUtil.isEmpty(platform)) {
            throw new BusinessException("平台不能为空");
        }
        //除了视频号以外要校验链接
        if (!platform.equals(SHIPINHAO) && !postUrl.contains(platform)) {
            throw new BusinessException("平台与链接不一致!");
        }
        Map<Object, Object> map = Maps.newHashMap();
        map.put("platform", platform);
        map.put("kolUrls", Lists.newArrayList(postUrl));
        map.put("campaignId", "2");
        JSONObject response = sendHttp(host + submit, JsonUtil.toJson(map));
        log.info("kol generate task response:{}", response);
        if (response == null) {
            throw new BusinessException("请求失败,请稍后重试");
        }
        int code = (Integer) response.get("code");
        if (code != 200) {
            String errorMsg = (String) response.get("message");
            throw new BusinessException("导入投放链接失败：" + errorMsg);
        }
        return new HairBaseResponseV1<>(((Number) response.get("data")).longValue());
    }

    /**
     * 获取达人信息，有就返回 没有就算了
     */
    @PostMapping("/getStarData")
    public HairBaseResponse<HairExportKolResponse> getStarData(@RequestBody @Validated HairMonitorKolTaskRequest request) {

        String platform = request.getPlatform();
        String postUrl = request.getUserUrl();
        if (!postUrl.contains(platform)) {
            throw new BusinessException("平台与链接不一致   ");
        }
        Map<Object, Object> map = Maps.newHashMap();
        map.put("platform", platform);
        map.put("kolUrl", request.getUserUrl());
        map.put("campaignId", "2");
        JSONObject response = sendHttp(host + kolDetail, JsonUtil.toJson(map));
        if (response == null) {
            throw new BusinessException("请求失败,请稍后重试");
        }
        int code = (Integer) response.get("code");
        if (code != 200) {
            String errorMsg = (String) response.get("msg");
            throw new BusinessException("导入投放链接失败：" + errorMsg);
        }
        JSONObject data = response.getJSONObject("data");
        HairExportKolResponse taskResponse = new HairExportKolResponse();
        BeanUtil.copyProperties(data, taskResponse);
        return new HairBaseResponse<>(taskResponse);
    }

    /**
     * 后测作品的监控详情
     */
    @PostMapping("/getPostMonitorData")
    public HairBaseResponse<HairMonitorResponseV2> getMonitorData(@RequestBody @Validated HairMonitorDataRequest request) {

        JSONObject response = sendHttp(host + getTaskResultList, JsonUtil.toJson(request));
        int code = (Integer) response.get("code");
        if (code != 200) {
            String errorMsg = (String) response.get("message");
            throw new BusinessException("查询作品详情失败：" + errorMsg);
        }
        HairMonitorResponseV2 taskResponse = new HairMonitorResponseV2();
        JSONObject data = response.getJSONObject("data");
        log.info("getPostMonitorData response data:{}", data);
        String postId = data.getString("postId");
        if (StrUtil.isEmpty(postId)) {
            return new HairBaseResponse<>(null);
        }
        taskResponse.setStatus(data.getString("status"));
        taskResponse.setPostId(data.getString("postId"));
        taskResponse.setPlatform(data.getString("platform"));
        taskResponse.setPublishedTime(data.getString("publishedTime"));
        taskResponse.setContent(data.getString("content"));
        taskResponse.setUrl(data.getString("url"));
        taskResponse.setTopicTags(data.getJSONArray("topicTags") == null ? Lists.newArrayList() : data.getJSONArray("topicTags").toJavaList(String.class));
        taskResponse.setBaseMetric(data.getObject("baseMetric", HairMonitorBaseMetric.class));
        taskResponse.setTrendMetric(data.getObject("trendMetric", new TypeReference<Map<String, List<HairMonitorTrendMetric>>>() {
        }));
        return new HairBaseResponse<>(taskResponse);
    }

    /**
     * 提交前测任务 更新达人价格。
     */
    @PostMapping("/submitPlatformPriceTask")
    public HairBaseResponse<HaiErSubmitPriceResponse> submitPlatformPriceTask(@RequestBody @Validated HairMonitorKolTaskRequest request) {
        request.setCampaignId(2L);
        JSONObject response = sendHttp(host + kolSubmit, JsonUtil.toJson(request));
        if (response == null) {
            throw new BusinessException("请求失败,请稍后重试");
        }
        int code = (Integer) response.get("code");
        if (code != 200) {
            String errorMsg = (String) response.get("message");
            throw new BusinessException("更新达人价格失败：" + errorMsg);
        }
        HaiErSubmitPriceResponse taskResponse = new HaiErSubmitPriceResponse();
        taskResponse.setTaskId(String.valueOf(response.get("data")));
        return new HairBaseResponse(taskResponse);
    }

    /**
     * 提交前测任务 更新达人价格。
     */
    @PostMapping("/getPlatformPriceTaskData")
    public HairBaseResponse<GetPlatformPriceTaskDataResponse> getPlatformPriceTaskData(@RequestBody @Validated GetPlatformPriceTaskDataRequest request) {
        JSONObject response = sendHttp(host + kolPrice, JsonUtil.toJson(request));
        if (response == null) {
            throw new BusinessException("请求失败,请稍后重试");
        }
        int code = (Integer) response.get("code");
        if (code != 200) {
            String errorMsg = (String) response.get("message");
            throw new BusinessException("查询达人价格信息失败：" + errorMsg);
        }
        JSONObject data = response.getJSONObject("data");
        GetPlatformPriceTaskDataResponse taskResponse = new GetPlatformPriceTaskDataResponse();
        BeanUtil.copyProperties(data, taskResponse);
        taskResponse.setUser_id(data.getString("userId"));
        return new HairBaseResponse<>(taskResponse);
    }

    private JSONObject sendHttp(String url, String json) {
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
        try (CloseableHttpClient client = HttpClients.custom().setDefaultRequestConfig(requestConfig).build()) {
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json");
            post.setHeader("Accept", "*/*");
            if (StrUtil.isNotEmpty(json)) {
                post.setEntity(new StringEntity(json, StandardCharsets.UTF_8));
            }
            try (CloseableHttpResponse response = client.execute(post)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 401) {
                    // 添加一个标志位来防止无限递归
                    if (!Thread.currentThread().getName().contains("retry")) {
                        Thread.currentThread().setName(Thread.currentThread().getName() + "-retry");
                        return sendHttp(url, json);
                    } else {
                        throw new BusinessException("Token刷新失败，已达到最大重试次数");
                    }
                }
                String responseBody = EntityUtils.toString(response.getEntity());
                if (responseBody == null) {
                    throw new BusinessException("接口请求失败");
                }
                log.info("Response Code: " + response.getStatusLine().getStatusCode());
                log.info("Response Body: " + responseBody);
                return JSON.parseObject(responseBody);
            }
        } catch (Exception e) {
            log.error("acquire category and brands fail", e);
        }
        return null;
    }

}
