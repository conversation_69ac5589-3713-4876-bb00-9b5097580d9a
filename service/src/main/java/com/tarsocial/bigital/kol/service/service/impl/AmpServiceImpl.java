package com.tarsocial.bigital.kol.service.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Script;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.entity.AmpTag;
import com.tarsocial.bigital.kol.common.domain.enums.WordRangeEnum;
import com.tarsocial.bigital.kol.common.domain.request.amp.AMPFilterRequest;
import com.tarsocial.bigital.kol.common.domain.request.amp.AMPKolTypeRequest;
import com.tarsocial.bigital.kol.common.domain.response.amp.AMPKolResponse;
import com.tarsocial.bigital.kol.common.domain.response.amp.AMPPostResponse;
import com.tarsocial.bigital.kol.service.dto.QueryDto;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import com.tarsocial.bigital.kol.service.mapper.AmpTagMapper;
import com.tarsocial.bigital.kol.service.service.AmpService;
import com.tarsocial.bigital.kol.service.util.DataUtil;
import com.tarsocial.bigital.kol.service.util.QueryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AmpServiceImpl implements AmpService {

    @Resource
    private AmpTagMapper ampTagMapper;

    @Qualifier("pgElasticsearchClient")
    @Resource
    private ElasticsearchClient client;

    public static final List<String> POST_SOURCE = Lists.newArrayList("long_interaction", "long_likeCount", "long_repostsCount", "long_commentsCount", "long_collectCount", "kw_coverUrl", "kw_url", "date_publishedAt", "kw_id", "kw_platform", "tx_title", "tx_content", "object_user", "kw_picUrl", "kw_videoUrl", "kw_amp_avatar", "kw_amp_nickname", "kw_amp_platformUserId", "long_30_no_paid_interaction", "long_30_no_paid_play", "long_30_paid_play", "long_30_paid_interaction", "kw_amp_url");

    @Override
    public List<AMPPostResponse> postList(AMPFilterRequest request) {
//        validTag(request.getPlatform(), parseKolType(request.getKolType()));
        QueryDto queryDto = buildRequest(request);

        SearchRequest searchRequest = SearchRequest.of(s ->
                s.index("kg_gs2_amp_v6_*")
                        .query(q -> q.bool(q2 -> q2.must(queryDto.getMust()).mustNot(queryDto.getMustNot())))
                        .sort(s1 -> s1.field(s2 -> s2.field("long_interaction").order(SortOrder.Desc)))
                        .highlight(h -> h
                                .fields("tx_title", hb -> hb.numberOfFragments(0))
                                .fields("tx_content", hb -> hb.numberOfFragments(0))
                                .fields("tx_ocr", hb -> hb.numberOfFragments(0))
                                .fields("tx_ocrCover", hb -> hb.numberOfFragments(0))
                                .fields("tx_asr", hb -> hb.numberOfFragments(0))
                        )
                        .size(100));

        log.info("AMP query postList dsl-> {}", searchRequest.toString());
        SearchResponse searchResponse = null;
        try {
            searchResponse = client.search(searchRequest, Map.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        List<AMPPostResponse> result = new ArrayList<>(2 << 6);
        List<Hit<Map>> hits = searchResponse.hits().hits();
        for (Hit<Map> hit : hits) {
            result.add(parseAMPPostResponse(hit.source(), hit.highlight()));
        }
        return result;
    }


    @Override
    public List<AMPKolResponse> kolList(AMPFilterRequest request) {
//        validTag(request.getPlatform(), parseKolType(request.getKolType()));
        QueryDto queryDto = buildRequest(request);

        HashMap<String, String> bucketsPath = new HashMap<>();
        bucketsPath.put("total", "sum_interaction");
        bucketsPath.put("count", "_count");
        Script script = Script.of(s -> s.inline(i -> i.source("params.total / params.count")));

        SearchRequest searchRequest = SearchRequest.of(s ->
                s.index("kg_gs2_amp_v6_*")
                        .aggregations("unique_user_id", a -> a.terms(l -> l.field("object_user.kw_userId").size(10000))
                                        .aggregations("sum_interaction", a3 -> a3.sum(sum -> sum.field("long_interaction")))
                                        .aggregations("avg_interaction", a4 -> a4.bucketScript(b1 -> b1.bucketsPath(b2 -> b2.dict(bucketsPath)).script(script)))
                                        .aggregations("sorted_buckets", a5 -> a5.bucketSort(a6 -> a6.sort(SortOptions.of(b6 -> b6.field(f6 -> f6.field("avg_interaction").order(SortOrder.Desc)))).size(200)))
                                        .aggregations("top_kw_id", a2 -> a2.terms(l -> l.field("kw_id").size(5)))
                        )
                        .query(q -> q.bool(q2 -> q2.must(queryDto.getMust()).mustNot(queryDto.getMustNot())))
                        .size(0));

        log.info("AMP query postList dsl-> {}", searchRequest.toString());
        SearchResponse<Map> searchResponse = null;
        try {
            searchResponse = client.search(searchRequest, Map.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<StringTermsBucket> userIdList = searchResponse.aggregations().get("unique_user_id").sterms().buckets().array();

        List<AMPKolResponse> result = new ArrayList<>(2 << 10);
        Map<String, List<String>> userPostMap = new HashMap<>(1500);
        userIdList.forEach(it -> {
            String userId = it.key().stringValue();
            long docCount = it.docCount();
            long sumInteraction = (long) it.aggregations().get("sum_interaction").sum().value();

            AMPKolResponse userInfo = new AMPKolResponse();
            userInfo.setKw_userId(userId);
            userInfo.setLong_avg_interaction(sumInteraction / docCount);
            userInfo.setLong_p6m_postNum(docCount);
            result.add(userInfo);

            List<String> topPostId = it.aggregations().get("top_kw_id").sterms().buckets().array().stream().map(p -> p.key().stringValue()).collect(Collectors.toList());
            userPostMap.put(userId, topPostId);
        });

        List<String> collect = userPostMap.values().stream().flatMap(List::stream).collect(Collectors.toList());

        HashMap<String, Hit<Map>> postMap = new HashMap<>(10000);

        ArrayList<Query> mustTerm = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getKeyword().getInclude())) {
            mustTerm.add(getKeywordIncludeQuery(request.getKeyword().getInclude()));
        }

        List<String> subList = new ArrayList<>(1024);
        collect.forEach(postId -> {
            subList.add(postId);
            if (subList.size() == 1000) {
                putPostMap(mustTerm, subList, postMap);
                subList.clear();
            }
        });
        putPostMap(mustTerm, subList, postMap);

        result.forEach(userInfo -> {
            List<String> posts = userPostMap.get(userInfo.getKw_userId());
            List<AMPPostResponse> postList = new ArrayList<>(posts.size());
            posts.forEach(postId -> {
                if (postMap.get(postId) != null) {
                    Map source = postMap.get(postId).source();
                    if (userInfo.getLong_followersCount() == null) {
                        userInfo.setLong_followersCount(DataUtil.getLong(source, "long_amp_followersCount"));
                        userInfo.setKw_avatar(DataUtil.getString(source, "kw_amp_avatar"));
                        userInfo.setKw_nickname(DataUtil.getString(source, "kw_amp_nickname"));
                        userInfo.setKw_url(DataUtil.getString(source, "kw_amp_url"));
                        userInfo.setKw_platformUserId(DataUtil.getString(source, "kw_amp_platformUserId"));
                    }
                    postList.add(parseAMPPostResponse(source, postMap.get(postId).highlight()));
                }
            });
            userInfo.setPosts(postList);
        });
        return result;
    }


    public List<AMPKolResponse> avgKolList(AMPFilterRequest request) {
//        validTag(request.getPlatform(), parseKolType(request.getKolType()));
        QueryDto queryDto = buildRequest(request);

        HashMap<String, String> bucketsPath = new HashMap<>();
        bucketsPath.put("total", "sum_interaction");
        bucketsPath.put("count", "_count");

        Script script = Script.of(s -> s.inline(i -> i.source("params.total / params.count")));

        SearchRequest searchRequest = SearchRequest.of(s ->
                s.index("kg_gs2_amp_v6_*")
                        .aggregations("unique_user_id", a -> a.terms(l -> l.field("object_user.kw_userId").size(10000))
                                .aggregations("sum_interaction", a3 -> a3.sum(sum -> sum.field("long_interaction")))
                                .aggregations("avg_interaction", a4 -> a4.bucketScript(b1 -> b1.bucketsPath(b2 -> b2.dict(bucketsPath)).script(script)))
                                .aggregations("sorted_buckets", a5 -> a5.bucketSort(a6 -> a6.sort(SortOptions.of(b6 -> b6.field(f6 -> f6.field("avg_interaction").order(SortOrder.Desc)))).size(500)))
                        )


                        .query(q -> q.bool(q2 -> q2.must(queryDto.getMust()).mustNot(queryDto.getMustNot())))
                        .size(0));

        log.info("AMP query postList dsl-> {}", searchRequest.toString());
        SearchResponse<Map> searchResponse = null;
        try {
            searchResponse = client.search(searchRequest, Map.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<StringTermsBucket> userIdList = searchResponse.aggregations().get("unique_user_id").sterms().buckets().array();

        List<AMPKolResponse> result = new ArrayList<>(2 << 10);
        Map<String, List<String>> userPostMap = new HashMap<>(1500);
        userIdList.forEach(it -> {
            String userId = it.key().stringValue();

            long docCount = it.docCount();
            long avgInteraction = (long) it.aggregations().get("avg_interaction").simpleValue().value();

            AMPKolResponse userInfo = new AMPKolResponse();
            userInfo.setKw_userId(userId);
            userInfo.setLong_avg_interaction(avgInteraction);
            userInfo.setLong_p6m_postNum(docCount);
            getLastUserInfo(userId, userInfo);
            result.add(userInfo);
        });

        return result;
    }


    private void getLastUserInfo(String userId, AMPKolResponse dto) {
        List<Query> queries = new ArrayList<>();
        queries.add(QueryUtil.term("object_user.kw_userId", userId));
        SearchRequest postRequest = SearchRequest.of(s ->
                s.index("kg_gs2_amp_v6_*")
                        .source(s3 -> s3.filter(f -> f.includes(POST_SOURCE)))
                        .query(q -> q.bool(q2 -> q2.must(queries)))
                        .sort(SortOptions.of(s1 -> s1.field(s2 -> s2.field("date_publishedAt").order(SortOrder.Desc))))
                        .size(1));

        log.info("AMP query post dsl-> {}", postRequest.toString());
        SearchResponse<Map> postResponse = null;
        try {
            postResponse = client.search(postRequest, Map.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        Map source = postResponse.hits().hits().get(0).source();
        dto.setKw_nickname(DataUtil.getString(source, "kw_amp_nickname"));
        dto.setKw_platformUserId(DataUtil.getString(source, "kw_amp_platformUserId"));
        dto.setKw_avatar(DataUtil.getString(source, "kw_amp_avatar"));
        dto.setKw_url(DataUtil.getString(source, "kw_amp_url"));
    }

    private void putPostMap(List<Query> mustTerm, List<String> postList, Map<String, Hit<Map>> postMap) {
        List<Query> queries = new ArrayList<>();
        queries.addAll(mustTerm);
        queries.add(QueryUtil.terms("kw_id", postList));
        SearchRequest postRequest = SearchRequest.of(s ->
                s.index("kg_gs2_amp")
                        .source(s3 -> s3.filter(f -> f.includes(POST_SOURCE)))
                        .query(q -> q.bool(q2 -> q2.must(queries)))
                        .highlight(h -> h
                                        .fields("tx_title", hb -> hb.numberOfFragments(0))
                                        .fields("tx_content", hb -> hb.numberOfFragments(0))
//                                .fields("tx_ocr", hb -> hb.numberOfFragments(0))
                                        .fields("tx_ocrCover", hb -> hb.numberOfFragments(0))
//                                .fields("tx_asr", hb -> hb.numberOfFragments(0))
                        )
                        .size(1000));

        log.info("AMP query post dsl-> {}", postRequest.toString());
        SearchResponse<Map> postResponse = null;
        try {
            postResponse = client.search(postRequest, Map.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        List<Hit<Map>> hits = postResponse.hits().hits();
        for (Hit<Map> hit : hits) {
            Map source = hit.source();
            postMap.put(DataUtil.getString(source, "kw_id"), hit);
        }
    }


    private QueryDto buildRequest(AMPFilterRequest request) {
        QueryDto queryDto = new QueryDto();
        List<Query> queries = Lists.newArrayList();
        List<Query> mustNot = Lists.newArrayList();
        //日期
        if (StringUtils.isNotEmpty(request.getStartDate()) && StringUtils.isNotEmpty(request.getEndDate())) {
            queries.add(QueryUtil.range("date_publishedAt", request.getStartDate(), request.getEndDate()));
        }

        //平台
        if (StringUtils.isNotEmpty(request.getPlatform())) {
            queries.add(QueryUtil.term("kw_platform", request.getPlatform()));
        }

        //达人类型
        if (CollectionUtils.isNotEmpty(parseKolType(request.getKolType()))) {
            queries.add(QueryUtil.terms("kw_amp_tags", parseKolType(request.getKolType())));
        }

        //达人层级 粉丝数
        if (CollectionUtils.isNotEmpty(request.getKolTier())) {
            List<Query> shouldQueries = Lists.newArrayList();
            request.getKolTier().forEach(it -> {
                List<Query> tierQuery = new ArrayList<>();
                tierQuery.add(QueryUtil.range("long_amp_followersCount", it.getMin(), it.getMax()));
                shouldQueries.add(QueryUtil.must(tierQuery));
            });

            if (CollectionUtils.isNotEmpty(shouldQueries)) {
                queries.add(QueryUtil.should(shouldQueries));
            }
        }

        //关键词
        if (request.getKeyword() != null) {
            List<List<String>> include = request.getKeyword().getInclude();
            if (CollectionUtils.isNotEmpty(include)) {
                queries.add(getKeywordIncludeQuery(include));
            }
            List<String> exclude = request.getKeyword().getExclude();
            if (CollectionUtils.isNotEmpty(exclude)) {
                List<Query> excludeQuery = new ArrayList<>();
                request.getKeyword().getExclude().forEach(it -> {
                    excludeQuery.add(QueryUtil.queryPlainWord(WordRangeEnum.getAllEsField(), it));
                });
                mustNot.add(QueryUtil.should(excludeQuery));
            }
        }

        // 抖音平台 近30天非商单播放/互动中位数不为0  或  30天商单播放/互动中位数不为0
        if ("douyin".equals(request.getPlatform())) {
            List<Query> shouldQuery = Lists.newArrayList();
            shouldQuery.add(QueryUtil.range("long_30_no_paid_interaction", 0, null));
            shouldQuery.add(QueryUtil.range("long_30_no_paid_play", 0, null));
            shouldQuery.add(QueryUtil.range("long_30_paid_play", 0, null));
            shouldQuery.add(QueryUtil.range("long_30_paid_interaction", 0, null));
            queries.add(QueryUtil.should(shouldQuery));
        }

        queryDto.setMust(queries);
        queryDto.setMustNot(mustNot);
        return queryDto;
    }

    private Query getKeywordIncludeQuery(List<List<String>> include) {
        if (CollectionUtils.isEmpty(include)) {
            return null;
        }

        List<Query> mustQuery = new ArrayList<>();
        include.forEach(it -> {
            List<Query> shouldQuery = new ArrayList<>();
            it.forEach(sub -> {
                shouldQuery.add(QueryUtil.queryPlainWord(WordRangeEnum.getAllEsField(), sub));
            });
            mustQuery.add(QueryUtil.should(shouldQuery));
        });
        return QueryUtil.must(mustQuery);
    }

    private List<String> parseKolType(List<AMPKolTypeRequest> kolType) {
        List<String> searchTags = new ArrayList<>();
        if (kolType != null) {
            kolType.forEach(it -> {
                if (CollectionUtils.isNotEmpty(it.getSecond())) {
                    searchTags.addAll(it.getSecond());
                } else {
                    searchTags.add(it.getFirst());
                }
            });
        }
        return searchTags;
    }

    /**
     * 校验传入的达人类型是否有效
     *
     * @param platform
     * @param kolTypeTags
     */
    private void validTag(String platform, List<String> kolTypeTags) {
        if (CollectionUtils.isEmpty(kolTypeTags)) {
            return;
        }

        List<AmpTag> tags = getTag(platform);
        HashSet<String> tagSet = new HashSet<>();
        tags.forEach(it -> {
            tagSet.add(it.getFirst());
            tagSet.add(it.getSecond());
        });

        kolTypeTags.forEach(it -> {
            if (!tagSet.contains(it)) {
                throw new BusinessException("参数校验错误,未知的达人类型: " + it);
            }
        });
    }


    private List<AmpTag> getTag(String platform) {
        LambdaQueryWrapper<AmpTag> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(platform), AmpTag::getPlatform, platform);
        return ampTagMapper.selectList(wrapper);
    }

    private AMPPostResponse parseAMPPostResponse(Map source, Map<String, List<String>> highlight) {
        AMPPostResponse dto = new AMPPostResponse();
        dto.setLong_interaction(DataUtil.getLong(source, "long_interaction"));
        dto.setLong_likeCount(DataUtil.getLong(source, "long_likeCount"));
        dto.setLong_repostsCount(DataUtil.getLong(source, "long_repostsCount"));
        dto.setLong_commentsCount(DataUtil.getLong(source, "long_commentsCount"));
        dto.setLong_collectCount(DataUtil.getLong(source, "long_collectCount"));

        dto.setLong_30_no_paid_interaction(DataUtil.getLong(source, "long_30_no_paid_interaction"));
        dto.setLong_30_no_paid_play(DataUtil.getLong(source, "long_30_no_paid_play"));
        dto.setLong_30_paid_play(DataUtil.getLong(source, "long_30_paid_play"));
        dto.setLong_30_paid_interaction(DataUtil.getLong(source, "long_30_paid_interaction"));

        dto.setKw_coverUrl(DataUtil.getString(source, "kw_coverUrl"));
        dto.setKw_url(DataUtil.getString(source, "kw_url"));
        dto.setDate_publishedAt(DataUtil.getString(source, "date_publishedAt"));
        dto.setKw_id(DataUtil.getString(source, "kw_id"));
        dto.setKw_platform(DataUtil.getString(source, "kw_platform"));

        dto.setTx_title(DataUtil.getString(source, "tx_title"));
        dto.setTx_ocr(DataUtil.getString(source, "tx_ocr"));
        dto.setTx_asr(DataUtil.getString(source, "tx_asr"));
        dto.setTx_content(DataUtil.getString(source, "tx_content"));

        if (source.get("object_user") != null) {
            Map object_user = (Map) source.get("object_user");
            dto.setKw_userId(DataUtil.getString(object_user, "kw_userId"));
        }

        //取高亮
        if (highlight != null) {
            if (highlight.get("tx_title") != null) {
                dto.setTx_title(highlight.get("tx_title").get(0));
            }
            if (highlight.get("tx_ocr") != null) {
                dto.setTx_ocr(highlight.get("tx_ocr").get(0));
            }
            if (highlight.get("tx_asr") != null) {
                dto.setTx_asr(highlight.get("tx_asr").get(0));
            }
            if (highlight.get("tx_content") != null) {
                dto.setTx_content(highlight.get("tx_content").get(0));
            }
        }

        if (source.get("kw_picUrl") != null) {
            dto.setArray_picUrl((List) source.get("kw_picUrl"));
        }
        if (source.get("kw_videoUrl") != null) {
            dto.setArray_videoUrl((List) source.get("kw_videoUrl"));
        }

        dto.setKw_avatar(DataUtil.getString(source, "kw_amp_avatar"));
        dto.setKw_nickname(DataUtil.getString(source, "kw_amp_nickname"));
        dto.setKw_platformUserId(DataUtil.getString(source, "kw_amp_platformUserId"));
        return dto;
    }

}
