package com.tarsocial.bigital.kol.service.service.tencent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.App;
import com.tarsocial.bigital.kol.service.mapper.AppMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @ClassName AppService
 * @date 2024年03月26日
 */
@Service
@Slf4j
public class AppService extends ServiceImpl<AppMapper, App> {

    @Resource
    private AppMapper appMapper;

    public App selectAppByKey(Long appId) {
        LambdaQueryWrapper<App> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(App::getId, appId);
        return appMapper.selectOne(wrapper);
    }


}
