package com.tarsocial.bigital.kol.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tarsocial.bigital.kol.common.domain.entity.AccessInterfaceLog;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface AccessInterfaceLogService extends IService<AccessInterfaceLog> {

    AccessInterfaceLog record(HttpServletRequest request, HttpServletResponse response, Exception ex);

}
