package com.tarsocial.bigital.kol.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @since 2024/4/12
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "service.abi.dev.oss")
public class AbiDevOssProperties {

    private String endpoint;

    private String accessKeyId;

    private String accessKeySecret;

    private String bucket;

    private String bucketName;


}
