package com.tarsocial.bigital.kol.service.constants;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public enum PlatformTaskEnum {

    /**
     * 抖音
     */
    DOU_YIN(94L, "douyin", "抖音"),

    /**
     * 小红书
     */
    KUAI_SHOU(106L, "kuaishou", "快手"),

    BILIBILI(1L, "bilibili", "B站"),

    RED(1L, "xiaohongshu", "小红书"),

    WEIBO(1L, "weibo", "微博");


    private final Long code;

    private final String name;

    private final String desc;


    PlatformTaskEnum(Long code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }


    public static PlatformTaskEnum getPlatformTaskByName(String name) {
        for (PlatformTaskEnum platformEnum : PlatformTaskEnum.values()) {
            if (platformEnum.getName().equals(name)) {
                return platformEnum;
            }
        }
        return PlatformTaskEnum.DOU_YIN;
    }

}
