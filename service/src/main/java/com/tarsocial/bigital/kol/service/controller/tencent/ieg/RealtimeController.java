package com.tarsocial.bigital.kol.service.controller.tencent.ieg;

import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.IegTaskCreateRequest;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.TencentTaskQueryRequest;
import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.common.domain.response.ieg.TencentTaskResponse;
import com.tarsocial.bigital.kol.service.service.ieg.RealtimeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/realtime")
public class RealtimeController {

    @Resource
    private RealtimeService realtimeService;


    /**
     * 创建短视频任务
     *
     * @param request
     * @return
     */
    @PostMapping("/shortVideoTask/create")
    public BaseResponse<Long> shortVideoTask(@RequestBody @Validated IegTaskCreateRequest request) {
        return new BaseResponse<>(realtimeService.shortVideoTask(request));
    }

//    @PostMapping("/shortVideoTask/query")
//    public BaseResponse<TencentTaskResponse> queryShortVideo(@RequestBody @Validated TencentTaskQueryRequest request) {
//        return new BaseResponse<>(realtimeService.queryShortVideo(request));
//    }


    /**
     * 创建直播任务
     *
     * @param request
     * @return
     */
    @PostMapping("/liveTask/create")
    public BaseResponse<Long> liveTask(@RequestBody @Validated IegTaskCreateRequest request) {
        return new BaseResponse<>(realtimeService.liveTask(request));
    }

    @PostMapping("/query")
    public BaseResponse<TencentTaskResponse> queryLiveTask(@RequestBody @Validated TencentTaskQueryRequest request) {
        return new BaseResponse<>(realtimeService.queryLiveTask(request));
    }

}
