package com.tarsocial.bigital.kol.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tarsocial.bigital.kol.common.domain.entity.AbiDictEntity;
import com.tarsocial.bigital.kol.common.domain.entity.PepsicoDictEntity;
import com.tarsocial.bigital.kol.service.mapper.AbiDictMapper;
import com.tarsocial.bigital.kol.service.mapper.PepsicoDictMapper;
import com.tarsocial.bigital.kol.service.service.DictService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/13
 */
@Service
public class DictServiceImpl implements DictService {

    @Resource
    private PepsicoDictMapper pepsicoDictMapper;

    @Resource
    private AbiDictMapper abiDictMapper;


    @Override
    public Set<PepsicoDictEntity> listAllByType() {


        LambdaQueryWrapper<PepsicoDictEntity> lqw = new LambdaQueryWrapper<>();
        lqw.in(PepsicoDictEntity::getQuarter, "Q1");
        List<PepsicoDictEntity> dictWordEntities = pepsicoDictMapper.selectList(lqw);

        return dictWordEntities.stream().map(it -> {
                    if ("抖音".equals(it.getPlatform())) {
                        it.setPlatform("douyin");
                    }
                    if ("红书".equals(it.getPlatform())) {
                        it.setPlatform("xiaohongshu");
                    }
                    return it;
                }).sorted(Comparator.comparing(PepsicoDictEntity::getDate).reversed())
                .collect(Collectors.toCollection(LinkedHashSet::new));

    }


    @Override
    public List<AbiDictEntity> listAllAbi() {


//        LambdaQueryWrapper<AbiDictEntity> lqw = new LambdaQueryWrapper<>();

        List<AbiDictEntity> dictWordEntities = abiDictMapper.selectList(null);

        return dictWordEntities;

    }

}

