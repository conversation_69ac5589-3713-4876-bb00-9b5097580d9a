package com.tarsocial.bigital.kol.service.config.sign;

import com.tarsocial.bigital.kol.service.config.global.TokenFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class RequestWrapperFilter {

    @Bean
    RequestBodyWrapperFilter getRequestBodyWrapperFilter(){
        return new RequestBodyWrapperFilter();
    }

    @Bean("requestBodyWrapperFilter")
    public FilterRegistrationBean<RequestBodyWrapperFilter> checkUserFilter(RequestBodyWrapperFilter bodyWrapperFilter) {
        FilterRegistrationBean<RequestBodyWrapperFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(bodyWrapperFilter);
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(1);
        registrationBean.setAsyncSupported(true);
        return registrationBean;
    }

//    /**
//     * 配置需要验证token的URL
//     */
//    @Value("${ieg.intercept.list}")
//    private String[] iegList;
//
//    @Bean
//    public FilterRegistrationBean<TokenFilter> tokenFilterRegistration() {
//        FilterRegistrationBean<TokenFilter> registration = new FilterRegistrationBean<>();
//        registration.setFilter(new TokenFilter());
//        // 设置过滤器拦截的URL模式
//        registration.addUrlPatterns(iegList);
//
//        return registration;
//    }

}
