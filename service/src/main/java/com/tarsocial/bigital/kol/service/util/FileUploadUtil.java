package com.tarsocial.bigital.kol.service.util;

import com.alibaba.fastjson.JSON;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;

public class FileUploadUtil {

    private static final String ssoUrl = "https://t-llm.tarsocial.com/oss/upload";

    /**
     * 上传文件到SSO 返回文件url
     *
     * @param path
     * @return
     */
    public static String fileUpload(String path) {
        // File to be uploaded
        File file = new File(path);

        // Prepare headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new FileSystemResource(file));
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        // Create RestTemplate and execute the request
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.exchange(
                ssoUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
        );

        if (response.getStatusCode() == HttpStatus.OK) {
            return JSON.parseObject(response.getBody()).getString("data");
        }
        return null;
    }
}
