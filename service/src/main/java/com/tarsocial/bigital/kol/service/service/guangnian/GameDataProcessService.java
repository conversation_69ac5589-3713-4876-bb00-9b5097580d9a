package com.tarsocial.bigital.kol.service.service.guangnian;

import java.util.*;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.tarsocial.bigital.kol.common.domain.entity.Post;
import com.tarsocial.bigital.kol.service.constants.Constants;
import com.tarsocial.bigital.kol.common.domain.entity.guangnian.EmotionCountBean;
import com.tarsocial.bigital.kol.common.domain.entity.guangnian.GamePostVo;
import com.tarsocial.bigital.kol.service.service.DataCenterProcessService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.bigital.kol.service.util.Downloader;
import com.tarsocial.bigital.kol.service.util.email.SendMailBean;
import com.tarsocial.bigital.kol.service.util.email.TemplateTypeEnum;
import com.tarsocial.enums.NullColumnEnum;
import com.tarsocial.enums.OperationEnum;
import com.tarsocial.enums.OrderTypeEnum;
import com.tarsocial.request.CommonKeyAndValue;
import com.tarsocial.request.DocSourceV1Request;
import com.tarsocial.request.FilterRequest;
import com.tarsocial.response.DocSourceResponse;
import com.tarsocial.vo.FieldOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @ClassName GameDataProcessService
 * @date 2024年04月23日
 */
@Service
@Slf4j
public class GameDataProcessService {

    @Resource
    private DataCenterProcessService dataCenterProcessService;

    @Value("${game.email.receiver}")
    private String[] receiver;

    @Value("${game.email.ccUser}")
    private String[] ccUser;

    /**
     * 监测数据获取
     */
    public byte[] gameData(Date start, Date end) {
        // 数据集合
        List<List<?>> dataList = org.apache.commons.compress.utils.Lists.newArrayList();
        // sheet名称
        List<String> sheetNameList = Arrays.asList("funplus", "竞品", "DEEP 1_游戏", "DEEP 2_游戏+话题");
        // 数据类型
        List<Class<?>> clazzList = Arrays.asList(GamePostVo.class, GamePostVo.class, GamePostVo.class, GamePostVo.class);

        Map<String, EmotionCountBean> map = new ConcurrentHashMap<>(4);

        List<GamePostVo> oneVoList = quarkPost(start, end, 90096L, Constants.ONE_KEYWORD);
        map.put("funplus", emotionCount(oneVoList));
        dataList.add(oneVoList);
        log.info("oneVoList size ->{}", oneVoList.size());

        List<GamePostVo> towVoList = quarkPost(start, end, 90092L, Constants.TWO_KEYWORD);
        map.put("竞品", emotionCount(towVoList));
        dataList.add(towVoList);

        List<GamePostVo> threeVoList = quarkPost(start, end, 99533L, Constants.THREE_KEYWORD);
        map.put("deep1", emotionCount(threeVoList));
        dataList.add(threeVoList);

        List<GamePostVo> fourVoList = quarkPost(start, end, 90093L, Constants.FOUR_KEYWORD);
        map.put("deep2", emotionCount(fourVoList));
        dataList.add(fourVoList);

        log.info("count {}", JSON.toJSONString(map));
        return Downloader.writeExcel(dataList, sheetNameList, clazzList, Sets.newHashSet("ocrCover"));
    }

    private EmotionCountBean emotionCount(List<GamePostVo> towVoList) {
        long pos = towVoList.stream().filter(x -> "正向".equals(x.getEmotion())).count();
        long neg = towVoList.stream().filter(x -> "负向".equals(x.getEmotion())).count();
        return new EmotionCountBean(pos, neg, towVoList.size());
    }

    public List<GamePostVo> quarkPost(Date start, Date end, Long mainId, HashMap<String, List<Pattern>> keywordMap) {
        List<GamePostVo> list = Lists.newArrayList();
        DocSourceV1Request docSourceV1Request = doDealRequest(start, end, mainId);
        DocSourceResponse response = pullPost(docSourceV1Request, list, keywordMap);

        String scrollId = response.getScrollId();
        boolean hasMore = response.getRows().size() == 10000;
        while (hasMore) {
            docSourceV1Request.setScrollId(scrollId);
            DocSourceResponse scrollResponse = pullPost(docSourceV1Request, list, keywordMap);
            scrollId = scrollResponse.getScrollId();
            hasMore = scrollResponse.getRows().size() == 10000;
        }
        return list;
    }

    private DocSourceResponse pullPost(DocSourceV1Request docSourceV1Request, List<GamePostVo> list, HashMap<String, List<Pattern>> keywordMap) {
        DocSourceResponse response = dataCenterProcessService.sourceV1(docSourceV1Request);
        log.info("原始数据量， size--->{}", response.getRows().size());
        response.getRows().forEach(x -> {
            if (Objects.isNull(x) || CollectionUtils.isEmpty(x.getSource())) {
                return;
            }
            GamePostVo gamePostVo = processPost(x.getSource());
            processKeyword(gamePostVo, keywordMap);
            if (Objects.nonNull(gamePostVo.getKeyword())) {
                list.add(gamePostVo);
            }
        });
        return response;
    }

    public FilterRequest getSimpleFilterComments() {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setCustomerDimension(false);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(312L);
        commonKeyAndValue.setValue(Lists.newArrayList("评论"));
        ruleIds.add(commonKeyAndValue);
        contentFilter.setOperationEnum(OperationEnum.exclude);
        contentFilter.setAllowNull(NullColumnEnum.CONTAIN_NULL_COLUMN);
        contentFilter.setRuleIds(ruleIds);
        return contentFilter;
    }

    private DocSourceV1Request doDealRequest(Date start, Date end, Long party) {

        DocSourceV1Request request = new DocSourceV1Request();
        request.setCount(10000);

        request.setMainPartyId(party);
        // 排序字段
        List<FieldOrderVo> orderBy = Lists.newArrayList();
        FieldOrderVo fieldOrderVo = new FieldOrderVo();
        fieldOrderVo.setName("date_publishedAt");
        fieldOrderVo.setOrderType(OrderTypeEnum.DESC);

        orderBy.add(fieldOrderVo);
        request.setOrderBy(orderBy);
        // 需要展示字段
        request.setFlatSource(false);
        // 开始时间和结束时间
        request.setStartDate(start);
        request.setEndDate(end);

        request.setArrayFlatSource(true);

        request.setPlatformIds(Arrays.asList(43L, 44L, 45L, 46L, 47L, 48L, 49L, 50L, 51L, 52L, 53L, 57L));
        //过滤条件
        List<FilterRequest> filters = Lists.newArrayList();
        filters.add(getSimpleFilterComments());
        request.setFilter(filters);
        request.setKeepAlive(5);
        request.setIndexType("post");
        return request;
    }

    private void processKeyword(GamePostVo gamePostVo, HashMap<String, List<Pattern>> keywordMap) {
        String content = gamePostVo.getContent() + gamePostVo.getOriginContent() + gamePostVo.getTitle() + gamePostVo.getOcrCover();
        List<String> list = Lists.newArrayList();
        keywordMap.forEach((key, value) -> value.forEach(p -> {
            if (StringUtils.hasLength(content) && p.matcher(content).find()) {
                list.add(key);
            }
        }));
        if (!CollectionUtils.isEmpty(list)) {
            gamePostVo.setKeyword(org.apache.commons.lang3.StringUtils.join(list, ";"));
        }
    }

    private GamePostVo processPost(Map<String, Object> source) {
        GamePostVo gamePostVo = new GamePostVo();
        Post post = JSON.parseObject(JSON.toJSONString(source), Post.class);

        gamePostVo.setPlatform(post.getDomain());
        gamePostVo.setContent(post.getContent());
        gamePostVo.setTitle(post.getTitle());
        gamePostVo.setUrl(post.getUrl());
        gamePostVo.setType(post.getContentType());
        gamePostVo.setOcrCover(post.getOcrCover());

        String publishedAt = post.getDatePublishedAt();
        if (Objects.nonNull(publishedAt) && publishedAt.length() == 19) {
            gamePostVo.setPublishedAt(DateUtil.convertDate(publishedAt, "yyyy-MM-dd HH:mm:ss"));
        }

        if (Objects.isNull(post.getObjectEmotionLabel())) {
            gamePostVo.setEmotion("中性");
        } else {
            String emotion = post.getObjectEmotionLabel().getEmotion();
            gamePostVo.setEmotion(Objects.isNull(emotion) ? "中性" : emotion);
        }

        if (Objects.nonNull(post.getObjectOrigin())) {
            gamePostVo.setOriginContent(post.getObjectOrigin().getContent());
        }

        Post.ObjectUser objectUser = post.getObjectUser();
        if (Objects.nonNull(objectUser)) {
            gamePostVo.setFans(objectUser.getFollowersCount());
            gamePostVo.setAuthorName(objectUser.getNickname());
        }

        return gamePostVo;
    }


    public SendMailBean postData(Date start, Date end) {
        SendMailBean sendMailBean = new SendMailBean();

        // 数据集合
        List<List<?>> dataList = org.apache.commons.compress.utils.Lists.newArrayList();
        // sheet名称
        List<String> sheetNameList = Arrays.asList("funplus", "竞品", "DEEP 1_游戏", "DEEP 2_游戏+话题");
        // 数据类型
        List<Class<?>> clazzList = Arrays.asList(GamePostVo.class, GamePostVo.class, GamePostVo.class, GamePostVo.class);

        Map<String, EmotionCountBean> map = new ConcurrentHashMap<>(4);


        List<GamePostVo> oneVoList = quarkPost(start, end, 90096L, Constants.ONE_KEYWORD);
        map.put("funplus", emotionCount(oneVoList));
        dataList.add(oneVoList);

        List<GamePostVo> towVoList = quarkPost(start, end, 90092L, Constants.TWO_KEYWORD);
        map.put("竞品", emotionCount(towVoList));
        dataList.add(towVoList);

        List<GamePostVo> threeVoList = quarkPost(start, end, 99533L, Constants.THREE_KEYWORD);
        map.put("deep1", emotionCount(threeVoList));
        dataList.add(threeVoList);

        List<GamePostVo> fourVoList = quarkPost(start, end, 90093L, Constants.FOUR_KEYWORD);
        map.put("deep2", emotionCount(fourVoList));
        dataList.add(fourVoList);

        // 邮件内容构建
        Map<String, Object> templateMap = buildMap(map);


        String subject = "funplus及竞品数据";

        if (Objects.equals(DateUtil.formatDate(start, "HH"), "17")) {
            subject = subject + DateUtil.formatDate(end, "MMdd") + "上午";
        } else {
            subject = subject + DateUtil.formatDate(start, "MMdd") + "下午";
        }

        sendMailBean.setReceiver(receiver)
                .setSubject(subject)
                .setUsername("funplus及竞品数据推送")
                .setCcUser(ccUser)
                .setTemplateMap(templateMap)
                .setFileName(subject)
                .setTemplateType(TemplateTypeEnum.GAME_POST.getCode())
                .setHasAttachment(true)
                .setByteArrayResource(new ByteArrayResource(Downloader.writeExcel(dataList, sheetNameList, clazzList, Sets.newHashSet("ocrCover"))));
        return sendMailBean;
    }

    private Map<String, Object> buildMap(Map<String, EmotionCountBean> map) {
        Map<String, Object> tempMap = new HashMap<>(1);
        StringBuilder sb = new StringBuilder();
        AtomicReference<Integer> i = new AtomicReference<>(1);
        map.forEach((key, value) -> sb.append("<div>")
                .append(i.getAndSet(i.get() + 1))
                .append(". ")
                .append(key)
                .append("：")
                .append(value.getCount())
                .append("条（正面：")
                .append(value.getPos())
                .append("条，负面：")
                .append(value.getNeg())
                .append("条）</div>\n"));
        tempMap.put("content", sb.toString());
        return tempMap;
    }

    public void quarkPostTest(Date start, Date end, Long mainId) {
        DocSourceV1Request docSourceV1Request = doDealRequest(start, end, mainId);
        DocSourceResponse response = dataCenterProcessService.sourceV1(docSourceV1Request);
        List<Post> posts = Lists.newArrayList();
        response.getRows().forEach(x -> {
            if (Objects.isNull(x) || CollectionUtils.isEmpty(x.getSource())) {
                return;
            }
            log.info("source {}", JSON.toJSONString(x.getSource()));
            Post post = JSON.parseObject(JSON.toJSONString(x.getSource()), Post.class);
            log.info("Post {}", JSON.toJSONString(post));
            posts.add(post);
        });
        System.out.println(posts);
    }
}
