package com.tarsocial.bigital.kol.service.config;

import co.elastic.clients.elasticsearch.ElasticsearchAsyncClient;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * middle es
 * <AUTHOR>
 * @since 2024/4/12
 */
@AllArgsConstructor
@Configuration
public class MiddleEsConfiguration {

    @Resource
    private final MiddleEsProperties properties;

    @Primary
    @Bean(name = "elasticsearchTransport")
    public ElasticsearchTransport elasticsearchTransport() {
        // https://www.elastic.co/guide/en/elasticsearch/client/java-api-client/7.17/_basic_authentication.html
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(properties.getUsername(), properties.getPassword()));

        // Create the low-level client
        RestClient restClient = RestClient
                .builder(new HttpHost(properties.getHost()))
                .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
                .setRequestConfigCallback(requestConfigBuilder ->
                        requestConfigBuilder.setConnectTimeout(5000)
                                // 300秒
                                .setSocketTimeout(300000)
                                // 15
                                .setConnectionRequestTimeout(15000)
                )
                .build();

        // Create the transport with a Jackson mapper

        ObjectMapper objectMapper  = handleEsConf();

        return new RestClientTransport(restClient, new JacksonJsonpMapper(objectMapper));
    }

    static ObjectMapper handleEsConf() {
        ObjectMapper objectMapper = new ObjectMapper();
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        objectMapper.registerModule(javaTimeModule);
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
        return objectMapper;
    }

    @Bean(name = "elasticsearchClient")
    public ElasticsearchClient elasticsearchClient(@Qualifier("elasticsearchTransport") ElasticsearchTransport transport) {
        // And create the API client
        return new ElasticsearchClient(transport);
    }

    @Bean(name = "elasticsearchAsyncClient")
    public ElasticsearchAsyncClient elasticsearchAsyncClient(@Qualifier("elasticsearchTransport") ElasticsearchTransport transport) {
        return new ElasticsearchAsyncClient(transport);
    }

}
