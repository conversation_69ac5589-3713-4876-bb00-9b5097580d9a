package com.tarsocial.bigital.kol.service;

import co.elastic.clients.json.JsonpUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * <AUTHOR>
 */
@ConfigurationPropertiesScan
@EnableScheduling
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableWebMvc
@EnableFeignClients(basePackages = {"com.tarsocial.bigital.kol.service.client", "com.tarsocial.dag.framework.auth.spi"})
public class TransmissionServiceApplication {
    public static void main(String[] args) {
        JsonpUtils.maxToStringLength(Integer.MAX_VALUE);
        SpringApplication.run(TransmissionServiceApplication.class, args);
    }
}
