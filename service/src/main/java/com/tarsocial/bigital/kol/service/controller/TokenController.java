package com.tarsocial.bigital.kol.service.controller;

import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.IegTokenRequest;
import com.tarsocial.bigital.kol.common.domain.response.ieg.IegBaseResponse;
import com.tarsocial.bigital.kol.service.service.TransmissionAuthService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class TokenController {


    @Resource
    private TransmissionAuthService transmissionAuthService;

    @PostMapping("/getToken")
    public IegBaseResponse<Object> iegTaskCreate(@RequestBody IegTokenRequest request, HttpServletResponse response) {
        Object token = transmissionAuthService.getToken(request, response);
        if (token == null) {
            return new IegBaseResponse<>(-1, "获取授权失败");
        }
        return new IegBaseResponse<>(token);
    }
}
