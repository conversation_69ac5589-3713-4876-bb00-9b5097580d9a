package com.tarsocial.bigital.kol.service.controller.guangnian;


import com.tarsocial.bigital.kol.common.domain.entity.guangnian.GameRequest;
import com.tarsocial.bigital.kol.service.service.guangnian.GameDataProcessService;
import com.tarsocial.bigital.kol.service.util.Downloader;
import com.tarsocial.bigital.kol.service.util.email.MailUtil;
import com.tarsocial.bigital.kol.service.util.email.SendMailBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/game")
public class GameDataController {

    @Resource
    private GameDataProcessService gameDataProcessService;

    @Resource
    private MailUtil mailUtil;

    @Resource
    @Qualifier("asyncExecutor")
    private Executor executor;

    @PostMapping("/export/post")
    public void exportPost(@RequestBody GameRequest request, HttpServletResponse response) throws IOException {
        // 导出数据
        byte[] bytes = gameDataProcessService.gameData(request.getStart(), request.getEnd());
        Downloader.downloadExcel(response, bytes, "funPlus及竞品数据0424下午");
    }

    @PostMapping("/send/message")
    public void sendFemEmail(@RequestBody GameRequest request) {
        SendMailBean bean = gameDataProcessService.postData(request.getStart(), request.getEnd());
        executor.execute(() -> mailUtil.sendAttachFileMail(bean));
    }
}
