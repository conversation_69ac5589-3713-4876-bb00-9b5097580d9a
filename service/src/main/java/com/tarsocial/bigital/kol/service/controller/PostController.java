package com.tarsocial.bigital.kol.service.controller;

import com.tarsocial.bigital.kol.common.domain.dto.PostDetailDto;
import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.service.config.PepsicoDevOssProperties;
import com.tarsocial.bigital.kol.service.job.PushDataSchedule;
import com.tarsocial.bigital.kol.service.service.PostService;
import com.tarsocial.bigital.kol.service.util.OssUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.util.Collections;
import java.util.List;

import static com.tarsocial.bigital.kol.service.runner.InitDictDataRunner.DICT_DATA_BY_TYPE;

/**
 * <AUTHOR>
 * @since 2024/3/14
 */
@Api(tags = {"【原帖】"})
@RestController
@AllArgsConstructor
@RequestMapping("/post")
public class PostController {

    @Resource
    private PostService postService;


    @Resource
    private PushDataSchedule publishSchedule;

    @Resource
    private PepsicoDevOssProperties pepsicoDevOssProperties;


    @Resource
    private OssUtil ossUtil;


    @PostMapping("/pepsico/list")
    @ApiOperation(value = "百事数据推送")
    public BaseResponse<List<PostDetailDto>> originalPost() {
        Long dyRule = 90052L;
        Long xhsRule = 89633L;
        return new BaseResponse<>(postService.originalPost(DICT_DATA_BY_TYPE, dyRule, xhsRule));
    }

    @PostMapping("abi/list")
    @ApiOperation(value = "abi 定时推送数据")
    public BaseResponse<List<PostDetailDto>> abiPushData() {
        publishSchedule.pushData();
        return new BaseResponse<>(Collections.emptyList());
    }

    @PostMapping("abi/check/oss/data")
    @ApiOperation(value = "检查数据是否在 oss 上")
    public BaseResponse<List<PostDetailDto>> checkOssData() {
        publishSchedule.checkData();
        return new BaseResponse<>(Collections.emptyList());
    }

    @GetMapping("/upload")
    @ApiOperation(value = "上传文件")
    public BaseResponse<List<PostDetailDto>> uploadFile(@RequestParam String filePath, @RequestParam String ossFilePath) {
        ossUtil.uploadAbiDevFile(new File(filePath), ossFilePath);
        return new BaseResponse<>(Collections.emptyList());
    }

    @GetMapping("/upload/pepsico")
    @ApiOperation(value = "上传文件")
    public BaseResponse<List<PostDetailDto>> uploadFilePepsico(@RequestParam String filePath, @RequestParam String ossFilePath) {
        ossUtil.uploadPepsicoDevFile(new File(filePath), ossFilePath, pepsicoDevOssProperties);
        return new BaseResponse<>(Collections.emptyList());
    }

    @GetMapping("/export")
    @ApiOperation(value = "根据夸克规则原贴导出")
    public BaseResponse<List<PostDetailDto>> exportPost() {
        postService.exportData();
        return new BaseResponse<>(Collections.emptyList());
    }


}
