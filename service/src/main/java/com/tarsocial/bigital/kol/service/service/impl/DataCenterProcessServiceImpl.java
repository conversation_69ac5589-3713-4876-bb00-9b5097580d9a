package com.tarsocial.bigital.kol.service.service.impl;


import com.alibaba.fastjson2.JSON;
import com.tarsocial.bigital.kol.service.client.QuarkExtendSpi;
import com.tarsocial.bigital.kol.service.service.DataCenterProcessService;
import com.tarsocial.bigital.kol.service.service.DataCenterTokenServiceImpl;
import com.tarsocial.request.DocSourceV1Request;
import com.tarsocial.response.DocSourceResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 夸克3.0数据对接
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataCenterProcessServiceImpl implements DataCenterProcessService {

    @Resource
    private QuarkExtendSpi carslanExtendSpi;

    @Resource
    private DataCenterTokenServiceImpl dataCenterTokenServiceImpl;


    /**
     * 文档源接口 根据条件获取帖子
     */
    @Override
    public DocSourceResponse sourceV1(DocSourceV1Request request) {
        // 查询参数构建
        String jsonReq = JSON.toJSONString(request);
        log.info("postExtendSpi sourceV1 request info ---> {}", jsonReq.substring(0, Math.min(jsonReq.length(), 500)));
        DocSourceResponse response = null;
        StopWatch stopWatch = new StopWatch();
        // 开始时间
        stopWatch.start();

        // 设置最大重试次数
        int maxRetries = 3;

        // 设置初始延迟时间 15s
        long initialDelayMillis = 15000;

        // 设置延迟递增因子
        double delayMultiplier = 2.0;

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                response = carslanExtendSpi.sourceV1(request);
                break;
            } catch (Exception e) {
                log.warn("postExtendSpi sourceV1 failed ---> ", e);
                dataCenterTokenServiceImpl.refreshToken();
                // 如果是最后一次重试，则不再延迟
                if (attempt == maxRetries - 1) {
                    break;
                }
                // 计算延迟时间
                long delayMillis = (long) (initialDelayMillis * Math.pow(delayMultiplier, attempt));
                // 延迟一段时间后进行下一次重试
                try {
                    log.warn("postExtendSpi sourceV1 failed 重试第 {} 次 ---> ", attempt);
                    TimeUnit.MILLISECONDS.sleep(delayMillis);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                }
            }
        }

        // 结束时间
        stopWatch.stop();
        // 统计执行时间（秒）
        log.info("sourceV1 执行时长：{} 秒", stopWatch.getTotalTimeSeconds());
        return response;
    }
}
