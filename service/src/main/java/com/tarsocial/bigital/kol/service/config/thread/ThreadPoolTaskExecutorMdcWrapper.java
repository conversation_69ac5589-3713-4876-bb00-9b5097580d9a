package com.tarsocial.bigital.kol.service.config.thread;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.validation.constraints.NotNull;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * ThreadPoolTaskExecutor 包装类
 * <AUTHOR>
 */
public class ThreadPoolTaskExecutorMdc<PERSON>rapper extends ThreadPoolTaskExecutor {

    public ThreadPoolTaskExecutorMdcWrapper() {
        super();
    }

    @Override
    public void execute(Runnable task) {
        super.execute(new MdcRunnable(task));
    }

    @NotNull
    @Override
    public <T> Future<T> submit(@NotNull Callable<T> task) {
        return super.submit(new MdcCallable(task));
    }

    @NotNull
    @Override
    public Future<?> submit(@NotNull Runnable task) {
        return super.submit(new MdcRunnable(task));
    }
}
