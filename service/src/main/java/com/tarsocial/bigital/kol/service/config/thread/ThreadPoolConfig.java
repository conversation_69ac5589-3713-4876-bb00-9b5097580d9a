package com.tarsocial.bigital.kol.service.config.thread;

import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * The class ThreadPoolConfig.
 *
 * <AUTHOR>
 */
@Component
public class ThreadPoolConfig {

    private static final String PUSH_PREFIX = "--data--Executor--";
    private static final int PUSH_CORE_POOL_SIZE = 8;
    private static final int PUSH_MAX_POOL_SIZE = 20;
    private static final int PUSH_KEEP_ALIVE_TIME = 30;


    @Bean("dataThreadPool")
    public ThreadPoolTaskExecutor getThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor pushThreadPool = new ThreadPoolTaskExecutorMdcWrapper();
        pushThreadPool.setThreadNamePrefix(PUSH_PREFIX);
        pushThreadPool.setCorePoolSize(PUSH_CORE_POOL_SIZE);
        pushThreadPool.setMaxPoolSize(PUSH_MAX_POOL_SIZE);
        pushThreadPool.setKeepAliveSeconds(PUSH_KEEP_ALIVE_TIME);
        pushThreadPool.setQueueCapacity(1000);
        pushThreadPool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        pushThreadPool.setAllowCoreThreadTimeOut(true);
        return pushThreadPool;
    }

    @Bean("asyncExecutor")
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutorMdcWrapper();
        // 核心线程数10：线程池创建时候初始化的线程数
        executor.setCorePoolSize(8);
        // 最大线程数20：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
        executor.setMaxPoolSize(20);
        // 缓冲队列200：用来缓冲执行任务的队列
        executor.setQueueCapacity(1000);
        // 允许线程的空闲时间60*5秒：当超过了核心线程出之外的线程在空闲时间到达之后会被销毁
        executor.setKeepAliveSeconds(60 * 5);
        // 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
        executor.setThreadNamePrefix("asyncExecutor-");
        // 线程池对拒绝任务的处理策略：这里采用了CallerRunsPolicy策略，当线程池没有处理能力的时候，该策略会直接在 execute 方法的调用线程中运行被拒绝的任务；如果执行程序已关闭，则会丢弃该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
