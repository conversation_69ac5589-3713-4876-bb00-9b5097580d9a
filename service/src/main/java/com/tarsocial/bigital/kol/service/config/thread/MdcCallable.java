package com.tarsocial.bigital.kol.service.config.thread;

import org.slf4j.MDC;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;

/**
 * 短期、一次性任务执行，支持MDC透传
 * <AUTHOR>
 */
public class MdcCallable<V> implements Callable<V> {

    private final Callable<V> callable;

    private transient final Map<String, String> contextMap = MDC.getCopyOfContextMap();

    public MdcCallable(Callable<V> callable) {
        this.callable = callable;
    }

    @Override
    public V call() throws Exception {
        if (Objects.nonNull(contextMap)) {
            MDC.setContextMap(contextMap);
        }
        try {
            return callable.call();
        } finally {
            MDC.clear();
        }
    }

}
