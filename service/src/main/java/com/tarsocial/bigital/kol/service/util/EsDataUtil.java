package com.tarsocial.bigital.kol.service.util;

/**
 * <p>
 *      ES数据处理工具类
 * </p>
 *
 * <AUTHOR> GoodLuck
 * @date 2023/10/18$ 10:42
 */


import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class EsDataUtil {
    public static String existData(Object obj) {
        return Objects.nonNull(obj) ? obj.toString() : null;
    }


    public static Double existDouble(Object obj) {
        return Objects.nonNull(obj) ? Double.parseDouble(obj.toString()) : null;
    }

    public static Boolean existBoolean(Object obj) {
        return Objects.nonNull(obj) ? Boolean.parseBoolean(obj.toString()) : null;
    }



    public static Integer existNumber(Object obj) {
        return Objects.nonNull(obj) ? Integer.parseInt(obj.toString()) : 0;
    }

    public static Long existLong(Object obj) {
        return Objects.nonNull(obj) ? Long.parseLong(obj.toString()) : 0L;
    }

    /**
     * 对象中数据获取
     *
     * @return
     */
    public static Map<String, Object> getMap(Object obj) {
        ObjectMapper objectMapper = new ObjectMapper();
        if (obj instanceof Map) {
            return objectMapper.convertValue(obj, Map.class);
        }
        return new HashMap<>(1);
    }

    /**
     * 对象中数据获取
     */
    public static List<Object> getList(Object obj) {
        ObjectMapper objectMapper = new ObjectMapper();
        if (obj instanceof List) {
            return objectMapper.convertValue(obj, List.class);
        }
        return new ArrayList<>(1);
    }

    public static List<String> getStrList(Object obj) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<String> list = new ArrayList<>();
        if (obj instanceof List) {
            final List listObj = objectMapper.convertValue(obj, List.class);
            list.addAll(listObj);
            return list;
        }
        return new ArrayList<>(1);
    }

    /**
     * 对象中数据获取
     */
    public static List<Map> getListMap(Object obj) {
        ObjectMapper objectMapper = new ObjectMapper();
        if (obj instanceof List) {
            return objectMapper.convertValue(obj, List.class);
        }
        return new ArrayList<>(1);
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    public static List<Map<String, Integer>> wordCount(String text, List<Pattern> patterns) {
        List<Map<String, Integer>> list = new ArrayList<>();
        patterns.forEach(x -> {
            // 创建 Map，用于存储单词出现次数
            Map<String, Integer> wordCounts = new HashMap<>();
            // 使用 Matcher 进行匹配，并统计每个单词出现的次数
            Matcher matcher = x.matcher(text);
            while (matcher.find()) {
                String word = matcher.group().toLowerCase();
                int count = wordCounts.getOrDefault(word, 0);
                wordCounts.put(word, count + 1);
            }
            list.add(wordCounts);
        });
        return list;
    }

    /**
     * 字符串中子串出现次数
     */
    public static int getMatches(String str, String substr) {
        int count = 0;
        int i = 0;
        while (str.indexOf(substr, i) != -1) {
            count++;
            i = str.indexOf(substr, i) + substr.length();
        }
        return count;
    }

    public static String existDataList(Object kwVideoUrl) {
        if (kwVideoUrl instanceof String) {
            return ((String) kwVideoUrl);
        } else if (kwVideoUrl instanceof List) {
            List<String> videoUrlList = (List<String>) kwVideoUrl;
            if (videoUrlList.size() > 0) {
                return (videoUrlList.get(0));
            }
        }
        return existData(kwVideoUrl);
    }
}
