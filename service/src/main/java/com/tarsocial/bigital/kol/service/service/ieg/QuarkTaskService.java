package com.tarsocial.bigital.kol.service.service.ieg;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.dto.QuarkTaskDto;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.QuarkPostDto;
import com.tarsocial.bigital.kol.common.domain.entity.Post;
import com.tarsocial.bigital.kol.common.domain.entity.QuarkUserEntity;
import com.tarsocial.bigital.kol.common.domain.entity.QuarkUserInfo;
import com.tarsocial.bigital.kol.common.domain.entity.guangnian.GamePostVo;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentIegUserLog;
import com.tarsocial.bigital.kol.common.domain.enums.PlatformEnum;
import com.tarsocial.bigital.kol.common.domain.enums.QuarkPlatform;
import com.tarsocial.bigital.kol.common.domain.response.SubTaskListResponse;
import com.tarsocial.bigital.kol.service.client.QuarkDataTaskSpi;
import com.tarsocial.bigital.kol.service.constants.Constants;
import com.tarsocial.bigital.kol.service.constants.TaskTypeEnum;
import com.tarsocial.bigital.kol.service.dto.QuarkTaskConditionDto;
import com.tarsocial.bigital.kol.service.service.DataCenterProcessService;
import com.tarsocial.bigital.kol.service.service.DataCenterTokenServiceImpl;
import com.tarsocial.bigital.kol.service.util.BotUtil;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.dag.task.model.enums.TaskStatusEnum;
import com.tarsocial.dag.task.model.request.SubTaskListRequest;
import com.tarsocial.dag.task.model.request.TaskCreateRequest;
import com.tarsocial.enums.NullColumnEnum;
import com.tarsocial.enums.OperationEnum;
import com.tarsocial.enums.OrderTypeEnum;
import com.tarsocial.request.CommonKeyAndValue;
import com.tarsocial.request.DocSourceV1Request;
import com.tarsocial.request.FilterRequest;
import com.tarsocial.response.DocSourceResponse;
import com.tarsocial.vo.FieldOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class QuarkTaskService {

    @Resource
    private DataCenterProcessService dataCenterProcessService;

    @Resource
    private DataCenterTokenServiceImpl dataCenterTokenService;

    @Resource
    private QuarkDataTaskSpi quarkDataTaskSpi;

    @Resource
    private ApplicationContext context;


    public Long createTask(TaskTypeEnum taskType, List<String> ids) {
        return createTask(taskType, ids, getTaskName(taskType, ids));
    }


    /**
     * 创建夸克任务
     *
     * @param taskType
     * @param ids
     * @return
     */
    public Long createTask(TaskTypeEnum taskType, List<String> ids, String taskName) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        TaskCreateRequest request = new TaskCreateRequest();
        request.setBizNo("2024TS-SL0107");
        request.setTaskName(taskName);
        request.setPlatformId(taskType.getPlatformId());
        request.setDetail(StringUtils.join(ids, "\n"));
        request.setTaskTypeId(taskType.getCode());
        log.info("taskCreate request:{}", JSONObject.toJSONString(request));
        Object o = null;
        try {
            o = quarkDataTaskSpi.taskCreate(request);
            log.info("quarkDataTaskSpi.taskCreate response: {}", JSON.toJSONString(o));
            return Long.parseLong(JSON.toJSONString(o));
        } catch (Exception e) {
            log.info("taskCreate error {} ,{}", taskType.getDesc(), JSONObject.toJSONString(o));
            BotUtil.send(context.getEnvironment().getActiveProfiles()[0] + ">> 夸克任务创建失败: \n" + e.getMessage());
            dataCenterTokenService.refreshToken();
            return null;
        }
    }


    public Long createDYKOLTask(TaskTypeEnum taskType, List<String> ids, String taskName, QuarkTaskConditionDto conditionDto) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        TaskCreateRequest request = new TaskCreateRequest();
        request.setBizNo("2024TS-SL0107");
        request.setTaskName(taskName);
        request.setPlatformId(taskType.getPlatformId());
        request.setDetail(StringUtils.join(ids, "\n"));
        request.setCondition(JSON.toJSONString(conditionDto));
        request.setTaskTypeId(taskType.getCode());
        log.info("taskCreate request:{}", JSONObject.toJSONString(request));
        Object o = null;
        try {
            o = quarkDataTaskSpi.taskCreate(request);
            log.info("quarkDataTaskSpi.taskCreate response: {}", JSON.toJSONString(o));
            return Long.parseLong(JSON.toJSONString(o));
        } catch (Exception e) {
            log.info("taskCreate error {} ,{}", taskType.getDesc(), JSONObject.toJSONString(o));
            BotUtil.send(context.getEnvironment().getActiveProfiles()[0] + ">> 夸克任务创建失败: \n" + e.getMessage());
            dataCenterTokenService.refreshToken();
            return null;
        }
    }


    /**
     * 查询夸克任务
     *
     * @param taskId
     * @return
     */
    public Map<String, QuarkTaskDto> queryTask(Long taskId) {
        //子任务
        Map<String, QuarkTaskDto> subTaskMap = new HashMap<>(1);

        SubTaskListRequest request = new SubTaskListRequest();
        request.setId(taskId);
        request.setKeyword("");
        request.setStatus(TaskStatusEnum.ALL);
        request.setPage(1);
        request.setSize(500);
        Object o = quarkDataTaskSpi.subTaskDetail(request);
        SubTaskListResponse page = BeanUtil.toBean(o, SubTaskListResponse.class);
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRows())) {
            return subTaskMap;
        }

        page.getRows().forEach(x -> {
            QuarkTaskDto dto = new QuarkTaskDto();
            dto.setStatus(x.getStatus());
            dto.setSubTaskId(x.getDetail());
            dto.setCreateTime(x.getCreateTime());
//                dto.setDetail(x.getSubTaskId());
            if (TaskStatusEnum.FAIL.name().equals(x.getStatus())) {
                dto.setErrorMsg(x.getCallBackMsg());
            }
            subTaskMap.put(x.getDetail(), dto);
        });

        return subTaskMap;
    }

    /**
     * 取消任务
     *
     * @param taskId
     */
    public void cancelTask(List<Long> taskId) {
        try {
            quarkDataTaskSpi.cancel(taskId);
        } catch (Exception ignored) {
            log.error("取消夸克任务失败 ->{}", ignored.getMessage(), ignored);
        }
    }


    /**
     * 查询夸克用户信息
     * /user  索引
     *
     * @param userIds
     * @param platform
     * @return
     */
    public List<QuarkUserInfo> pullUser(List<String> userIds, String platform) {
        DocSourceV1Request docSourceV1Request = doDealRequest(userIds, platform, 158883L);
        DocSourceResponse response = dataCenterProcessService.sourceV1(docSourceV1Request);
        List<QuarkUserInfo> list = Lists.newArrayList();
        response.getRows().stream().filter(x -> Objects.nonNull(x) && !CollectionUtils.isEmpty(x.getSource()))
                .forEach(x -> {
                    QuarkUserInfo userInfo = processUser(x.getSource());
                    if (Objects.nonNull(userInfo)) {
                        list.add(userInfo);
                    }
                });
        return list;
    }

    public List<QuarkUserEntity> queryUser(List<String> userIds, String platform) {
        DocSourceV1Request docSourceV1Request = doDealRequest(userIds, platform, 158883L);
        DocSourceResponse response = dataCenterProcessService.sourceV1(docSourceV1Request);
        List<QuarkUserEntity> list = Lists.newArrayList();
        response.getRows().stream().filter(x -> Objects.nonNull(x) && !CollectionUtils.isEmpty(x.getSource()))
                .forEach(x -> {
                    QuarkUserEntity userInfo = null;
                    try {
                        userInfo = JSON.parseObject(JSON.toJSONString(x.getSource()), QuarkUserEntity.class);
                    } catch (Exception ignored) {
                    }
                    if (Objects.nonNull(userInfo)) {
                        list.add(userInfo);
                    }
                });
        return list;
    }


    /**
     * 近3个月 商单贴
     *
     * @param userId
     * @param platform
     * @return
     */
    public List<QuarkPostDto> queryPost(List<String> userId, String platform) {
        DocSourceV1Request request = new DocSourceV1Request();
        request.setCount(1000);
        //空规则
        request.setMainPartyId(99611L);
        request.setFlatSource(true);
        request.setArrayFlatSource(true);
        request.setSourceFields(Lists.newArrayList("object_user", "date_publishedAt", "kw_id", "kw_platform", "bool_isPaid", "long_readCount", "kw_coverUrl", "tx_content", "kw_url", "long_duration", "long_repostsCount", "long_commentsCount", "long_likeCount", "long_collectCount", "tx_title", "long_coinCount", "long_danmuCount"));
        //43, 44, 45, 51, 53
        request.setPlatformIds(Arrays.asList(QuarkPlatform.getPlatformCodeByValue(platform)));
        request.setKeepAlive(1);
        request.setIndexType("post");


        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        // 将日期设置为当前日期
        calendar.setTime(new Date());
        // 将日期调整为一个月前
        calendar.add(Calendar.MONTH, -3);
        // 开始时间和结束时间
        request.setStartDate(DateUtil.getStartOfDay(calendar.getTime()));
        request.setEndDate(DateUtil.getEndOfDay(new Date()));

        //用户
        List<FilterRequest> filters = Lists.newArrayList();
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);

        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(220L);
        commonKeyAndValue.setValue(userId);
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.equals);
        filters.add(contentFilter);

        //排除评论
        FilterRequest contentFilter2 = new FilterRequest();
        contentFilter2.setFilter(true);
        contentFilter2.setAllowNull(NullColumnEnum.CONTAIN_NULL_COLUMN);

        List<CommonKeyAndValue> ruleIds2 = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue2 = new CommonKeyAndValue();
        commonKeyAndValue2.setId(312L);
        commonKeyAndValue2.setValue(Lists.newArrayList("评论"));
        ruleIds2.add(commonKeyAndValue2);
        contentFilter2.setRuleIds(ruleIds2);
        contentFilter2.setOperationEnum(OperationEnum.exclude);
        filters.add(contentFilter2);

        //商单
        FilterRequest contentFilter3 = new FilterRequest();
        contentFilter3.setFilter(true);

        List<CommonKeyAndValue> ruleIds3 = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue3 = new CommonKeyAndValue();
        commonKeyAndValue3.setId(397L);
        commonKeyAndValue3.setValue(Lists.newArrayList("true"));
        ruleIds3.add(commonKeyAndValue3);
        contentFilter3.setRuleIds(ruleIds3);
        contentFilter3.setOperationEnum(OperationEnum.equals);
        filters.add(contentFilter3);


        request.setFilter(filters);

        DocSourceResponse docSourceResponse = dataCenterProcessService.sourceV1(request);

        log.info("原始数据量， size--->{}", docSourceResponse.getRows().size());
        List<QuarkPostDto> list = new ArrayList<>(docSourceResponse.getRows().size());
        docSourceResponse.getRows().forEach(x -> {
            if (Objects.isNull(x) || CollectionUtils.isEmpty(x.getSource())) {
                return;
            }
            QuarkPostDto quarkPostDto = processPost(x.getSource());
            if (quarkPostDto != null) {
                list.add(quarkPostDto);
            }
        });
        return list;
    }

    private QuarkPostDto processPost(Map<String, Object> source) {
        QuarkPostDto quarkPostDto = new QuarkPostDto();
        Post post = JSON.parseObject(JSON.toJSONString(source), Post.class);
        Object userId = source.get("object_user.kw_userId");
        if (userId == null) {
            return null;
        }
        quarkPostDto.setUserId(userId.toString());
        quarkPostDto.setPublishedAt(post.getDatePublishedAt());
        quarkPostDto.setId(post.getId());
        quarkPostDto.setPlatform(post.getPlatform());
        quarkPostDto.setIsPaid(post.getIsPaid());
        quarkPostDto.setReadCount(post.getReadCount());
        quarkPostDto.setCoverUrl(post.getCoverUrl());
        quarkPostDto.setContent(post.getContent());
        quarkPostDto.setUrl(post.getUrl());
        quarkPostDto.setDuration(post.getDuration());
        quarkPostDto.setRepostsCount(post.getRepostsCount());
        quarkPostDto.setCommentsCount(post.getCommentsCount());
        quarkPostDto.setLikeCount(post.getLikeCount());
        quarkPostDto.setCollectCount(post.getCollectCount());
        quarkPostDto.setTitle(post.getTitle());
        quarkPostDto.setCoinCount(post.getCoinCount());
        quarkPostDto.setDanmuCount(post.getDanmuCount());
        return quarkPostDto;
    }


    private QuarkUserInfo processUser(Map<String, Object> source) {

        try {
            return JSON.parseObject(JSON.toJSONString(source), QuarkUserInfo.class);
        } catch (Exception e) {
            log.error("QuarkUserInfo processUser convert error ！！！ source ---> {}", JSON.toJSONString(source));
            return null;
        }
    }


    public DocSourceV1Request doDealRequest(List<String> userIds, String platform, Long mainId) {
        DocSourceV1Request request = new DocSourceV1Request();
        request.setCount(10000);
        request.setMainPartyId(mainId);

        // 需要展示字段
        request.setFlatSource(false);
        request.setArrayFlatSource(true);

        request.setSourceFields(Constants.filed);

        if (org.springframework.util.StringUtils.hasLength(platform)) {
            request.setPlatformIds(Collections.singletonList(Constants.QuarkPlatformType.getPlatformCodeByValue(platform)));
        } else {
            request.setPlatformIds(Constants.QuarkPlatformType.getAllPlatformCode());
        }
        // 过滤条件
        List<FilterRequest> filters = Lists.newArrayList();
        filters.add(getSimpleFilterUserIds(userIds));
        request.setFilter(filters);
        request.setKeepAlive(1);
        request.setIndexType("user");
        return request;
    }


    private FilterRequest getSimpleFilterUserIds(List<String> userIds) {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setAllowNull(null);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(446L);
        commonKeyAndValue.setValue(userIds);
        ruleIds.add(commonKeyAndValue);
        contentFilter.setRuleIds(ruleIds);
        contentFilter.setOperationEnum(OperationEnum.equals);
        return contentFilter;
    }

    public String getTaskName(TaskTypeEnum taskType, List<String> ids) {
        return taskType.getDesc() + "-" + ids.get(0);
    }
}
