package com.tarsocial.bigital.kol.service.client;

import com.tarsocial.config.TokenConfiguration;
import com.tarsocial.dag.framework.feign.config.DagFeignClientConfiguration;
import com.tarsocial.dag.wordcloud.spi.WordCloudSpi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "carslan-insight-word-cloud", url = "${quark.wordCloudUrl}", configuration = {TokenConfiguration.class, DagFeignClientConfiguration.class})
public interface QuarkWordCloudSpi extends WordCloudSpi {
}
