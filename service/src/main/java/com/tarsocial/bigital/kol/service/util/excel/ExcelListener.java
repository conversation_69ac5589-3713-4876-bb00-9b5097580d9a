package com.tarsocial.bigital.kol.service.util.excel;

/**
 * <AUTHOR>
 * @team C1
 * @owner z<PERSON><PERSON><PERSON>
 * @since 2021-12-21 11:06
 */

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 监听类，可以自定义
 *
 * <AUTHOR>
 * @Created 2019-7-18 18:01:53
 **/
@Component
@Slf4j
@Data
public class ExcelListener implements ReadListener<Object> {

    /**
     * 自定义用于暂时存储data。
     * 可以通过实例获取该值
     */
    private List list = new ArrayList<>();

    //解析的行数
    private Integer analysisCount = 0;

    /**
     * 分析一行时触发调用函数。(即每行数据解析的时候都会触发该方法)
     *
     * @param object          object
     * @param analysisContext 上下文是 Excel 阅读器的主要定位点。
     */
    @Override
    public void invoke(Object object, AnalysisContext analysisContext) {
        log.debug("解析到一条数据:{}", JSON.toJSONString(object));
        list.add(object);
        analysisCount++;
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        ReadListener.super.onException(exception, context);
    }

    @Override
    public void invokeHead(Map headMap, AnalysisContext context) {
        ReadListener.super.invokeHead(headMap, context);
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        ReadListener.super.extra(extra, context);
    }

    /**
     * 所有数据解析完后调用该方法
     *
     * @param analysisContext analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("共解析数据:{}", analysisCount);
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return ReadListener.super.hasNext(context);
    }
}
