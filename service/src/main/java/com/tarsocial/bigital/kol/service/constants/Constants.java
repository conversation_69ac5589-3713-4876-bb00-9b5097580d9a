package com.tarsocial.bigital.kol.service.constants;

import lombok.Getter;
import org.apache.commons.compress.utils.Lists;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024/4/12
 */
public interface Constants {

    String BUZZ = "buzz";
    String ALL = "all";
    String ENGAGEMENT = "engagement";

    String MONTH = "month";
    String WEEK = "week";
    String RULE = "rule";
    String KEYWORD = "keyword";
    final String INTERACTION = "long_interaction";
    final String LIKE_COUNT = "long_likeCount";

    List<String> filed = Arrays.asList(
            "kw_userId"
            , "kw_platform"
            , "kw_kolId"
            , "tkw_nickname"
            , "long_followersCount"
            , "kw_gender"
            , "object_star"
            , "kw_tags"
            , "kw_industryTags"
            , "object_liveStar"
            , "kw_secUid"
    );


    HashMap<String, String> GENDER = new HashMap<String, String>() {{
        put("1", "男");
        put("2", "女");
        put("3", "未知");
    }};

    HashMap<String, List<Pattern>> KOL_TAG = new HashMap<String, List<Pattern>>() {{
        put("Foodie", Arrays.asList(Pattern.compile("美食")));
        put("Parent-Child", Arrays.asList(Pattern.compile("母婴"), Pattern.compile("亲子")));
        put("Entertainment", Arrays.asList(Pattern.compile("剧情"), Pattern.compile("影视综咨询")));
        put("Lifestyle", Arrays.asList(Pattern.compile("生活记录"), Pattern.compile("生活Vlog")));
        put("Fashion", Arrays.asList(Pattern.compile("时尚")));
        put("Pet", Arrays.asList(Pattern.compile("宠物"), Pattern.compile("萌宠")));
        put("Agriculture", Arrays.asList(Pattern.compile("农产品"), Pattern.compile("三农")));
    }};

    HashMap<String, List<Pattern>> ONE_KEYWORD = new HashMap<String, List<Pattern>>() {{
        put("funplus", Arrays.asList(Pattern.compile("funplus"), Pattern.compile("fun plus")));
        put("趣加", Collections.singletonList(Pattern.compile("趣加")));
        put("好玩橙", Collections.singletonList(Pattern.compile("好玩橙")));
        put("光辇", Collections.singletonList(Pattern.compile("光辇")));
        put("制梦", Collections.singletonList(Pattern.compile("制梦")));
        put("曙光防线", Collections.singletonList(Pattern.compile("曙光防线")));
        put("代号：界", Arrays.asList(Pattern.compile("代号：界"), Pattern.compile("代号界")));
        put("制梦科技", Collections.singletonList(Pattern.compile("制梦科技")));
        put("King of avalon", Arrays.asList(Pattern.compile("King of avalon"), Pattern.compile("KingofAvalon")));
        put("state of survival", Arrays.asList(Pattern.compile("state of survival"), Pattern.compile("stateofsurvival")));
        put("sea of conquest", Arrays.asList(Pattern.compile("sea of conquest"), Pattern.compile("SeaofConquest")));
        put("DCW", Collections.singletonList(Pattern.compile("DCW")));
        put("ldX", Collections.singletonList(Pattern.compile("ldX")));
        put("钟英武", Collections.singletonList(Pattern.compile("钟英武")));
        put("阿瓦隆之王", Collections.singletonList(Pattern.compile("阿瓦隆之王")));
        put("火器文明", Collections.singletonList(Pattern.compile("火器文明")));
        put("GOG", Collections.singletonList(Pattern.compile("GOG")));
        put("SSCN", Collections.singletonList(Pattern.compile("SSCN")));
        put("Deep", Collections.singletonList(Pattern.compile("Deep")));
        put("迷你王国：求生", Arrays.asList(Pattern.compile("迷你王国：求生"), Pattern.compile("迷你王国:求生"), Pattern.compile("迷你王国求生")));
        put("风暴航线", Collections.singletonList(Pattern.compile("风暴航线")));
        put("王者之海", Collections.singletonList(Pattern.compile("王者之海")));
        put("砰砰船长", Collections.singletonList(Pattern.compile("砰砰船长")));
        put("Antia", Collections.singletonList(Pattern.compile("Antia")));
    }};

    HashMap<String, List<Pattern>> TWO_KEYWORD = new HashMap<String, List<Pattern>>() {{
        put("绝区零", Collections.singletonList(Pattern.compile("绝区零")));
        put("鸣潮", Collections.singletonList(Pattern.compile("鸣潮")));
        put("物华弥新", Collections.singletonList(Pattern.compile("物华弥新")));
        put("新月同行", Collections.singletonList(Pattern.compile("新月同行")));
        put("燕云十六声", Collections.singletonList(Pattern.compile("燕云十六声")));
        put("DNF手游", Arrays.asList(Pattern.compile("DNF手游"), Pattern.compile("dnf 手游")));
        put("射雕", Collections.singletonList(Pattern.compile("射雕")));
        put("流浪地球", Collections.singletonList(Pattern.compile("流浪地球")));
        put("无期迷途", Collections.singletonList(Pattern.compile("无期迷途")));
        put("无尽冬日", Collections.singletonList(Pattern.compile("无尽冬日")));
        put("三国：谋定天下", Collections.singletonList(Pattern.compile("谋定天下")));
    }};

    HashMap<String, List<Pattern>> THREE_KEYWORD = new HashMap<String, List<Pattern>>() {{
        put("龟笼潮", Collections.singletonList(Pattern.compile("龟笼潮")));
        put("龟龙巢", Collections.singletonList(Pattern.compile("龟龙巢")));
        put("笼潮", Collections.singletonList(Pattern.compile("笼潮")));
        put("归龙潮", Collections.singletonList(Pattern.compile("归龙潮")));
        put("笼巢", Collections.singletonList(Pattern.compile("笼巢")));
        put("暗号瞳", Collections.singletonList(Pattern.compile("暗号瞳")));
    }};

    HashMap<String, List<Pattern>> FOUR_KEYWORD = new HashMap<String, List<Pattern>>() {{
        put("归龙潮", Collections.singletonList(Pattern.compile("归龙潮")));
        put("龟笼潮", Collections.singletonList(Pattern.compile("龟笼潮")));
        put("龟龙巢", Collections.singletonList(Pattern.compile("龟龙巢")));
        put("笼潮", Collections.singletonList(Pattern.compile("笼潮")));
        put("媚宅", Collections.singletonList(Pattern.compile("媚宅")));
        put("运营", Collections.singletonList(Pattern.compile("运营")));
        put("胸", Collections.singletonList(Pattern.compile("胸")));
        put("xxn", Collections.singletonList(Pattern.compile("xxn")));
        put("褒姒", Collections.singletonList(Pattern.compile("褒姒")));
        put("上线", Collections.singletonList(Pattern.compile("上线")));
        put("广进", Collections.singletonList(Pattern.compile("广进")));
        put("大礼包", Collections.singletonList(Pattern.compile("大礼包")));
        put("zng", Collections.singletonList(Pattern.compile("zng")));
        put("裁员", Collections.singletonList(Pattern.compile("裁员")));
        put("缅北", Collections.singletonList(Pattern.compile("缅北")));
        put("离职", Collections.singletonList(Pattern.compile("离职")));
        put("媚女", Collections.singletonList(Pattern.compile("媚女")));
        put("做二次元游戏的我遇到了三次元的铁壁", Collections.singletonList(Pattern.compile("做二次元游戏的我遇到了三次元的铁壁")));
        put("笼巢", Collections.singletonList(Pattern.compile("笼巢")));
        put("暗号瞳", Collections.singletonList(Pattern.compile("暗号瞳")));
    }};

    HashMap<String, String> PLATFORM_MAP = new HashMap<String, String>() {{
        put("Red", "xiaohongshu");
        put("Weibo", "weibo");
        put("Douyin", "douyin");
        put("all", "all");
    }};

    enum PlatformType {
        /**
         * PlatformType
         */
        WEIBO(43L, "weibo", "微博"),
        TIKTOK(44L, "douyin", "抖音"),
        RED_BOOK(45L, "red", "小红书"),
        BBS(46L, "luntan", "论坛"),
        WEB(47L, "luntan", "网页"),
        HEADLINE(48L, "douyin", "头条"),
        WECHAT(49L, "red", "微信"),
        VIDEO(50L, "douyin", "视频号"),
        BILI(51L, "red", "bilibili"),
        ZHIHU(52L, "douyin", "知乎"),
        KUAISHOU(53L, "red", "快手"),
        ;

        Long code;
        String value;
        String display;

        PlatformType(Long code, String value, String display) {
            this.code = code;
            this.value = value;
            this.display = display;
        }

        public Long code() {
            return this.code;
        }

        public String value() {
            return this.value;
        }

        public String display() {
            return this.display;
        }

        public static List<Long> getAllPlatformCode() {
            List<Long> list = Lists.newArrayList();
            for (PlatformType platformType : PlatformType.values()) {
                list.add(platformType.code());
            }
            return list;
        }

        public static Long getPlatformCodeByValue(String value) {
            for (PlatformType platformType : PlatformType.values()) {
                if (platformType.value().equalsIgnoreCase(value)) {
                    return platformType.code();
                }
            }
            return null;
        }
    }


    @Getter
    enum QuarkPlatformType {
        /**
         * PlatformType
         */
        WEIBO(43L, "weibo", "微博"),
        DOUYIN(44L, "douyin", "抖音"),
        XIAOHONGSHU(45L, "xiaohongshu", "小红书"),
        BBS(46L, "bbs", "论坛"),
        WEB(47L, "web", "网页"),
        TOUTIAO(48L, "toutiao", "头条"),
        WEIXIN(49L, "weixin", "微信"),
        SHIPINHAO(50L, "shipinhao", "视频号"),
        BILIBILI(51L, "bilibili", "bilibili"),
        ZHIHU(52L, "zhihu", "知乎"),
        KUAISHOU(53L, "kuaishou", "快手"),
        EC(54L, "ec", "电商评论"),
        TMALL(55L, "tmall", "天猫"),
        JD(56L, "jd", "京东"),
        APP(57L, "app", "APP"),
        ;

        Long code;
        String value;
        String display;

        QuarkPlatformType(Long code, String value, String display) {
            this.code = code;
            this.value = value;
            this.display = display;
        }

        public Long code() {
            return this.code;
        }

        public String value() {
            return this.value;
        }

        public String display() {
            return this.display;
        }

        public static List<Long> getAllPlatformCode() {
            List<Long> list = Lists.newArrayList();
            for (QuarkPlatformType platformType : QuarkPlatformType.values()) {
                list.add(platformType.code());
            }
            return list;
        }

        public static List<String> getAllPlatformValue() {
            List<String> list = Lists.newArrayList();
            for (QuarkPlatformType platformType : QuarkPlatformType.values()) {
                list.add(platformType.getValue());
            }
            return list;
        }

        public static Long getPlatformCodeByValue(String value) {
            for (QuarkPlatformType platformType : QuarkPlatformType.values()) {
                if ("red".equalsIgnoreCase(value)) {
                    return XIAOHONGSHU.getCode();
                }
                if (platformType.value().equalsIgnoreCase(value)) {
                    return platformType.code();
                }
            }
            return null;
        }

        public static List<Long> getCodeListByValue(String value) {

            if ("other".equals(value) || "others".equals(value)) {
                List<Long> code = QuarkPlatformType.getAllPlatformCode();
                code.remove(QuarkPlatformType.WEIBO.getCode());
                code.remove(QuarkPlatformType.DOUYIN.getCode());
                code.remove(QuarkPlatformType.XIAOHONGSHU.getCode());
                return code;
            }

            if (StringUtils.hasLength(value) && "redbook".equals(value)) {
                value = QuarkPlatformType.XIAOHONGSHU.getValue();
            }

            for (QuarkPlatformType platformType : QuarkPlatformType.values()) {
                if (platformType.value().equalsIgnoreCase(value)) {
                    return Collections.singletonList(platformType.code());
                }
            }
            return QuarkPlatformType.getAllPlatformCode();
        }
    }

    @Getter
    enum QuarkFieldSourceType {
        OBJECT_USER_TKW_NICKNAME("object_user.tkw_nickname", "用户昵称"),
        OBJECT_USER_TX_DESCRIPTION("object_user.tx_description", "用户个人描述"),
        TX_TITLE("tx_title", "标题"),
        TX_CONTENT("tx_content", "正文"),
        TX_OCRCOVER("tx_ocrCover", "封面OCR"),
        TX_ASR("tx_asr", "语音识别内容"),
        TX_OCR("tx_ocr", "内容OCR"),
        OBJECT_PROMOTIONS_TX_OCRCOVER("object_promotions.tx_title", "购物车标题"),
        OBJECT_ORIGIN_TX_OCRCOVER("object_origin.tx_content.tx_title", "原帖内容"),
        ;
        String name;
        String cnName;

        QuarkFieldSourceType(String name, String cnName) {
            this.name = name;
            this.cnName = cnName;
        }

        public void setName(String name) {
            this.name = name;
        }

        public void setCnName(String cnName) {
            this.cnName = cnName;
        }

        public static List<String> getAllFieldSourceName() {
            List<String> list = Lists.newArrayList();
            for (QuarkFieldSourceType fieldSourceType : QuarkFieldSourceType.values()) {
                list.add(fieldSourceType.getName());
            }
            return list;
        }
    }


    enum BrandType {
        /**
         * BrandType
         */
        TIKTOK(1L, "douyin", "抖音"),
        RED_BOOK(2L, "red", "小红书"),
        KHC(4L, "khc", "卡夫亨氏"),
        ALI(3L, "ali tmall", "阿里"),
        ;

        Long code;
        String value;
        String display;

        BrandType(Long code, String value, String display) {
            this.code = code;
            this.value = value;
            this.display = display;
        }

        public Long code() {
            return this.code;
        }

        public String value() {
            return this.value;
        }

        public String display() {
            return this.display;
        }
    }


    enum StarChartType {
        /**
         * 星图指标类型
         */
        COOPERATE(1, "cooperateIndex", "合作指数"),
        CP(2, "cpIndex", "性价比指数"),
        LINK_CONVERT(3, "linkConvertIndex", "转化指数"),
        LINK_SHOPPING(4, "linkShoppingIndex", "种草指数"),
        LINK_SPREAD(5, "linkSpreadIndex", "传播指数"),
        LINK_STAR(6, "linkStarIndex", "星图指数"),
        ;

        Integer code;
        String value;
        String display;

        StarChartType(Integer code, String value, String display) {
            this.code = code;
            this.value = value;
            this.display = display;
        }

        public Integer code() {
            return this.code;
        }

        public String value() {
            return this.value;
        }

        public String display() {
            return this.display;
        }
    }

    enum FansPortrayalType {
        /**
         * 星图指标类型
         */
        GROUP_PEOPLE(1024, "distribution_list", "八大人群分布"),
        VIEW(512, "distribution_list", "观看频率分布"),
        AGE(2, "distribution_list", "年龄分布"),
        PROVINCE(4, "distribution_list", "省份分布"),
        CITY_GRADE(32, "distribution_list", "城市等级分布"),
        GENDER(1, "distribution_list", "性别分布"),
        INTERESTS(64, "distribution_list", "兴趣分布"),
        CITY(256, "distribution_list", "城市分布"),
        DEVICES(8, "distribution_list", "设备品牌分布"),
        CATEGORY(2048, "distribution_list", "消费品类分布"),
        PRICE(4096, "distribution_list", "客单价分布"),
        ;

        Integer type;
        String value;
        String display;

        FansPortrayalType(Integer type, String value, String display) {
            this.type = type;
            this.value = value;
            this.display = display;
        }

        public Integer type() {
            return this.type;
        }

        public String value() {
            return this.value;
        }

        public String display() {
            return this.display;
        }
    }

    enum LandingType {
        /**
         * 抖音落地页 推广目的
         */
        SALE_LEAD(1, "销售线索收集"),
        APPLICATION(3, "应用推广"),
        FAST_APPLICATION(10, "快应用"),
        PRODUCT_CATALOG(5, "商品目录推广"),
        LIVE_ROOM(9, "直播间推广"),
        STORE(6, "门店推广"),
        TIKTOK_SIGNAL(7, "抖音号推广"),
        ELECTRIC_SHOP(8, "电商店铺推广"),
        ;

        Integer code;
        String value;

        LandingType(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        public Integer getCode() {
            return this.code;
        }

        public String getValue() {
            return this.value;
        }

        public static String getValueByCode(Integer code) {
            for (LandingType landingType : LandingType.values()) {
                if (landingType.getCode().equals(code)) {
                    return landingType.getValue();
                }
            }
            return null;
        }

        /**
         * @return
         * @Description 将枚举转换成Map对象方便取值 - 固定类型- 仅为了提供思路
         * <AUTHOR>
         * @date 2021/12/30
         */
        public static Map<String, Integer> convertToMap() {
            Map<String, Integer> mapResult = new HashMap<>();

            Arrays.stream(LandingType.values()).forEach(type -> {
                mapResult.put(type.getValue(), type.getCode());
            });

            return mapResult;
        }
    }


    enum RuleConfigType {
        /**
         * 夸克词包规则
         */
        EMOTE_TAG_QUARK("Consumer Lifestyle tag query", "情绪词帖子标签词包 quark"),
        EMOTE_TAG_MATCH("Consumer Lifestyle tag match", "情绪词帖子标签词包 正则"),
        VIRAL_FOODIE_CONTENT_QUARK("Content Inspiration Viral Foodie Content Query", "标签词包 quark"),
        VIRAL_ACTIVATION_QUERY("Content Inspiration Viral Activation Query", "关注品牌 quark"),
        VIRAL_ACTIVATION_QUERY_THEME("Content Inspiration Viral Activation Theme Query", "关注活动 quark"),
        VIRAL_FOODIE_CONTENT_MARCH("Content Inspiration Viral Foodie Content Match", "标签词包 正则"),
        VIRAL_ACTIVATION_MATCH("Content Inspiration Viral Activation Match", "关注品牌 正则"),
        VIRAL_ACTIVATION_MATCH_THEME("Content Inspiration Viral Activation Theme Match", "关注活动 正则"),
        EMOTE_KEYWORD("Consumer Lifestyle emote query", "帖子情绪词"),
        ;

        String key;

        String desc;

        RuleConfigType(String key, String desc) {
            this.key = key;
            this.desc = desc;
        }

        public String getKey() {
            return this.key;
        }

        public String getDesc() {
            return this.desc;
        }

    }

    enum ThemeType {
        /**
         * 主题类型
         */
        AR(1, "AR", "AR"),
        AI(2, "AI", "AI"),
        SUPER_BRAND_DAY(3, "超级品牌日", "Super Brand Day"),
        POP_UP(4, "快闪", "Pop-up"),
        ESG(5, "ESG", "ESG"),
        CAMPING(6, "露营", "Camping"),
        BIZARRE(7, "市集", "Bizarre"),
        CROSSOVER(8, "联名", "Crossover"),
        ;

        Integer code;
        String value;
        String translate;

        ThemeType(Integer code, String value, String translate) {
            this.code = code;
            this.value = value;
            this.translate = translate;
        }

        public Integer code() {
            return this.code;
        }

        public String value() {
            return this.value;
        }

        public String translate() {
            return this.translate;
        }

        public static String getTranslateByValue(String value) {
            for (ThemeType themeType : ThemeType.values()) {
                if (themeType.value().equals(value)) {
                    return themeType.translate();
                }
            }
            return null;
        }
    }

    enum FoodieType {
        /**
         * 主题类型
         */
        FRIED_STEAK(1, "煎牛排", "Fried steak"),
        FRIED_EGG(2, "炸鸡蛋", "Fried egg"),
        LOW_FAT_CHICKEN_BREAST(3, "低脂鸡胸肉", "Low fat chicken breast"),
        HOMEMADE_SANDWICH(4, "自制三明治", "Homemade sandwich"),
        PASTA(5, "意面", "Pasta"),
        HOT_AND_SOUR_POWDER(6, "酸辣粉", "Hot and sour powder"),
        SNAIL_POWDER(7, "螺狮粉", "Snail powder"),
        STEAMED_RICE(8, "煲仔饭", "Steamed rice"),
        COARSE_GRAIN(9, "粗粮", "Coarse grain"),
        MIXED_NOODLES(10, "拌面", "Mixed noodles"),
        SALAD(11, "沙拉", "Salad"),
        BRAISED_VEGETABLE(12, "冒菜", "Braised vegetable"),
        MALATANG(13, "麻辣烫", "Malatang"),
        XINJIANG_FRIED_RICE_NOODLES(14, "新疆炒米粉", "Xinjiang Fried Rice Noodles"),
        SPICY_FRAGRANT_POT(15, "麻辣香锅", "Spicy fragrant pot"),
        DRUNKEN_CRAB(16, "醉蟹", "Drunken crab"),
        DUMPLINGS(17, "水饺", "Dumplings"),
        SHRIMP(18, "虾", "Shrimp"),
        BARBECUE(19, "烧烤", "Barbecue"),
        HOT_POT(20, "火锅", "Hot pot"),
        AIR_FRYER_RECIPE(21, "空气炸锅食谱", "Air Fryer Recipes"),
        BOILED_VEGETABLE(22, "水煮菜", "Boiled vegetable"),
        ;

        Integer code;
        String chinaValue;
        String translate;

        FoodieType(Integer code, String chinaValue, String translate) {
            this.code = code;
            this.chinaValue = chinaValue;
            this.translate = translate;
        }

        public Integer code() {
            return this.code;
        }

        public String chinaValue() {
            return this.chinaValue;
        }

        public String translate() {
            return this.translate;
        }

        public static String getTranslateByValue(String chinaValue) {
            for (FoodieType themeType : FoodieType.values()) {
                if (themeType.chinaValue.equals(chinaValue)) {
                    return themeType.translate();
                }
            }
            return null;
        }
    }

    @Getter
    enum LifeStyleType {
        /**
         * 主题类型
         */
        HOT_FOOD("Hot food", "爆款美食"),
        GYM("Gym", "体育舞蹈"),
        WORKPLACE("Workplace", "工作职场"),
        LIFE_SHARING("Life sharing", "生活碎片"),
        TRAVEL("Travel", "旅行户外"),
        ENTERTAINMENT("Entertainment", "影视综艺"),
        INTEREST("Interest", "兴趣二次元"),
        IN_SEASON("In Season", "时令节气"),
        PARENT_CHILD("Parent-child", "母婴亲子"),
        REDBOOK_SELECTION("Redbook selection", "红薯优选"),
        ;

        String key;
        String value;

        LifeStyleType(String key, String value) {
            this.value = value;
            this.key = key;
        }

        public static String getKeyByValue(String value) {
            for (LifeStyleType type : LifeStyleType.values()) {
                if (type.getValue().equals(value)) {
                    return type.getKey();
                }
            }
            return null;
        }
    }


    enum KwType {
        /**
         * 落地页类型
         */

    }

    @Getter
    enum OperationType {
        INSERT(1, "新增"),
        BATCH_INSERT(2, "批量新增"),
        DELETE(3, "删除"),
        BATCH_DELETE(4, "批量删除"),
        UPDATE(5, "编辑"),
        RECOVER(6, "恢复"),
        DELETE_CATEGORY(7, "删除品类"),
        UPDATE_CATEGORY(8, "编辑品类"),
        INSERT_CATEGORY(9, "新增品类"),
        INSERT_THEME(10, "新增主题"),
        ;

        Integer code;
        String value;

        OperationType(Integer code, String value) {
            this.value = value;
            this.code = code;
        }
    }

    @Getter
    enum PageType {
        CONSUMER_LIFESTYLE(1, "Consumer Lifestyle", "khc_consumer_post"),
        REDBOOK_SELECTION(2, "Redbook Selection", "khc_consumer_post"),
        ACTIVATION_THEME(3, "Activation By Theme", "khc_viral_activation"),
        TIKTOK_LANDING(4, "Activation By Tiktok-Landing", "khc_viral_landing"),
        TIKTOK_HOT_TOPIC(5, "Activation By Tiktok-Hot Topic", "khc_viral_hot_topic"),
        VIRAL_FOODIE(6, "Viral Foodie Content", "khc_viral_foodie_content"),
        LIVESTREAMING_THEME(7, "Livestreaming Theme", "khc_livestreaming_theme"),
        CONTENT_WALL(8, "KHC Content Wall", "khc_content_wall"),
        DIGITAL_INTERACTION(9, "Digital Interaction", "khc_digital_interaction"),
        RECYCLED(10, "回收站", ""),
        KHC_SOCIAL_CAMPAIGN_SUMMARY(11, "Social Campaign Summary", "khc_social_campaign"),
        ;

        Integer code;
        String value;
        String table;

        PageType(Integer code, String value, String table) {
            this.value = value;
            this.code = code;
            this.table = table;
        }

        public static String getTableByValue(String value) {
            for (PageType pageType : PageType.values()) {
                if (pageType.getValue().equals(value)) {
                    return pageType.getTable();
                }
            }
            return "";
        }
    }

    /**
     * 夸克查询字段枚举
     */
    @Getter
    enum QuarkConditionField {
        REPOSTS_COUNT(287L, "long_repostsCount", 1, "转发量求和"),
        COMMENTS_COUNT(288L, "long_commentsCount", 1, "评论量求和"),
        LIKE_COUNT(289L, "long_likeCount", 1, "点赞量求和"),
        COLLECT_COUNT(290L, "long_collectCount", 1, "收藏数求和"),
        ;

        Long fieldId;
        String value;
        Integer type;
        String desc;

        QuarkConditionField(Long fieldId, String value, Integer type, String desc) {
            this.fieldId = fieldId;
            this.type = type;
            this.value = value;
            this.desc = desc;
        }

        public static Integer getTypeById(Long fieldId) {
            for (QuarkConditionField field : QuarkConditionField.values()) {
                if (field.getFieldId().equals(fieldId)) {
                    return field.getType();
                }
            }
            return null;
        }

    }

    /**
     * 夸克筛选条件枚举
     */
    @Getter
    enum ConditionOperator {
        EQUALS(0, "等于"),
        NOT_EQUAL(1, "不等于"),
        EXIST(2, "存在"),
        NOT_EXIST(3, "不存在"),
        GREATER_AND_EQUAL(4, "大于等于"),
        GREATER(5, "大于"),
        LESS_AND_EQUAL(6, "小于等于"),
        LESS(7, "小于"),
        CONTAIN(8, "包含"),
        EXCLUDE(9, "排除"),
        ;

        Integer code;
        String label;

        ConditionOperator(Integer code, String label) {
            this.code = code;
            this.label = label;
        }
    }


    /**
     * 夸克筛选条件枚举
     */
    @Getter
    enum CampaignCategoryBrand {
        DRESSING_DDF(1, "salad dressing", "大多福"),
        DRESSING_QB(2, "salad dressing", "丘比"),
        DRESSING_BL(3, "salad dressing", "百利"),
        DRESSING_VE(4, "salad dressing", "Vepiaopiao"),
        DRESSING_FLS(5, "salad dressing", "菲力斯"),
        DRESSING_HLM(6, "salad dressing", "好乐门"),
        DRESSING_GL(7, "salad dressing", "冠利"),
        DRESSING_QH(8, "salad dressing", "千禾"),
        DRESSING_JMH(9, "salad dressing", "今明后"),
        DRESSING_LH(10, "salad dressing", "巷子里的灵魂"),
        DRESSING_HS(11, "salad dressing", "亨氏"),
        ADD_WSD(12, "salad Soy sauce 0 add", "味事达"),
        ADD_XH(13, "salad Soy sauce 0 add", "欣和"),
        ADD_HT(14, "salad Soy sauce 0 add", "海天"),
        ADD_SXX(15, "salad Soy sauce 0 add", "松鲜鲜"),
        ADD_CB(16, "salad Soy sauce 0 add", "厨邦"),
        ADD_LJJ(17, "salad Soy sauce 0 add", "李锦记"),
        ADD_HJ(18, "salad Soy sauce 0 add", "好记"),
        ADD_JJ(19, "salad Soy sauce 0 add", "加加"),
        ADD_ZB(20, "salad Soy sauce 0 add", "中坝"),
        ADD_QH(21, "salad Soy sauce 0 add", "千禾"),
        ADD_TTL(22, "salad Soy sauce 0 add", "太太乐"),
        ADD_JL(23, "salad Soy sauce 0 add", "家乐"),
        ORGANIC_WSD(24, "Organic Soy sauce", "味事达"),
        ORGANIC_XH(25, "Organic Soy sauce", "欣和"),
        ORGANIC_HT(26, "Organic Soy sauce", "海天"),
        ORGANIC_SXX(27, "Organic Soy sauce", "松鲜鲜"),
        ORGANIC_CB(28, "Organic Soy sauce", "厨邦"),
        ORGANIC_LJJ(29, "Organic Soy sauce", "李锦记"),
        ORGANIC_HJ(30, "Organic Soy sauce", "好记"),
        ORGANIC_ZB(31, "Organic Soy sauce", "中坝"),
        ORGANIC_QH(32, "Organic Soy sauce", "千禾"),
        ORGANIC_TTL(33, "Organic Soy sauce", "太太乐"),
        ORGANIC_JL(34, "Organic Soy sauce", "家乐"),
        ;

        Integer code;
        String category;
        String brand;

        CampaignCategoryBrand(Integer code, String category, String brand) {
            this.code = code;
            this.category = category;
            this.brand = brand;
        }

    }

}
