package com.tarsocial.bigital.kol.service.controller.tencent.ieg;

import com.tarsocial.bigital.kol.common.domain.entity.QuarkUserInfo;

import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.IegTaskCreateRequest;
import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.common.domain.response.ieg.LiveTaskCreateResponse;
import com.tarsocial.bigital.kol.common.domain.response.ieg.TaskUserDetailResponse;
import com.tarsocial.bigital.kol.service.service.ieg.IegProcessService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;



@RestController
@RequestMapping("/api/ieg/live")
public class LiveDataController {

    @Resource
    private IegProcessService iegProcessService;


    @PostMapping("/task/create")
    public BaseResponse<LiveTaskCreateResponse> iegTaskCreate(@RequestBody IegTaskCreateRequest request) {
        return new BaseResponse<>( iegProcessService.addAllTask(request.getIds(), request.getPlatform()));
    }

    @PostMapping("/task/detail")
    public BaseResponse<TaskUserDetailResponse> taskDetail(@RequestBody @Validated IegTaskCreateRequest request){
        return new BaseResponse<>(iegProcessService.taskDetail(request));
    }

    @PostMapping("/query/user")
    public BaseResponse<List<QuarkUserInfo>> pushIegUser(@RequestBody IegTaskCreateRequest request) {
        return new BaseResponse<>(iegProcessService.pushIegUser(request));
    }

    @PostMapping("/task/retry")
    public BaseResponse<String> taskRetry() {
        iegProcessService.taskRetry();
        return new BaseResponse<>("ok");
    }
}
