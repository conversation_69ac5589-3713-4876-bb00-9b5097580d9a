package com.tarsocial.bigital.kol.service.job;

import com.tarsocial.bigital.kol.service.service.guangnian.GameDataProcessService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.bigital.kol.service.util.email.MailUtil;
import com.tarsocial.bigital.kol.service.util.email.SendMailBean;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.Executor;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RefreshToken {

    @XxlJob("refreshToken")
    public void refreshToken() {
        log.info("zzzzzz ----> 测试一下ffff");
    }
}
