package com.tarsocial.bigital.kol.service.advice;

import com.tarsocial.bigital.kol.common.domain.response.HairBaseResponse;
import com.tarsocial.bigital.kol.common.domain.response.ResponseCode;
import com.tarsocial.bigital.kol.service.haierController.hair.HaiErController;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import javax.xml.bind.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.postgresql.util.PSQLException;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @title HairControllerAdvice
 * @date 2025/1/16 14:58
 * @description 「HairControllerAdvice」
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
@RestControllerAdvice(basePackages = "com.tarsocial.bigital.kol.service.haierController.hair")
public class HairControllerAdvice {
    @ExceptionHandler(Exception.class)
    public HairBaseResponse<String> handle(Exception e) {
        if ("org.apache.catalina.connector.ClientAbortException".equals(e.getClass().getName())
            || "java.io.IOException: Broken pipe".equals(e.getClass().getName())) {
            log.error("found ClientAbortException, ignore it.");
            return null;
        }
        log.error("exception", e);
        return HairBaseResponse.error(e.getMessage());
    }


    @ExceptionHandler(BusinessException.class)
    public HairBaseResponse<String> handle(BusinessException e) {
        log.error("business exception", e);
        return new HairBaseResponse<>(-1, e.getMessage());
    }

    @ExceptionHandler(ValidationException.class)
    public HairBaseResponse<String> handle(ValidationException e) {
        log.error("business exception", e);
        return new HairBaseResponse<>(-1, ResponseCode.VALIDATE_ERROR.getMessage());
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public HairBaseResponse<String> handle(MissingServletRequestParameterException e) {
        log.error("business exception", e);
        return new HairBaseResponse<>(-1, ResponseCode.PARAM_ERROR.getMessage());
    }

    @ExceptionHandler(PSQLException.class)
    public HairBaseResponse<String> handle(PSQLException e) {
        log.error("business exception", e);
        return new HairBaseResponse<>(-1, ResponseCode.PARAM_ERROR.getMessage());
    }
}
