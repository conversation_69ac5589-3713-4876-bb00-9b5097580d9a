package com.tarsocial.bigital.kol.service.config.redis;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "spring.redis")
@Configuration
@Data
public class RedisProperties {

    private String host;
    private int port;
    private String password;
    private String username;
    private int database;
}
