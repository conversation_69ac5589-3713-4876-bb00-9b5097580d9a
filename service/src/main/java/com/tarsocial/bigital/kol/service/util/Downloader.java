package com.tarsocial.bigital.kol.service.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.convert.ListConverter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MimeTypeUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> href="mailto:<EMAIL>">ely</a>
 * @since 2022/6/21
 */
public class Downloader {

    private static final Logger logger = LoggerFactory.getLogger(Downloader.class);

    /**
     * Example:
     * <p>
     * data class Order(
     *
     * @ExcelProperty("订单号", index = 1)
     * val orderNo: String,
     * @ExcelProperty("用户名", index = 0)
     * val userName: String
     * )
     * @RestController
     * @RequestMapping("/api/order") class OrderController {
     * @GetMapping("download")
     * @ApiOperation(nickname = "download", value = "Download Order")
     * fun download(response: HttpServletResponse) {
     * DownloadUtils.downloadExcel(response, "订单数据", listOf(Order("NO0001", "zhangsan"), Order("NO0002", "lisi")))
     * }
     * }
     */
    public static <E> void downloadExcel(HttpServletResponse response, String fileName, List<E> list) {
        downloadExcel(response, fileName, "sheet1", list, list.get(0).getClass());
    }

    public static <E> void downloadExcel(HttpServletResponse response, String fileName, String sheetName, List<E> list, Class<?> clazz) {
        try {
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replace("\\+", "%20");

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", String.format("attachment;filename*=utf-8''%s.xlsx", encodedFileName));

            if (list.isEmpty()) {
                EasyExcel.write(response.getOutputStream()).sheet(sheetName).doWrite(list);
                return;
            }

            EasyExcel.write(response.getOutputStream(), clazz).sheet(sheetName).doWrite(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public static void resetCellMaxTextLength() {
        SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
        if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
            Field field;
            try {
                field = excel2007.getClass().getDeclaredField("_maxTextLength");
                field.setAccessible(true);
                field.set(excel2007, Integer.MAX_VALUE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void downloadCSV(HttpServletResponse response, String fileName, List<Map<String, Object>> list) {
        response.setContentType(MimeTypeUtils.TEXT_PLAIN_VALUE);
        try {
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replace("\\+", "%20");
            response.setHeader("Content-disposition", String.format("attachment;filename=%s.csv", encodedFileName));
            OutputStream outputStream = response.getOutputStream();
            CSVPrinter csvPrinter = new CSVPrinter(
                    new OutputStreamWriter(outputStream, StandardCharsets.UTF_8), CSVFormat.EXCEL);

            csvPrinter.printRecord(list.get(0).keySet());
            for (Map map : list) {
                csvPrinter.printRecord(map.values());
            }
            csvPrinter.flush();
            csvPrinter.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 从网络Url中下载文件
     *
     * @param urlStr url的路径
     * @throws IOException
     */
    public static String downLoadByUrl(String urlStr, String savePath, String fileName) {

        try {
            fileName = fileName == null ? getFileName(urlStr) : fileName;
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置超时间为5秒
            conn.setConnectTimeout(5 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            //得到输入流
            InputStream inputStream = conn.getInputStream();
            //获取自己数组
            byte[] getData = readInputStream(inputStream);
            //文件保存位置
            File saveDir = new File(savePath);
            // 没有就创建该文件
            if (!saveDir.exists()) {
                saveDir.mkdir();
            }
            File file = new File(saveDir + File.separator + fileName);
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(getData);

            fos.close();
            inputStream.close();
            System.out.println("the file: " + url + " download success");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return savePath + File.separator + fileName;

    }

    /**
     * 从输入流中获取字节数组
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    private static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[4 * 1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

    /**
     * 从src文件路径获取文件名
     *
     * @param srcRealPath src文件路径
     * @return 文件名
     */
    private static String getFileName(String srcRealPath) {
        return StringUtils.substringAfterLast(srcRealPath, "/");
    }

    /**
     * 将 Excel 写入 ByteArrayOutputStream
     */
    public static <E> byte[] excelToByteArray(List<E> list, Class<E> clazz) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, clazz).sheet("sheet1").doWrite(list);
        return outputStream.toByteArray();
    }

    /**
     * 写入Excel文件
     *
     * @param dataListList  数据列表
     * @param sheetNameList sheet名称列表
     * @param clazzList     实体类class列表
     * @return byte[]
     */
    public static byte[] writeExcel(List<List<?>> dataListList, List<String> sheetNameList, List<Class<?>> clazzList, Set<String> excludeColumns) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter writeWorkbook = null;
        if (CollectionUtils.isEmpty(excludeColumns)) {
            writeWorkbook = EasyExcel.write(outputStream).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(getStyleStrategy()).build();
        } else {
            writeWorkbook = EasyExcel.write(outputStream).excelType(ExcelTypeEnum.XLSX).excludeColumnFieldNames(excludeColumns).registerWriteHandler(getStyleStrategy()).build();
        }


        try {
            for (int i = 0; i < dataListList.size(); i++) {
                WriteSheet writeSheet = new WriteSheet();
                writeSheet.setSheetNo(i + 1);
                writeSheet.setSheetName(sheetNameList.get(i));
                writeSheet.setClazz(clazzList.get(i));
                writeWorkbook.write(dataListList.get(i), writeSheet);
            }
        } finally {
            if (writeWorkbook != null) {
                writeWorkbook.finish();
            }
        }
        return outputStream.toByteArray();
    }

    public static void downloadExcel(HttpServletResponse response, byte[] bytes, String fileName) throws IOException {
        // 设置响应头信息
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment; filename*=utf-8''" + URLEncoder.encode(fileName + ".xlsx", "utf-8"));
        // 输出数据到客户端
        response.getOutputStream().write(bytes);
    }

    public static HorizontalCellStyleStrategy getStyleStrategy() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 10);
        // 字体样式
        headWriteFont.setFontName("Calibri");
        headWriteCellStyle.setWriteFont(headWriteFont);
        //自动换行
        headWriteCellStyle.setWrapped(false);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();

        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 10);
        // 字体样式
        contentWriteFont.setFontName("Calibri");
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

}
