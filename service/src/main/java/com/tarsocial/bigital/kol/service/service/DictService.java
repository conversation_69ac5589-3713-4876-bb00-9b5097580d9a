package com.tarsocial.bigital.kol.service.service;


import com.tarsocial.bigital.kol.common.domain.entity.AbiDictEntity;
import com.tarsocial.bigital.kol.common.domain.entity.PepsicoDictEntity;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/3/13
 */
public interface DictService {
    /**
     * 获取所有字典数据
     *
     * @return 结果
     */
    Set<PepsicoDictEntity> listAllByType();


    List<AbiDictEntity> listAllAbi();
}
