package com.tarsocial.bigital.kol.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.aliyuncs.utils.StringUtils;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.dto.PostDetailDto;
import com.tarsocial.bigital.kol.common.domain.dto.PostDetailsDto;
import com.tarsocial.bigital.kol.common.domain.entity.PepsicoDictEntity;
import com.tarsocial.bigital.kol.common.domain.enums.PlatformEnum;
import com.tarsocial.bigital.kol.service.config.AbiProdOssProperties;
import com.tarsocial.bigital.kol.service.constants.Constants;
import com.tarsocial.bigital.kol.service.service.DataCenterProcessService;
import com.tarsocial.bigital.kol.service.service.PostService;
import com.tarsocial.bigital.kol.service.util.DataUtil;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.bigital.kol.service.util.FileUtils;
import com.tarsocial.bigital.kol.service.util.OssUtil;
import com.tarsocial.enums.NullColumnEnum;
import com.tarsocial.enums.OperationEnum;
import com.tarsocial.enums.OrderTypeEnum;
import com.tarsocial.request.CommonKeyAndValue;
import com.tarsocial.request.DocSourceV1Request;
import com.tarsocial.request.FilterRequest;
import com.tarsocial.response.DocSourceResponse;
import com.tarsocial.vo.FieldOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.tarsocial.bigital.kol.service.runner.InitDictDataRunner.DICT_DATA_ABI;


/**
 * <AUTHOR>
 * @since 2024/3/14
 */
@Slf4j
@Service
public class PostServiceImpl implements PostService {

    private static final int BATCH_SIZE = 8000;
    private static final String JSON_L = ".jsonl";

    @Resource
    private AbiProdOssProperties abiProdOssProperties;

    @Resource
    private DataCenterProcessService dataCenterProcessService;

    @Resource
    private OssUtil ossUtil;

    @Override
    public List<PostDetailDto> originalPost(Set<PepsicoDictEntity> dictWords, Long dyRule, Long xhsRule) {


        dictWords.forEach(it -> {
            DocSourceV1Request docSourceV1Request;
            List<FilterRequest> filters = Lists.newArrayList();
            if (it.getPlatform().equals(PlatformEnum.DOUYIN.getCode())) {
                docSourceV1Request = doDealRequest(Lists.newArrayList(it.getPlatform()), it.getDate(), it.getDate(), dyRule, "long_interaction", filters);
            } else {
                docSourceV1Request = doDealRequest(Lists.newArrayList(it.getPlatform()), it.getDate(), it.getDate(), xhsRule, "long_interaction", filters);
            }
            DocSourceResponse docSourceResponse = dataCenterProcessService.sourceV1(docSourceV1Request);
            String scrollId = docSourceResponse.getScrollId();
            List<PostDetailDto> postDetailList = handleResponse(docSourceResponse, it.getPlatform() + "_" + it.getDate());

            boolean hasMore = docSourceResponse.getRows().size() == BATCH_SIZE;
            while (hasMore) {
                docSourceV1Request.setScrollId(scrollId);
                DocSourceResponse newResp = dataCenterProcessService.sourceV1(docSourceV1Request);
                postDetailList.addAll(handleResponse(newResp, it.getPlatform() + "_" + it.getDate()));
                scrollId = newResp.getScrollId();
                hasMore = newResp.getRows().size() == BATCH_SIZE && it.getCount() > postDetailList.size();
            }
            if (postDetailList.size() > it.getCount()) {
                List<PostDetailDto> postDetailDtoList = postDetailList.subList(0, Math.toIntExact(it.getCount()));
                log.info("平台：{} 时间：{}, 数据量：{}", it.getPlatform(), it.getDate(), postDetailDtoList.size());
                FileUtils.writeListJsonL(postDetailDtoList, "./newfile/" + it.getPlatform() + "_" + it.getDate() + JSON_L);
            } else {
                log.info("平台：{} 时间：{}, 需求数据量：{}，实际查询数据量 = {}", it.getPlatform(), it.getDate(), it.getCount(), postDetailList.size());
                FileUtils.writeListJsonL(postDetailList, "./newfile/" + it.getPlatform() + "_" + it.getDate() + "_" + (it.getCount() - postDetailList.size()) + JSON_L);
            }
        });

        // 合并文件
//        String date = DateUtil.formatLocalDate2(LocalDate.now());
//        FileUtils.mergeJsonlFile("post_ " + date + "_mergeJsonl.jsonl", JSON_L);

        // 输出 oss todo

        return Collections.emptyList();
    }


    /**
     * ABI 推数
     *
     * @param platformList  平台
     * @param start         开始时间
     * @param end           结束时间
     * @return 结果
     */
    @Override
    public int originalPostByPlatform(List<String> platformList, String start, String end) {

        LocalDate parse;
        if (start == null) {
            parse = LocalDate.now();
        } else {
            parse = LocalDateTimeUtil.parseDate(start, "yyyy-MM-dd");
        }

        String date = DateUtil.formatLocalDate(parse.plusDays(-1), "yyyy-MM-dd");

        platformList.forEach(platform -> {
            List<PostDetailsDto> postList = originalPost(Lists.newArrayList(platform), date, date, "date_publishedAt", -1);
            // 生成 jsonl 文件
            FileUtils.writePostDetailsJsonL(postList, "./0921_" + platform + JSON_L);
        });

        // 合并文件 csv json
        String ossDate = DateUtil.formatLocalDate(parse.plusDays(-1), "yyyyMMdd");
        String filePath = "./post_" + ossDate + ".csv";
        int dataCount = FileUtils.mergeJsonlFile(filePath, JSON_L);
        ossUtil.uploadAbiDevFile(new File(filePath), ossDate);
        ossUtil.uploadAbiProdFile(new File(filePath), ossDate, abiProdOssProperties);

        return dataCount;
    }

    public List<PostDetailsDto> originalPost(List<String> platformList, String start, String end, String sortField, Integer limit) {
        List<PostDetailsDto> postList = new ArrayList<>();
        List<FilterRequest> filters = Lists.newArrayList();

        DICT_DATA_ABI.forEach(party -> {
            DocSourceV1Request docSourceV1Request = doDealRequest(Lists.newArrayList(platformList), start, end, party.getRuleId(), sortField, filters);
            DocSourceResponse docSourceResponse = dataCenterProcessService.sourceV1(docSourceV1Request);
            String scrollId = docSourceResponse.getScrollId();

            boolean hasMore = docSourceResponse.getRows().size() == BATCH_SIZE && (limit == -1 || postList.size() <= limit);
            while (hasMore) {
                docSourceV1Request.setScrollId(scrollId);
                DocSourceResponse docSourceResponse1 = dataCenterProcessService.sourceV1(docSourceV1Request);
                postList.addAll(handleAbiResponse(docSourceResponse1, party.getBrand()));
                scrollId = docSourceResponse1.getScrollId();
                hasMore = docSourceResponse1.getRows().size() == BATCH_SIZE && (limit == -1 || postList.size() < limit);
            }

            postList.addAll(handleAbiResponse(docSourceResponse, party.getBrand()));
        });
        log.info("platform = {}, count = {}", platformList, postList.size());
        return postList;
    }


    @Override
    public void exportData() {
        List<String> platformList = Lists.newArrayList("weibo", "weixin", "xiaohongshu", "bbs", "web", "bilibili", "zhihu", "kuaishou", "douyin");
        String start = "2023-09-01";
        String end = "2023-11-30";
        List<Long> mainPartyList = Lists.newArrayList(90102L, 90132L);
        mainPartyList.forEach(party -> {
            List<PostDetailsDto> postList = originalPost(platformList, start, end, null, 20000);
            FileUtils.generateExcel(postList, "./abi_" + party + "_" + System.currentTimeMillis() + ".xlsx");
        });
    }

    public DocSourceV1Request doDealRequest(List<String> platform, String start, String end, Long rule, String sortField, List<FilterRequest> filters) {

        DocSourceV1Request docSourceV1Request = new DocSourceV1Request();
        docSourceV1Request.setCount(BATCH_SIZE);

        docSourceV1Request.setMainPartyId(rule);
        // 排序字段
        if (!StringUtils.isEmpty(sortField)) {
            List<FieldOrderVo> orderBy = Lists.newArrayList();
            FieldOrderVo fieldOrderVo = new FieldOrderVo();
            fieldOrderVo.setName(sortField);
            fieldOrderVo.setOrderType(OrderTypeEnum.DESC);
            orderBy.add(fieldOrderVo);
            docSourceV1Request.setOrderBy(orderBy);
        }
        // 需要展示字段
        docSourceV1Request.setFlatSource(true);

        // 开始时间和结束时间
        docSourceV1Request.setStartDate(DateUtil.getStartOfDay(DateUtil.parseDateString(start)));
        docSourceV1Request.setEndDate(DateUtil.getEndOfDay(DateUtil.parseDateString(end)));

        // 平台
        List<Long> platforms = platform.stream().map(Constants.QuarkPlatformType::getPlatformCodeByValue).collect(Collectors.toList());

        docSourceV1Request.setPlatformIds(platforms);

        // 过滤条件
        filters.add(getSimpleFilterComments());
        docSourceV1Request.setFilter(new ArrayList<>(new HashSet<>(filters)));
        docSourceV1Request.setKeepAlive(5);
        docSourceV1Request.setIndexType("post");
        return docSourceV1Request;
    }

    /**
     * 文本长度过滤条件
     *
     * @return 过滤条件
     */
    @NotNull
    private static FilterRequest getSimpleFilter() {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setCustomerDimension(false);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(547L);
        commonKeyAndValue.setValue(Lists.newArrayList("60"));
        ruleIds.add(commonKeyAndValue);
        contentFilter.setOperationEnum(OperationEnum.greater_and_equal);
        contentFilter.setRuleIds(ruleIds);
        return contentFilter;
    }

    /**
     * 评论过滤条件
     *
     * @return 过滤条件
     */
    @NotNull
    private static FilterRequest getSimpleFilterComments() {
        FilterRequest contentFilter = new FilterRequest();
        contentFilter.setFilter(true);
        contentFilter.setCustomerDimension(false);
        List<CommonKeyAndValue> ruleIds = Lists.newArrayList();
        CommonKeyAndValue commonKeyAndValue = new CommonKeyAndValue();
        commonKeyAndValue.setId(312L);
        commonKeyAndValue.setValue(Lists.newArrayList("评论"));
        ruleIds.add(commonKeyAndValue);
        contentFilter.setOperationEnum(OperationEnum.exclude);
        contentFilter.setAllowNull(NullColumnEnum.CONTAIN_NULL_COLUMN);
        contentFilter.setRuleIds(ruleIds);
        return contentFilter;
    }

    public List<PostDetailDto> handleResponse(DocSourceResponse docSourceResponse, String name) {
        List<PostDetailDto> postDetailList = Lists.newArrayList();
        if (Objects.nonNull(docSourceResponse) && CollUtil.isNotEmpty(docSourceResponse.getRows())) {
            docSourceResponse.getRows().forEach(s -> {
                Map<String, Object> source = s.getSource();
                PostDetailDto postDetailDto = new PostDetailDto();
                postDetailDto.setPublishAt(DateUtil.extractDate(String.valueOf(source.get("date_publishedAt"))));
                postDetailDto.setName(name + "数据");
                postDetailDto.setPlatform(source.get("kw_platform"));
                postDetailDto.setInteraction(source.get("long_interaction"));
                postDetailDto.setRepostsCount(source.get("long_repostsCount"));
                postDetailDto.setFollowersCount(source.get("object_user.long_followersCount"));
                postDetailDto.setShareCount(source.get("long_shareCount"));
                postDetailDto.setCommentsCount(source.get("long_commentsCount"));
                postDetailDto.setLikeCount(source.get("long_likeCount"));
                postDetailDto.setCollectCount(source.get("long_collectCount"));
                postDetailDto.setUserId(DataUtil.idMd5(String.valueOf(postDetailDto.getPlatform()), String.valueOf(source.get("object_user.kw_userId"))));
                postDetailDto.setNickname(source.get("object_user.tkw_nickname"));
                postDetailDto.setProvince(source.get("object_user.kw_province"));
                postDetailDto.setCity(source.get("object_user.kw_city"));
                postDetailDto.setCountry(source.get("object_user.kw_country"));
                postDetailDto.setIpLocation(source.get("object_user.kw_ipLocation"));
                postDetailDto.setLocation(source.get("object_user.kw_location"));
                postDetailDto.setId(DataUtil.idMd5(String.valueOf(postDetailDto.getPlatform()), String.valueOf(source.get("kw_id"))));
                postDetailDto.setUrl(source.get("kw_url"));
                postDetailDto.setContent(source.get("tx_content"));
                postDetailDto.setContentLength(source.get("long_contentLength"));
                String content = String.valueOf(postDetailDto.getContent());
                if (content.length() < 60) {
                    return;
                }
                postDetailDto.setContentLength(content.length());
                postDetailDto.setDuration(source.get("long_duration"));
                postDetailDto.setPbw(source.get("object_user.double_pbw"));
                postDetailDto.setTitle(source.get("tx_title"));
                postDetailDto.setCoverUrl(source.get("kw_coverUrl"));
                postDetailDto.setOcrCover(source.get("tx_ocrCover"));
                postDetailDto.setVideoUrl(source.get("kw_videoUrl"));
                postDetailDto.setOcr(source.get("tx_ocr"));
                postDetailDto.setAsr("");
                postDetailDto.setPicUrl(source.get("kw_picUrl"));
                postDetailDto.setHashtags(source.get("kw_hashtags"));
                postDetailDto.setHashtagsCount(source.get("kw_hashtagsCount"));
                postDetailDto.setPostType(source.get("kw_postType"));
                postDetailDto.setVerifiedType(source.get("object_user.kw_verifiedType"));
                postDetailDto.setBirthday(source.get("object_user.date_birthday"));
                postDetailDto.setVerified(source.get("object_user.bool_verified"));
                postDetailDto.setUserGender(source.get("object_user.kw_gender"));
                postDetailDto.setUserDescription(source.get("object_user.tx_description"));
                postDetailDto.setContentType(source.get("kw_contentType"));
                postDetailDto.setUpdateAt(source.get("date_updateAt"));
                postDetailDto.setReadCount(source.get("long_readCount"));
                postDetailDto.setUserLikeCount(source.get("object_user.long_likeCount"));
                postDetailDto.setMediaType(source.get("object_user.kw_mediaType"));
                postDetailDto.setImpCount(source.get("long_impCount"));
                postDetailDto.setMcn(source.get("object_user.kw_mcn"));
                postDetailDto.setIsKOL(source.get("object_user.bool_isKOL"));
                postDetailDto.setEmotion(source.get("object_emotionLabel.kw_emotion"));
                postDetailDto.setNoteCount(source.get("object_user.long_noteCount"));
                postDetailDto.setDongtaiCount(source.get("object_user.long_dongtaiCount"));
                postDetailDto.setMusicCount(source.get("object_user.long_musicCount"));
                postDetailDto.setVideoCount(source.get("object_user.long_videoCount"));
                postDetailDto.setFavoritCount(source.get("object_user.long_favoritCount"));
                postDetailList.add(postDetailDto);
            });
        }
        return postDetailList;
    }

    public List<PostDetailsDto> handleAbiResponse(DocSourceResponse docSourceResponse, String brand) {
        List<PostDetailsDto> postDetailList = Lists.newArrayList();
        if (Objects.nonNull(docSourceResponse) && CollUtil.isNotEmpty(docSourceResponse.getRows())) {
            docSourceResponse.getRows().forEach(s -> {
                Map<String, Object> source = s.getSource();
                PostDetailsDto postDetailDto = new PostDetailsDto();
                postDetailDto.setPublishAt(source.get("date_publishedAt"));
                postDetailDto.setPlatform(source.get("kw_platform"));
                postDetailDto.setDomain(source.get("kw_domain"));
                postDetailDto.setInteraction(source.get("long_interaction"));
                postDetailDto.setRepostsCount(source.get("long_repostsCount"));
                postDetailDto.setZaikan(source.get("long_zaikan"));
                postDetailDto.setFollowersCount(source.get("object_user.long_followersCount"));
                postDetailDto.setDanmuCount(source.get("long_danmuCount"));
                postDetailDto.setCoinCount(source.get("long_coinCount"));
                postDetailDto.setShareCount(source.get("long_shareCount"));
                postDetailDto.setCommentsCount(source.get("long_commentsCount"));
                postDetailDto.setLikeCount(source.get("long_likeCount"));
                postDetailDto.setCollectCount(source.get("long_collectCount"));
                postDetailDto.setUserId(source.get("object_user.kw_userId"));
                postDetailDto.setNickname(source.get("object_user.tkw_nickname"));
                postDetailDto.setProvince(source.get("object_user.kw_province"));
                postDetailDto.setCity(source.get("object_user.kw_city"));
                postDetailDto.setCountry(source.get("object_user.kw_country"));
                postDetailDto.setIpProvince(source.get("object_user.kw_ipProvince"));
                postDetailDto.setId(source.get("kw_id"));
                postDetailDto.setUrl(source.get("kw_url"));
                postDetailDto.setContent(source.get("tx_content"));
                postDetailDto.setDuration(source.get("long_duration"));
                postDetailDto.setPbw(source.get("object_user.double_pbw"));
                postDetailDto.setMid(source.get("kw_mid"));
                postDetailDto.setIsOriginal(source.get("bool_isOriginal"));
                postDetailDto.setTitle(source.get("tx_title"));
                postDetailDto.setBrand(Collections.singletonList(brand));
                postDetailDto.setBiz(source.get("object_user.kw_biz"));
                postDetailDto.setCoverUrl(source.get("kw_coverUrl"));
                postDetailDto.setOcrCover(source.get("tx_ocrCover"));
                postDetailDto.setVideoUrl(source.get("kw_videoUrl"));
                postDetailDto.setOcr(source.get("tx_ocr"));
                postDetailDto.setAsr(source.get("tx_asr"));
                postDetailDto.setPicUrl(source.get("kw_picUrl"));
                postDetailDto.setPicCount(source.get("long_picCount"));
                postDetailDto.setMentions(source.get("kw_mentions"));
                postDetailDto.setHashtags(source.get("kw_hashtags"));
                postDetailDto.setHashtagsCount(source.get("kw_hashtagsCount"));
                postDetailDto.setPostType(source.get("kw_postType"));
                postDetailDto.setOriginPublishedAt(source.get("object_origin.date_publishedAt"));
                postDetailDto.setOriginId(source.get("object_origin.kw_id"));
                postDetailDto.setOriginContent(source.get("object_origin.tx_content"));
                postDetailDto.setOriginReadCount(source.get("object_origin.long_readCount"));
                postDetailDto.setOriginRepostsCount(source.get("object_origin.long_repostsCount"));
                postDetailDto.setOriginCommentsCount(source.get("object_origin.long_commentsCount"));
                postDetailDto.setOriginLikeCount(source.get("object_origin.long_likeCount"));
                postDetailDto.setOriginInteraction(source.get("object_origin.long_interaction"));
                postDetailDto.setOriginAuthorName(source.get("object_origin.object_user.kw_authorName"));
                postDetailDto.setOriginAuthorId(source.get("object_origin.object_user.kw_userId"));
                postDetailDto.setOriginAuthorNickname(source.get("object_origin.object_user.tkw_nickname"));
                postDetailDto.setOriginFriendsCount(source.get("object_origin.object_user.long_friendsCount"));
                postDetailDto.setOriginStatusesCount(source.get("object_origin.object_user.long_statusesCount"));
                postDetailDto.setOriginFavouritesCount(source.get("object_origin.object_user.long_favouritesCount"));
                postDetailDto.setOriginVerified(source.get("object_origin.object_user.bool_verified"));
                postDetailDto.setOriginBiFollowersCount(source.get("object_origin.object_user.long_biFollowersCount"));
                postDetailDto.setVerifiedType(source.get("object_user.kw_verifiedType"));
                postDetailDto.setVerifiedReason(source.get("object_user.tkw_verifiedReason"));
                postDetailDto.setGender(source.get("object_user.kw_gender"));
                postDetailDto.setBirthday(source.get("object_user.date_birthday"));
                postDetailDto.setDescription(source.get("object_user.tx_description"));
                postDetailDto.setUserUrl(source.get("object_user.kw_url"));
                postDetailDto.setVerified(source.get("object_user.bool_verified"));
                postDetailDto.setOnlyId(postDetailDto.getPlatform() + "-" + postDetailDto.getId());
                postDetailDto.setCreateTime(source.get("date_createAt"));
                postDetailDto.setUpdateTime(source.get("date_updateAt"));

                postDetailList.add(postDetailDto);
            });
        }
        return postDetailList;
    }

}
