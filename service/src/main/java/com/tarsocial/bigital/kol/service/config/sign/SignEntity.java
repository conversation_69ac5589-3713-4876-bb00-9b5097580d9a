package com.tarsocial.bigital.kol.service.config.sign;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@ToString
@FieldNameConstants
public class SignEntity {

    private String sign;

    private Long appId;

    private String nonce;

    private String timestamp;

    private SignModel signModel;

    private Map<String, String[]> params;

    private String signSerial;

    public static SignEntity design(Long appId, String appSecret, Map<String, String[]> params) {
        return SignEntity.design(appId, appSecret, params, null, null);
    }

    public static SignEntity design(Long appId, String appSecret, Map<String, String[]> params, String nonce, String timestamp) {
        Assert.notNull(appId, "appId is empty, cannot sign.");
        SignEntity signEntity = new SignEntity();
        signEntity.setAppId(appId);
        signEntity.setNonce(nonce);
        signEntity.setTimestamp(timestamp);
        signEntity.setSignModel(SignModel.SHA_256);
        signEntity.setParams(params);
        if (StringUtils.isNotEmpty(appSecret)) {
            try {
                signEntity.setSign(signEntity.countersign(appSecret));
            } catch (Exception e) {
                log.error("{} countersign fail : {}", appId, e);
            }
        }
        return signEntity;
    }

    public boolean usedSignature() {
        return StringUtils.isNotEmpty(this.sign);
    }

    public String countersign(String appSecret) throws Exception {
        List<String> signItems = Lists.newArrayList();
        signItems.add(Fields.appId.concat("=").concat(this.appId.toString()));
        if (StringUtils.isNotEmpty(this.nonce)) {
            signItems.add(Fields.nonce.concat("=").concat(this.nonce));
        }
        if (StringUtils.isNotEmpty(this.timestamp)) {
            signItems.add(Fields.timestamp.concat("=").concat(this.timestamp));
        }
        // key 升序
        signItems.addAll(
                new TreeMap<>(this.params).entrySet().stream().map(
                        entry -> entry.getKey().concat("=").concat(String.join(",", entry.getValue()))
                ).collect(Collectors.toList())
        );

        this.signSerial = String.join("&", signItems);

        Mac mac = Mac.getInstance(signModel.getModel());
        mac.init(new SecretKeySpec(appSecret.getBytes(), signModel.getModel()));
        mac.update(signSerial.getBytes(StandardCharsets.UTF_8));
        return new BigInteger(1, mac.doFinal()).toString(16);
    }

}
