package com.tarsocial.bigital.kol.service.job;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.entity.AbiPushDataLogEntity;
import com.tarsocial.bigital.kol.service.config.AbiDevOssProperties;
import com.tarsocial.bigital.kol.service.config.AbiProdOssProperties;
import com.tarsocial.bigital.kol.service.mapper.AbiPushDataLogMapper;
import com.tarsocial.bigital.kol.service.service.PostService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.bigital.kol.service.util.OssUtil;
import com.tarsocial.bigital.kol.service.util.WeChatBotUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 定时推送
 *
 * <AUTHOR>
 * @since 2024/2/27
 */
@Component
@Slf4j
public class PushDataSchedule {

    @Resource
    private PostService postService;

    @Resource
    private OssUtil ossUtil;

    @Resource
    private AbiDevOssProperties properties;

    @Resource
    private AbiProdOssProperties abiProdOssProperties;

    @Resource
    private AbiPushDataLogMapper abiPushDataLogMapper;


    /**
     * 每天3点传输
     */
//    @Scheduled(cron = "0 30 2 * * ?")
    public void pushData() {
        LocalDateTime now = LocalDateTime.now();
        WeChatBotUtils.sendTextMsg(String.format("时间：%s\n" +
                "预警：开始推送", DateUtil.formatLocalDateTime(LocalDateTime.now())));

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("start ABI data transmission time = {}....", LocalDate.now());
        List<String> platformList = Lists.newArrayList("weibo", "weixin", "xiaohongshu", "bbs", "web", "bilibili", "zhihu", "kuaishou", "douyin");


        int dataCount = 0;
        try {
            dataCount = postService.originalPostByPlatform(platformList, null, null);
            stopWatch.stop();
            log.info("end ABI data transmission total time = {} 秒....", stopWatch.getTotalTimeSeconds());

            WeChatBotUtils.sendTextMsg(String.format("时间：%s\n" +
                    "预警：推送成功！\n" +
                    "耗时：%s 秒 \n" +
                    "数据量：%s", DateUtil.formatLocalDateTime(LocalDateTime.now()), stopWatch.getTotalTimeSeconds(), dataCount));

            insertLog(now, LocalDateTime.now(), dataCount, 200, "成功！");
        } catch (Exception e) {
            insertLog(now, LocalDateTime.now(), 0, 10001, "失败！");
            WeChatBotUtils.sendTextMsg(String.format("时间：%s\n" +
                    "预警：推送失败！\n", DateUtil.formatLocalDateTime(LocalDateTime.now())));
            log.error(e.getMessage());
        }
    }


    /**
     * 每天凌晨4点校验推送是否成功
     */
//    @Scheduled(cron = "0 0 4 * * ?")
    public void checkData() {

        String date = DateUtil.formatLocalDate(LocalDate.now().plusDays(-1), "yyyyMMdd");
        String file = "post_" + date + ".csv";

        String fileName = properties.getBucketName() + "/table/post/" + date + "/" + file;
        boolean fileExists = ossUtil.getOosClient().doesObjectExist(properties.getBucket(), fileName);


        if (fileExists) {
            WeChatBotUtils.sendTextMsg(String.format("时间：%s\n" +
                    "预警：数据已经推送成功!\n" +
                    "环境：dev", DateUtil.formatLocalDateTime(LocalDateTime.now())));
        } else {
            WeChatBotUtils.sendTextMsg(String.format("时间：%s\n" +
                    "预警：数据已经推送失败！\n" +
                    "环境：dev", DateUtil.formatLocalDateTime(LocalDateTime.now())));

        }

        String fileName2 = abiProdOssProperties.getBucketName() + "/table/post/" + date + "/" + file;

        OSS oosClient = new OSSClientBuilder()
                .build(abiProdOssProperties.getEndpoint(), abiProdOssProperties.getAccessKeyId(), abiProdOssProperties.getAccessKeySecret());

        boolean fileExists2 = oosClient.doesObjectExist(abiProdOssProperties.getBucket(), fileName2);

        if (fileExists2) {
            WeChatBotUtils.sendTextMsg(String.format("时间：%s\n" +
                    "预警：数据已经推送成功! \n" +
                    "环境：prod", DateUtil.formatLocalDateTime(LocalDateTime.now())));
        } else {
            WeChatBotUtils.sendTextMsg(String.format("时间：%s\n" +
                    "预警：数据已经推送失败！\n" +
                    "环境：prod", DateUtil.formatLocalDateTime(LocalDateTime.now())));

        }

    }

    public void insertLog(LocalDateTime startTime, LocalDateTime endTime, Integer dataCount, Integer code, String message) {
        AbiPushDataLogEntity abiPushDataLogEntity = new AbiPushDataLogEntity();
        abiPushDataLogEntity.setCode(code);
        abiPushDataLogEntity.setDataCount(dataCount);
        abiPushDataLogEntity.setMessage(message);
        abiPushDataLogEntity.setPushStartTime(startTime);
        abiPushDataLogEntity.setPushEndTime(endTime);
        abiPushDataLogMapper.insert(abiPushDataLogEntity);
    }


}
