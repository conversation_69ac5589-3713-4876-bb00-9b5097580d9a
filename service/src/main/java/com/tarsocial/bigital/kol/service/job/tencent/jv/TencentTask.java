package com.tarsocial.bigital.kol.service.job.tencent.jv;

import com.tarsocial.bigital.kol.common.domain.entity.tencent.Task;
import com.tarsocial.bigital.kol.service.config.global.UUIDUtil;
import com.tarsocial.bigital.kol.service.service.guangnian.GameDataProcessService;
import com.tarsocial.bigital.kol.service.service.tencent.DataProcessService;
import com.tarsocial.bigital.kol.service.service.tencent.TaskService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.bigital.kol.service.util.email.MailUtil;
import com.tarsocial.bigital.kol.service.util.email.SendMailBean;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TencentTask {

    @Resource
    private TaskService taskService;

    @Resource
    private DataProcessService dataProcessService;

    @Resource
    @Qualifier("asyncExecutor")
    private Executor asyncExecutor;

    @XxlJob("pushTencentPost")
    public void pushTencentPost() {
        log.info("pushTencentPost ------->  start");
        Date now = new Date();
        // Tencent 话题任务获取
        List<Task> tasks = taskService.taskList(1L, now);
        // 抓数起始时间
        tasks.forEach(task -> asyncExecutor.execute(() -> {
            log.info("Thread:{}, pushTencentPost -------> taskId:{}  start", Thread.currentThread().getName(), task.getId());
            MDC.put("traceId", UUIDUtil.uuid());
            // 获取 当前时间和抓取开始时间的差值
            long gap = DateUtil.daysBetween(task.getStartTime(), now);

            if ((gap % task.getFrequency()) == 0) {
                // 按频率抓数
                dataProcessService.pushTencentPost(task, task.getPublishedAt(), now);
            }
            log.info("Thread:{}, pushTencentPost -------> taskId:{}  end", Thread.currentThread().getName(), task.getId());
            // 防止内存泄露
            MDC.remove("traceId");

        }));

    }
}
