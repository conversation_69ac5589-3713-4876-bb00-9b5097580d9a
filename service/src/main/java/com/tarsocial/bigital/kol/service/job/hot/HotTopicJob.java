package com.tarsocial.bigital.kol.service.job.hot;

import com.tarsocial.bigital.kol.service.service.HotTopicJobService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class HotTopicJob {

    @Resource
    private HotTopicJobService hotTopicJobService;

    @XxlJob("hotMonitor")
    public void hotMonitor() {
        hotTopicJobService.monitor();
    }


}
