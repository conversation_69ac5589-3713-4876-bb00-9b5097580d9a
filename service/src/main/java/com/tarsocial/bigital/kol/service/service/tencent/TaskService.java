package com.tarsocial.bigital.kol.service.service.tencent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.Task;
import com.tarsocial.bigital.kol.service.mapper.TaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @ClassName TaskService
 * @date 2024年03月26日
 */
@Service
@Slf4j
public class TaskService extends ServiceImpl<TaskMapper, Task> {

    public Task getTaskInfo(String topicKey) {
        LambdaQueryWrapper<Task> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Task::getTopicKey, topicKey).eq(Task::getDeleted, 0).last("limit 1");
        return baseMapper.selectOne(wrapper);
    }

    public void saveTask(Task task) {
        baseMapper.batchSave(Collections.singletonList(task));
    }

    public List<Task> taskList(Long appId, Date now) {
        LambdaQueryWrapper<Task> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Task::getAppId, appId)
                .eq(Task::getDeleted, 0)
                .gt(Task::getEndTime, now)
                .lt(Task::getStartTime, now);
        return baseMapper.selectList(wrapper);
    }
}
