package com.tarsocial.bigital.kol.service.controller.tencent.external;

import com.tarsocial.bigital.kol.service.service.tencent.DataProcessService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/external/tencent")
public class ExternalController {

    @Resource
    private DataProcessService dataProcessService;


//    @PostMapping("/topic/task/create")
//    public BaseResponse<String> taskCreate(@RequestBody TaskCreateRequest request, @RequestHeader("appId") String appId) {
//        Task task = dataProcessService.taskCreate(request, appId);
//        return new BaseResponse<>(task.getTopicKey());
//    }
//
//
//    @PostMapping("/topic/post")
//    public BaseResponse<String> pushPost(@RequestBody PushPostRequest request) {
//        dataProcessService.pushPost(request);
//        return new BaseResponse<>("success");
//    }

}
