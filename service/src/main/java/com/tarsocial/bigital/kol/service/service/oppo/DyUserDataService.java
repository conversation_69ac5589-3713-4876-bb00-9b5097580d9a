package com.tarsocial.bigital.kol.service.service.oppo;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tarsocial.bigital.kol.common.domain.entity.oppo.DyUserData;
import com.tarsocial.bigital.kol.service.mapper.DyUserDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class DyUserDataService extends ServiceImpl<DyUserDataMapper, DyUserData> {

    public Integer addQueryNumber(List<Long> idList) {
        if(CollectionUtils.isEmpty(idList))
            return 0;
        return baseMapper.addQueryNumber(idList);
    }

}
