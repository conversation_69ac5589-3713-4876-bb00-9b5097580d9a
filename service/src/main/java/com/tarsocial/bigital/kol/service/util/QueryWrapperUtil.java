package com.tarsocial.bigital.kol.service.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class QueryWrapperUtil {

    public static String sql(Configuration configuration, QueryWrapper wrapper, Class<? extends BaseMapper> mapper) {
        MappedStatement statement = configuration.getMappedStatement(mapper.getName() + ".selectCount");
        Map params = Collections.singletonMap("ew", wrapper);
        BoundSql boundSql = statement.getBoundSql(params);
        // 带有问号占位符的 SQL 语句
        String sql = boundSql.getSql();
        // 参数信息列表
        List<ParameterMapping> paramMappings = boundSql.getParameterMappings();
        // MetaObject 是 mybatis 通过表达式取出对象内容的工具
        MetaObject metaObject = configuration.newMetaObject(params);
        for (ParameterMapping p : paramMappings) {
            String paramName = p.getProperty();
            Object paramValue = metaObject.getValue(paramName);
            String value = "";
            if (paramValue instanceof List) {
                value = "('" + ((List) paramValue).stream().collect(Collectors.joining("','")) + "')";
            } else {
                value = "'" + paramValue.toString() + "'";
            }
            sql = sql.replaceFirst("\\?", value);
        }
        return sql;

    }

    public static String condition(String sql) {
        String condition = null;
        if (sql.contains("where")) {
            condition = sql.split("where")[1].trim();
        }
        if (sql.contains("WHERE")) {
            condition = sql.split("WHERE")[1].trim();
        }
        if (condition != null && condition.contains("limit")) {
            condition = condition.split("limit")[0].trim();
        }
        if (condition != null && condition.contains("LIMIT")) {
            condition = condition.split("LIMIT")[0].trim();
        }
        return condition;
    }

    public static void main(String[] args) {
        String sql = "SELECT COUNT( * ) AS total FROM dy_user_data \n" +
                " \n" +
                " WHERE (user_id IN ('12','13','14') AND cpm >= '2' AND cpm <= '9' AND followers_count >= '3' AND followers_count <= '7' AND monthly_connected_users >= '1' AND monthly_connected_users <= '8')Disconnected from the target VM, address: '127.0.0.1:54349', transport: 'socket'\n";
        System.out.println( condition(sql));
    }

}
