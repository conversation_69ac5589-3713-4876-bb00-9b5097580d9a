package com.tarsocial.bigital.kol.service.client;

import com.tarsocial.config.TokenConfiguration;
import com.tarsocial.dag.framework.feign.config.DagFeignClientConfiguration;
import com.tarsocial.dag.task.model.request.SubTaskListRequest;
import com.tarsocial.dag.task.model.request.TaskCreateRequest;
import com.tarsocial.dag.task.model.response.SubTaskResp;
import com.tarsocial.dag.task.spi.TaskSpi;
import com.tarsocial.spi.DataCenterSpi;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "quark-data-task", url = "${quark.dataTask}", configuration = {TokenConfiguration.class,
        DagFeignClientConfiguration.class})
public interface QuarkDataTaskSpi {

    /**
     * 创建普通任务
     * monitor create
     *
     * @param request request
     * @return Object
     */
    @PostMapping("task/create")
    Object taskCreate(@RequestBody TaskCreateRequest request);

    /**
     * 普通任务查询
     * taskDetail
     *
     * @param id id
     * @return Object
     */
    @GetMapping("task/detail")
    Object taskDetail(@RequestParam Long id);

    /**
     * 子任务列表查询
     * taskDetail
     *
     * @param request request
     * @return Object
     */
    @PostMapping("/task/sub/list")
    Object subTaskDetail(@RequestBody SubTaskListRequest request);

    @PostMapping("/task/cancel")
    Object cancel(@RequestBody List<Long> taskIds);
}
