package com.tarsocial.bigital.kol.service.client.tencent;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Maps;
import com.tarsocial.bigital.kol.service.config.sign.SignEntity;
import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.URLDecoder;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Order(2)
@Component
public class SignatureInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        Map<String, String[]> serialMap = serial(map(template.queries()), bodyString(template.body()));

        String timestamp = JSON.toJSONString(System.currentTimeMillis());
        Random random = new Random();
        String nonce = (100000 + random.nextInt(899999)) + "";

        SignEntity design = SignEntity.design(1L, "281503a6ae5b33e5bbcf97639f66a6fb", serialMap, nonce, timestamp);

        template.header(SignEntity.Fields.appId, "1");
        template.header(SignEntity.Fields.sign, design.getSign());
        template.header(SignEntity.Fields.timestamp, timestamp);
        template.header(SignEntity.Fields.nonce, nonce);
    }

    private static final Long CONNECT_TIME_OUT_MILLIS = 30L;

    private static final Long READ_TIME_OUT_MILLIS = 120L;

    @Bean
    public Request.Options options(){
        return new Request.Options(CONNECT_TIME_OUT_MILLIS, TimeUnit.SECONDS, READ_TIME_OUT_MILLIS, TimeUnit.SECONDS, true);
    }


    public static Map<String, String[]> serial(Map<String, String[]> params, String body) {
        if (CollectionUtils.isEmpty(params)) {
            params = new HashMap<>(1);
        }
        if (StringUtils.hasLength(body)) {
            params.put("body", new String[]{body});
        }
        return params;
    }

    public static String bodyString(byte[] body) {
        if (ArrayUtils.isEmpty(body)) {
            return null;
        }
        BufferedReader reader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(body)));
        StringBuilder buffer = new StringBuilder();
        String line;
        while (true) {
            try {
                if ((line = reader.readLine()) == null) {
                    break;
                }
            } catch (IOException e) {
                break;
            }
            buffer.append(line.trim());
        }
        return buffer.toString().replace(" ", "");
    }

    private Map<String, String[]> map(Map<String, Collection<String>> queries) {
        Map<String, String[]> params = Maps.newHashMap();
        queries.forEach((key, value) -> params.put(key, value.stream().map(s -> {
            try {
                return URLDecoder.decode(s, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
                return null;
            }
        }).toArray(String[]::new)));
        return params;
    }

}
