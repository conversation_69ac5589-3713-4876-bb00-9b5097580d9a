package com.tarsocial.bigital.kol.service.util;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tarsocial.bigital.kol.common.domain.dto.PostDetailDto;
import com.tarsocial.bigital.kol.common.domain.dto.PostDetailsDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.io.*;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/25
 */
@Slf4j
public class FileUtils {

    /**
     * 支持 jsonl csv 文件的合并
     *
     * @param fileName 合并的文件名
     * @param suffix   后缀
     */
    public static int mergeJsonlFile(String fileName, String suffix) {
        File[] files = new File(".").listFiles((dir, name) -> name.endsWith(suffix));
        if (files != null) {
            int jsonCount = 0;

            try (BufferedWriter writer = new BufferedWriter(new FileWriter(fileName))) {
                for (File file : files) {
                    try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            writer.write(line);
                            writer.newLine();
                            jsonCount++;

                        }
                    }
                    Files.delete(file.toPath());
                }
                log.info("Merged successfully.");
                log.info("Total JSON count: " + jsonCount);
                return jsonCount;
            } catch (IOException e) {
                log.error("Error occurred while writing to file: " + e.getMessage());
                return 0;
            }
        }
        return 0;
    }

    /**
     * write file
     *
     * @param dataList data
     * @param filePath path
     */
    public static void writeListJsonL(List<PostDetailDto> dataList, String filePath) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            ObjectMapper objectMapper = new ObjectMapper();
            for (Object data : dataList) {
                String json = objectMapper.writeValueAsString(data);
                writer.write(json);
                writer.newLine();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    /**
     * write file
     *
     * @param dataList data
     * @param filePath path
     */
    public static void writePostDetailsJsonL(List<PostDetailsDto> dataList, String filePath) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            ObjectMapper objectMapper = new ObjectMapper();
            for (Object data : dataList) {
                String json = objectMapper.writeValueAsString(data);
                writer.write(json);
                writer.newLine();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public static void generateExcel(List<PostDetailsDto> dataList, String filePath) {

        // 设置每个工作簿的行数
        int batchSize = 1000;

        // 创建工作簿
        Workbook workbook = new SXSSFWorkbook();

        // 创建标题行
        Sheet sheet = workbook.createSheet("Data");
        Row headerRow = sheet.createRow(0);
        Field[] fields = PostDetailDto.class.getDeclaredFields();
        int cellNum = 0;
        for (Field field : fields) {
            JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
            if (jsonProperty != null) {
                Cell cell = headerRow.createCell(cellNum++);
                cell.setCellValue(jsonProperty.value());
            }
        }

        // 填充数据
        int rowNum = 1;
        for (PostDetailsDto data : dataList) {
            Row row = sheet.createRow(rowNum++);
            cellNum = 0;
            for (Field field : fields) {
                JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
                if (jsonProperty != null) {
                    field.setAccessible(true);
                    try {
                        Cell cell = row.createCell(cellNum++);
                        Object value = field.get(data);
                        if (value != null) {
                            if (value instanceof String) {
                                String cellValue = value.toString();
                                if (cellValue.length() > 32767) {
                                    cellValue = cellValue.substring(0, 32767);
                                }
                                cell.setCellValue(cellValue);
                            } else if (value instanceof Integer) {
                                cell.setCellValue((Integer) value);
                            } else if (value instanceof Double) {
                                cell.setCellValue((Double) value);
                            } // 可以根据需要添加其他类型的处理
                        }
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }

            try {
                // 达到批处理大小时，刷新并释放临时文件
                if (rowNum % batchSize == 0) {
                    ((SXSSFSheet) sheet).flushRows(100);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 写入到文件
        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            workbook.write(fileOut);
            System.out.println("Excel文件已生成：" + filePath);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


}
