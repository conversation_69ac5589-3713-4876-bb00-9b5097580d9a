package com.tarsocial.bigital.kol.service.service.tencent;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.PostReceiveRequest;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.TaskStatusRequest;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.TencentPostBean;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.TencentPostExcel;
import com.tarsocial.bigital.kol.common.domain.entity.Post;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.Task;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.TencentPostLog;
import com.tarsocial.bigital.kol.common.domain.enums.QuarkPlatform;
import com.tarsocial.bigital.kol.common.domain.request.tencent.PushPostRequest;
import com.tarsocial.bigital.kol.common.domain.request.tencent.TaskCreateRequest;
import com.tarsocial.bigital.kol.service.client.tencent.TencentExtendSpi;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import com.tarsocial.bigital.kol.service.service.DataCenterProcessService;
import com.tarsocial.bigital.kol.service.service.ocr.OcrDataProcessService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.bigital.kol.service.util.Downloader;
import com.tarsocial.bigital.kol.service.util.tos.TosUtil;
import com.tarsocial.request.DocSourceV1Request;
import com.tarsocial.response.DocSourceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName DataProcessService
 * @date 2024年03月26日
 */
@Service
@Slf4j
public class DataProcessService {

    @Resource
    private TaskService taskService;

    @Resource
    private TencentPostLogService tencentPostLogService;

    @Resource
    private OcrDataProcessService ocrDataProcessService;

    @Resource
    private DataCenterProcessService dataCenterProcessService;

    @Resource
    private TencentExtendSpi tencentExtendSpi;

    @Resource
    @Qualifier("asyncExecutor")
    private Executor asyncExecutor;

    @Resource
    private TosUtil tosUtil;

    @Value("${jv.data.num}")
    private Integer dataNum;

    public Task taskCreate(TaskCreateRequest request, String appId) {
        Task task = BeanUtil.toBean(request, Task.class);
        task.setCreateTime(new Date());
        task.setDeleted(0);
        task.setPublishedAt(DateUtil.convertDate("2023-06-01 00:00:00", "yyyy-MM-dd HH:mm:ss"));
        task.setEndTime(DateUtil.getDayEnd(task.getEndTime()));
        task.setAppId(Objects.nonNull(appId) ? Long.parseLong(appId) : null);
        taskService.saveTask(task);
        return task;
    }

    public void pushPost(PushPostRequest request) {
        Task taskInfo = taskService.getTaskInfo(request.getTopicKey());
        if (Objects.isNull(taskInfo)) {
            throw new BusinessException("未获取到相关任务，topicKey -->   " + request.getTopicKey());
        }
        // topic 验证
        List<String> check = checkTopic(request);
        if (!CollectionUtils.isEmpty(check)) {
            throw new BusinessException("未获取到topic关联的帖子ID列表 : " + StringUtils.join(check, ";"));
        }

        asyncExecutor.execute(() -> {
            // 查询夸克 抖音帖子数据
            List<TencentPostBean> postBeanList = queryPost(request.getIds(), taskInfo);
            log.info("quark query post size ----> {} 条", postBeanList.size());

            // 调用第三方接口，传输数据
            PostReceiveRequest request1 = new PostReceiveRequest();
            request1.setTaskId(taskInfo.getTopicKey());
            request1.setTopicKey(request.getTopicKey());
            request1.setData(postBeanList);
            log.info("tencentExtendSpi.taskReceive request size： {}", postBeanList.size());
            Object response = tencentExtendSpi.taskReceive(request1);
            log.info("tencentExtendSpi.taskReceive response： {}", JSON.toJSONString(response));

            // 保存到 post_log 中
            tencentPostLogService.savePostLog(postBeanList, request.getTopicKey());

            // 数据传输完成
            TaskStatusRequest statusRequest = new TaskStatusRequest();
            statusRequest.setTopicKey(taskInfo.getTopicKey());
            statusRequest.setIsFinish(true);
            log.info("tencentExtendSpi.taskStatus request： {}", JSON.toJSONString(statusRequest));
            Object taskStatus = tencentExtendSpi.taskStatus(statusRequest);
            log.info("tencentExtendSpi.taskStatus response： {}", JSON.toJSONString(taskStatus));
            // 保存原始数据文件
            log.info("保存原始数据文件 topicKey : {},  post size： {} ---> start", taskInfo.getTopicKey(), postBeanList.size());
            savePostFile(taskInfo, postBeanList);
            log.info("保存原始数据文件 topicKey : {},  post size： {} ---> end", taskInfo.getTopicKey(), postBeanList.size());
        });
    }

    public Integer queryPost(Date start, Date end, Task task) {
        int dataNum = 0;
        List<TencentPostBean> list = Lists.newArrayList();
        DocSourceV1Request request = ocrDataProcessService.doDealRequest(start, end, 99611L, task.getTopicName());
        DocSourceResponse response = pullPost(request, list, task.getTopicName());
        // 推送数据
        pushPost(task, list);
        dataNum += list.size();

        String scrollId = response.getScrollId();
        boolean hasMore = response.getRows().size() == 10000;
        while (hasMore) {
            List<TencentPostBean> posts = Lists.newArrayList();
            request.setScrollId(scrollId);
            DocSourceResponse scrollResponse = pullPost(request, posts, task.getTopicName());
            // 推送数据
            pushPost(task, posts);
            scrollId = scrollResponse.getScrollId();
            hasMore = scrollResponse.getRows().size() == 10000;
            dataNum += posts.size();
        }
        return dataNum;
    }

    private DocSourceResponse pullPost(DocSourceV1Request docSourceV1Request, List<TencentPostBean> list, String topicName) {
        DocSourceResponse response = dataCenterProcessService.sourceV1(docSourceV1Request);
        Date now = new Date();
        response.getRows().forEach(x -> {
            if (Objects.isNull(x) || CollectionUtils.isEmpty(x.getSource())) {
                return;
            }
            TencentPostBean postBean = processPost(x.getSource(), topicName, now);
            if (Objects.nonNull(postBean)) {
                list.add(postBean);
            }
        });
        return response;
    }

    private List<TencentPostBean> queryPost(List<String> ids, Task taskInfo) {
        DocSourceV1Request docSourceV1Request = ocrDataProcessService.doDealRequest(ids, QuarkPlatform.DOUYIN.getValue(), 99611L);
        List<TencentPostBean> list = Lists.newArrayList();
        pullPost(docSourceV1Request, list, taskInfo.getTopicName());
        return list;
    }


    private TencentPostBean processPost(Map<String, Object> source, String topicName, Date now) {
        Post post;
        try {
            post = JSON.parseObject(JSON.toJSONString(source), Post.class);
        } catch (Exception e) {
            log.warn("TencentPostBean processPost convert error：{}", source);
            return null;
        }
        if (Objects.isNull(post)) {
            return null;
        }

        TencentPostBean tencentPost = new TencentPostBean();
        tencentPost.setDataRange(topicName);
        tencentPost.setGrabTime(DateUtil.formatDate(now, "yyyy-MM-dd HH:mm:ss"));
        tencentPost.setPublishTime(post.getDatePublishedAt());
        tencentPost.setPlatform(post.getPlatform());
        tencentPost.setTitle(post.getContent());
        tencentPost.setPlayCount(post.getReadCount());
        tencentPost.setLikeCount(post.getLikeCount());
        tencentPost.setCommentCount(post.getCommentsCount());
        tencentPost.setForwardCount(post.getRepostsCount());
        tencentPost.setFavorateCount(post.getCollectCount());
        String url = post.getUrl();
        tencentPost.setUrl(org.springframework.util.StringUtils.hasLength(url) ? url : "https://www.douyin.com/video/" + post.getId());
        tencentPost.setContentId(post.getId());
        tencentPost.setVideoLength(post.getDuration());
        tencentPost.setWordCount(post.getContentLength());
        tencentPost.setIsOriginal(post.getIsOriginal());
        tencentPost.setCoverUrl(post.getCoverUrl());
        tencentPost.setCoverOCR(post.getOcrCover());
        tencentPost.setTopics(post.getHashtags());
        tencentPost.setTopicCount(post.getHashtagsCount());
        tencentPost.setContentType(post.getContentType());
        tencentPost.setIsCommercialOrder(post.getIsPaid());
        tencentPost.setOrderPrice(post.getPaidPrice());
        tencentPost.setVideoTags(post.getVideoTag());
        tencentPost.setIsDelete(post.getIsDelete());
        Post.ObjectMusic objectMusic = post.getObjectMusic();
        if (Objects.nonNull(objectMusic)) {
            tencentPost.setMusicId(objectMusic.getId());
        }

        // 购物车
        tencentPost.setHasProduct(post.getIsECommerceEnable());
        List<Post.ObjectPromotions> promotions = post.getObjectPromotions();
        if (!CollectionUtils.isEmpty(promotions)) {
            List<TencentPostBean.ProductInfoDTO> productList = Lists.newArrayList();
            promotions.forEach(x -> {
                TencentPostBean.ProductInfoDTO product = new TencentPostBean.ProductInfoDTO();
                product.setProductId(x.getPromotionId());
                product.setProductUrl(x.getUrl());
                product.setProductImage(x.getImgsUrl());
                productList.add(product);
            });
            tencentPost.setProductInfo(productList);
        }

        tencentPost.setCreateTime(post.getCreateAt());
        tencentPost.setUpdateTime(post.getUpdateAt());

        Post.ObjectUser user = post.getObjectUser();

        if (Objects.nonNull(user)) {
            tencentPost.setUserName(user.getNickname());
            tencentPost.setUserUID(user.getUserId());
            tencentPost.setUserAccount(user.getPlatformUserId());
            tencentPost.setFollowerCount(user.getFollowersCount());
            tencentPost.setUserBirthday(user.getBirthday());
            tencentPost.setUserDescription(user.getDescription());
            tencentPost.setUserHomepageUrl(user.getUrl());
            tencentPost.setUserAvatarUrl(user.getAvatar());
            tencentPost.setGender(user.getGender());
            tencentPost.setVerifiedName(user.getVerifiedReason());
        }
        return tencentPost;
    }


    private List<String> checkTopic(PushPostRequest request) {
        List<TencentPostLog> posts = tencentPostLogService.findPostByTopic(request.getIds(), request.getTopicKey());
        // 去重 获取帖子ID
        List<String> list = posts.stream().map(TencentPostLog::getPostId).distinct().collect(Collectors.toList());
        list.removeIf(x -> request.getIds().contains(x));
        return list;
    }

    public void pushTencentPost(Task task, Date start, Date end) {
        // 查询夸克 抖音帖子数据并推送
        int postSize = queryPost(start, end, task);
        log.info("quark query post size ----> {} 条", postSize);

        // 数据传输完成
        TaskStatusRequest statusRequest = new TaskStatusRequest();
        statusRequest.setTopicKey(task.getTopicKey());
        statusRequest.setIsFinish(true);
        log.info("tencentExtendSpi.taskStatus topicKey : {}, request： {}", task.getTopicKey(), JSON.toJSONString(statusRequest));
        Object taskStatus = tencentExtendSpi.taskStatus(statusRequest);
        log.info("tencentExtendSpi.taskStatus topicKey : {}, response： {}", task.getTopicKey(), JSON.toJSONString(taskStatus));
    }

    public void pushPost(Task task, List<TencentPostBean> postBeanList) {
        if (CollectionUtils.isEmpty(postBeanList)) {
            return;
        }
        // 拆分集合
        List<List<TencentPostBean>> partition = Lists.partition(postBeanList, dataNum);

        partition.forEach(x -> {
            // 调用第三方接口，传输数据
            PostReceiveRequest request1 = new PostReceiveRequest();
            request1.setTaskId(task.getTopicKey());
            request1.setTopicKey(task.getTopicKey());
            request1.setData(x);
            log.info("tencentExtendSpi.taskReceive topicKey : {}, request size： {} ", task.getTopicKey(), x.size());
            Object response = tencentExtendSpi.taskReceive(request1);
            log.info("tencentExtendSpi.taskReceive topicKey : {}, response： {}", task.getTopicKey(), JSON.toJSONString(response));

            // 保存到 post_log 中
            tencentPostLogService.savePostLog(x, task.getTopicKey());
        });
        // 保存原始数据文件
        log.info("保存原始数据文件 topicKey : {},  post size： {} ---> start", task.getTopicKey(), postBeanList.size());
        savePostFile(task, postBeanList);
        log.info("保存原始数据文件 topicKey : {},  post size： {} ---> end", task.getTopicKey(), postBeanList.size());
    }

    private void savePostFile(Task task, List<TencentPostBean> postBeanList) {
        List<TencentPostExcel> excels = Lists.newArrayList();
        postBeanList.forEach(post -> {
            TencentPostExcel excel = BeanUtil.toBean(post, TencentPostExcel.class);
            excel.setProduct(JSON.toJSONString(post.getProductInfo()));
            excel.setVideoTag(JSON.toJSONString(post.getVideoTags()));
            excel.setTopic(JSON.toJSONString(post.getTopics()));
            excels.add(excel);
        });

        byte[] bytes = Downloader.writeExcel(Collections.singletonList(excels), Collections.singletonList("post"), Collections.singletonList(TencentPostExcel.class), null);
        String fileName = "tencent/" + DateUtil.formatDate(new Date(), "yyyyMMdd") + "/TOPIC-KEY-" + task.getTopicKey() + "/POST-" + DateUtil.formatDate(new Date(), "yyyyMMddHHmmss") + ".xlsx";
        tosUtil.uploadBytes(bytes, fileName);
    }
}
