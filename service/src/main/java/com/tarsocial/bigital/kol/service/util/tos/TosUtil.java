package com.tarsocial.bigital.kol.service.util.tos;


import cn.hutool.core.lang.UUID;
import com.volcengine.tos.*;
import com.volcengine.tos.auth.StaticCredentials;
import com.volcengine.tos.model.object.PutObjectFromFileInput;
import com.volcengine.tos.model.object.PutObjectFromFileOutput;
import com.volcengine.tos.model.object.PutObjectInput;
import com.volcengine.tos.model.object.PutObjectOutput;
import com.volcengine.tos.transport.TransportConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 火山云 云对象存储 工具类
 */
@Slf4j
@Component
public class TosUtil {

    @Resource
    private HuoShanOSSProperties properties;

    /**
     * 获取阿里云OSS客户端对象
     */
    public TOSV2 getTOSClient() {
        TransportConfig config = TransportConfig.builder()
                .maxRetryCount(3)
                .dnsCacheTimeMinutes(3)
                .connectTimeoutMills(properties.getConnectTimeoutMills())
                .build();
        TOSClientConfiguration configuration = TOSClientConfiguration.builder()
                .transportConfig(config)
                .region(properties.getRegion())
                .endpoint(properties.getEndpoint())
                .credentials(new StaticCredentials(properties.getAccessKey(), properties.getSecretKey()))
                .build();
        return new TOSV2ClientBuilder().build(configuration);
    }

    /**
     * 文件上传
     */
    public String uploadFile(MultipartFile multipartFile) {
        // 获取tosClient对象
        TOSV2 tosClient = getTOSClient();

        Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"));
        Path filePath = null;
        try {
            filePath = Files.createTempFile(tempDir, "", multipartFile.getOriginalFilename());
            multipartFile.transferTo(filePath.toFile());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        File file = filePath.toFile();
        try {
            PutObjectFromFileInput putObjectInput = new PutObjectFromFileInput()
                    .setBucket(properties.getBucketName()).setKey(file.getName()).setFile(file);
            PutObjectFromFileOutput output = tosClient.putObjectFromFile(putObjectInput);
            log.info("putObject succeed, object's etag is {}", output.getEtag());
            return "https://" + properties.getBucketName() + "." + properties.getEndpoint() + "/" + file.getName();

        } catch (TosClientException e) {
            // 操作失败，捕获客户端异常，一般情况是请求参数错误，此时请求并未发送
            log.info("putObject failed !!! Error Message {} ", e.getMessage());
        } catch (TosServerException e) {
            // 操作失败，捕获服务端异常，可以获取到从服务端返回的详细错误信息
            log.info("putObject failed");
            log.info("StatusCode: " + e.getStatusCode());
            log.info("Code: " + e.getCode());
            log.info("Message: " + e.getMessage());
            log.info("RequestID: " + e.getRequestID());
        } catch (Throwable t) {
            // 作为兜底捕获其他异常，一般不会执行到这里
            log.info("putObject failed");
            log.info("unexpected exception, message: " + t.getMessage());
            log.info("unexpected exception, message: " + t.getMessage());
        }
        return null;
    }


    /**
     * byte数据上传
     */
    public String uploadBytes(byte[] bytes, String fileName) {
        // 获取tosClient对象
        TOSV2 tosClient = getTOSClient();

        try {
            // 统一封装成 ByteArrayInputStream
            ByteArrayInputStream stream = new ByteArrayInputStream(bytes);
            PutObjectInput putObjectInput = new PutObjectInput()
                    .setBucket(properties.getBucketName()).setKey(fileName).setContent(stream);
            PutObjectOutput output = tosClient.putObject(putObjectInput);

            log.info("putObject succeed, object's etag is {}", output.getEtag());
            return "https://" + properties.getBucketName() + "." + properties.getEndpoint() + "/" + fileName;

        } catch (TosClientException e) {
            // 操作失败，捕获客户端异常，一般情况是请求参数错误，此时请求并未发送
            log.info("putObject failed !!! Error Message {} ", e.getMessage());
        } catch (TosServerException e) {
            // 操作失败，捕获服务端异常，可以获取到从服务端返回的详细错误信息
            log.info("putObject failed");
            log.info("StatusCode: " + e.getStatusCode());
            log.info("Code: " + e.getCode());
            log.info("Message: " + e.getMessage());
            log.info("RequestID: " + e.getRequestID());
        } catch (Throwable t) {
            // 作为兜底捕获其他异常，一般不会执行到这里
            log.info("putObject failed");
            log.info("unexpected exception, message: " + t.getMessage());
            log.info("unexpected exception, message: " + t.getMessage());
        }
        return null;
    }

    public static HuoShanOSSProperties properties() {
        HuoShanOSSProperties properties = new HuoShanOSSProperties();
        properties.setEndpoint("tos-cn-shanghai.volces.com");
        properties.setRegion("cn-shanghai");
        properties.setAccessKey("AKLTMDE1NmYxYmFhYjkxNGE3ZDg5MzFmYmY2N2MzZTZmN2Y");
        properties.setSecretKey("TmpOak9HTmtZall5WXpBNU5EWXdZamxtT0RjNE1EaGxZV0V5TXpGaE9EYw==");
        properties.setConnectTimeoutMills(10000);
        properties.setBucketName("kfhs-develop");
        return properties;
    }

    /**
     * 文件上传
     */
    public String uploadImageUrl(String url) {
        if (!StringUtils.hasLength(url) || getLastSubstringAfterSlash(url) == null) {
            return null;
        }

        // 获取tosClient对象
        TOSV2 tosClient = getTOSClient();
        try (InputStream inputStream = new BufferedInputStream(new URL(url).openStream())) {
            String fileName = UUID.fastUUID().toString(true) + "." + getLastSubstringAfterSlash(url);
            PutObjectInput putObjectInput = new PutObjectInput().setBucket(properties.getBucketName()).setKey(fileName).setContent(inputStream);
            PutObjectOutput output = tosClient.putObject(putObjectInput);
            System.out.println("putObject succeed, object's etag is " + output.getEtag());
            System.out.println("putObject succeed, object's crc64 is " + output.getHashCrc64ecma());
            log.info("putObject succeed, object's etag is {}", output.getEtag());
            return "https://" + properties.getBucketName() + "." + properties.getEndpoint() + "/" + fileName;
        } catch (IOException e) {
            log.info("putObject read data from url failed");
        } catch (TosClientException e) {
            // 操作失败，捕获客户端异常，一般情况是请求参数错误，此时请求并未发送
            log.info("putObject failed !!! Error Message {} ", e.getMessage());
        } catch (TosServerException e) {
            // 操作失败，捕获服务端异常，可以获取到从服务端返回的详细错误信息
            log.info("putObject failed");
            log.info("StatusCode: " + e.getStatusCode());
            log.info("Code: " + e.getCode());
            log.info("Message: " + e.getMessage());
            log.info("RequestID: " + e.getRequestID());
        } catch (Throwable t) {
            // 作为兜底捕获其他异常，一般不会执行到这里
            log.info("putObject failed");
            log.info("unexpected exception, message: " + t.getMessage());
            log.info("unexpected exception, message: " + t.getMessage());
        }
        return null;
    }


    public static String getLastSubstringAfterSlash(String url) {
        int lastIndex = url.lastIndexOf("/");
        if (lastIndex != -1 && lastIndex < url.length() - 1) {
            return url.substring(lastIndex + 1);
        }
        return null;
    }

    public boolean checkUrl(String url) {
        if (!StringUtils.hasLength(url)) {
            return false;
        }

        try {
            URL link = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) link.openConnection();
            conn.setRequestMethod("HEAD");
            int responseCode = conn.getResponseCode();
            conn.disconnect();
            return responseCode == HttpURLConnection.HTTP_OK;
        } catch (Exception e) {
            return false;
        }
    }
}
