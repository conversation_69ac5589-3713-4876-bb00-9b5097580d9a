package com.tarsocial.bigital.kol.service.config.global;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.locks.ReentrantLock;

/**
 * uuid 工具类
 * <AUTHOR>
 */
public class UUIDUtil {

    private static final ReentrantLock lock = new ReentrantLock();

    public static String uuid(){
        lock.lock();
        try {
            long time = System.currentTimeMillis();
            Random random = new Random(100);
            String all = time +""+random.longs(time);
            return UUID.nameUUIDFromBytes(all.getBytes()).toString().replace("-","");
        }finally {
            lock.unlock();
        }
    }

}
