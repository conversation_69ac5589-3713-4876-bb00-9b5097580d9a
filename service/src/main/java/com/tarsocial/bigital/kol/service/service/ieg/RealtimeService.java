package com.tarsocial.bigital.kol.service.service.ieg;


import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.tarsocial.bigital.kol.common.domain.dto.QuarkTaskDto;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.QuarkPostDto;
import com.tarsocial.bigital.kol.common.domain.dto.tencent.realtime.TencentUserInfoDto;
import com.tarsocial.bigital.kol.common.domain.entity.QuarkUserEntity;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentQuarkTask;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentTaskPO;
import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentUserInfo;
import com.tarsocial.bigital.kol.common.domain.enums.PlatformEnum;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.IegTaskCreateRequest;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.TencentTaskQueryRequest;
import com.tarsocial.bigital.kol.common.domain.response.ieg.TencentTaskResponse;
import com.tarsocial.bigital.kol.service.config.global.UUIDUtil;
import com.tarsocial.bigital.kol.service.constants.BusinessTypeEnum;
import com.tarsocial.bigital.kol.service.constants.PlatformTaskEnum;
import com.tarsocial.bigital.kol.service.constants.TaskTypeEnum;
import com.tarsocial.bigital.kol.service.constants.TencentTaskStatusEnum;
import com.tarsocial.bigital.kol.service.constants.TencentTaskUserStatusEnum;
import com.tarsocial.bigital.kol.service.dto.QuarkTaskConditionDto;
import com.tarsocial.bigital.kol.service.exception.BusinessException;
import com.tarsocial.bigital.kol.service.mapper.TencentTaskMapper;
import com.tarsocial.bigital.kol.service.service.tencent.ieg.TencentQuarkTaskService;
import com.tarsocial.bigital.kol.service.service.tencent.ieg.TencentUserInfoService;
import com.tarsocial.bigital.kol.service.util.DateUtil;
import com.tarsocial.dag.task.model.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RealtimeService {

    @Resource
    private TencentUserInfoService tencentUserInfoService;

    @Resource
    private QuarkTaskService quarkTaskService;

    @Resource
    private TencentTaskMapper tencentTaskMapper;

    @Resource
    private TencentQuarkTaskService tencentQuarkTaskService;

    private static final List<String> LIVE_PLATFORM_LIST = Lists.newArrayList(PlatformEnum.DOUYIN.getCode(), PlatformEnum.KUAISHOU.getCode());


    public Long shortVideoTask(IegTaskCreateRequest request) {
        return tencentTask(request, BusinessTypeEnum.REALTIME_KOL);
    }

    public Long liveTask(IegTaskCreateRequest request) {
        return tencentTask(request, BusinessTypeEnum.REALTIME_LIVE);
    }


    /**
     * 创建腾讯主任务
     *
     * @param userIds
     * @param platform
     * @param businessTypeEnum
     * @return
     */
    public TencentTaskPO saveTencentTask(BusinessTypeEnum businessTypeEnum, String platform, List<String> userIds) {
        TencentTaskPO taskPO = new TencentTaskPO();
        taskPO.setUserId(JSON.toJSONString(userIds));
        taskPO.setPlatform(platform);
        taskPO.setBusinessType(businessTypeEnum.getCode().toString());
        taskPO.setTencentBatchId(UUIDUtil.uuid());
        tencentTaskMapper.insert(taskPO);
        return taskPO;
    }


    public Long tencentTask(IegTaskCreateRequest request, BusinessTypeEnum businessTypeEnum) {
        List<String> userIds = request.getIds();
        //直播只支持抖音 快手
        if (BusinessTypeEnum.REALTIME_LIVE.getCode().equals(businessTypeEnum.getCode()) && !LIVE_PLATFORM_LIST.contains(request.getPlatform())) {
            throw new BusinessException("实时直播任务 不支持该平台");
        }
        if (CollectionUtils.isEmpty(userIds)) {
            throw new BusinessException("用户ID数量不能为空！！！");
        }
        if (!CollectionUtils.isEmpty(userIds) && userIds.size() > 10) {
            throw new BusinessException("用户ID数量不能超过10个！！！");
        }

        TencentTaskPO taskPO = saveTencentTask(businessTypeEnum, request.getPlatform(), userIds);

        //查询用户信息
        LambdaQueryWrapper<TencentUserInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TencentUserInfo::getUserId, userIds);
        List<TencentUserInfo> userList = tencentUserInfoService.list(wrapper);

        //保存新用户
        List<String> hasList = userList.stream().map(TencentUserInfo::getUserId).collect(Collectors.toList());
        List<String> saveUserIds = new ArrayList<>(userIds);
        saveUserIds.removeAll(hasList);
        tencentUserInfoService.saveBatch(saveUserIds.stream().map(userId -> {
            TencentUserInfo tencentIegUserInfo = new TencentUserInfo();
            tencentIegUserInfo.setUserId(userId);
            tencentIegUserInfo.setPlatform(request.getPlatform());
            return tencentIegUserInfo;
        }).collect(Collectors.toList()));


        preTask(businessTypeEnum, request.getPlatform(), userIds, taskPO.getId());

        if (PlatformEnum.DOUYIN.getCode().equals(request.getPlatform())) {
            postTask(businessTypeEnum, taskPO, userList);
        }

        return taskPO.getId();
    }


    /**
     * 创建夸克前置任务
     *
     * @param businessTypeEnum
     * @param platform
     * @param userIds
     * @param tencentTaskId
     */
    public void preTask(BusinessTypeEnum businessTypeEnum, String platform, List<String> userIds, Long tencentTaskId) {
        List<TencentQuarkTask> list = new ArrayList<>();

        //直播
        if (BusinessTypeEnum.REALTIME_LIVE.equals(businessTypeEnum)) {
            if (PlatformTaskEnum.KUAI_SHOU.getName().equals(platform)) {
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.KS_LIVE_USER, userIds, businessTypeEnum.getCode().toString()));
            } else {
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.DY_LIVE_PLAZA, userIds, businessTypeEnum.getCode().toString()));
            }
        }

        //短视频达人
        if (BusinessTypeEnum.REALTIME_KOL.equals(businessTypeEnum)) {
            if (PlatformTaskEnum.DOU_YIN.getName().equals(platform)) {
                List<QuarkUserEntity> quarkUserInfos = quarkTaskService.queryUser(userIds, PlatformTaskEnum.DOU_YIN.getName());

                //有kw_secUid 创建 更新用户信息任务
                Map<String, String> userUidMap = quarkUserInfos.stream().filter(user -> StringUtils.isNotEmpty(user.getSecUid())).collect(Collectors.toMap(
                        QuarkUserEntity::getUserId,
                        QuarkUserEntity::getSecUid,
                        (oldValue, newValue) -> newValue
                ));
                if (!CollectionUtils.isEmpty(userUidMap)) {
                    list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.DY_USER, userUidMap, businessTypeEnum.getCode().toString()));
                }


                //没有kolId的用户，创建 星图录入新用户  任务
                List<String> userList = quarkUserInfos.stream()
                        .filter(user -> user.getKolId() == null || user.getKolId().isEmpty())
                        .map(QuarkUserEntity::getUserId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(userList)) {
                    list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.DY_USER_NEW, userList, businessTypeEnum.getCode().toString()));
                }
            } else if (PlatformTaskEnum.BILIBILI.getName().equals(platform)) {
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.BILI_USER, userIds, businessTypeEnum.getCode().toString()));
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.BILI_KOL, userIds, businessTypeEnum.getCode().toString()));
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.BILI_HUAHUO, userIds, businessTypeEnum.getCode().toString()));
            } else if (PlatformTaskEnum.KUAI_SHOU.getName().equals(platform)) {
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.KS_KOL, userIds, businessTypeEnum.getCode().toString()));
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.KS_USER, userIds, businessTypeEnum.getCode().toString()));
            } else if (PlatformTaskEnum.RED.getName().equals(platform)) {
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.RED_USER, userIds, businessTypeEnum.getCode().toString()));
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.RED_PAID, userIds, businessTypeEnum.getCode().toString()));
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.RED_KOL, userIds, businessTypeEnum.getCode().toString()));
            } else if (PlatformTaskEnum.WEIBO.getName().equals(platform)) {
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.WEIBO_USER, userIds, businessTypeEnum.getCode().toString()));
                list.addAll(createQuarkTask(tencentTaskId, TaskTypeEnum.WEIBO_TASK_USER, userIds, businessTypeEnum.getCode().toString()));
            }
        }
        tencentQuarkTaskService.saveBatch(list);
    }


    /**
     * 创建后置任务       目前只有抖音平台需要创建后置任务
     *
     * @param businessTypeEnum
     * @param taskPO
     * @param userList
     */
    public void postTask(BusinessTypeEnum businessTypeEnum, TencentTaskPO taskPO, List<TencentUserInfo> userList) {
        List<TencentQuarkTask> list = new ArrayList<>();
        //创建后置任务  有kolId直接创建后置任务，无kolId待后续创建
        // 使用 partitioningBy 按 kolId 是否非空拆分
        Map<Boolean, List<TencentUserInfo>> partitioned = userList.stream()
                .collect(Collectors.partitioningBy(user -> user.getKolId() != null));

        // 有kolId 创建后置任务
        List<TencentUserInfo> kolList = partitioned.get(true); // kolId 非空的列表

        Map<String, String> userMap = kolList.stream()
                .collect(Collectors.toMap(
                        TencentUserInfo::getUserId,
                        TencentUserInfo::getKolId,
                        (oldValue, newValue) -> newValue // 重复时用新值覆盖旧值
                ));

        if (BusinessTypeEnum.REALTIME_LIVE.equals(businessTypeEnum) && !CollectionUtils.isEmpty(userMap)) {
            list.addAll(createQuarkTask(taskPO.getId(), TaskTypeEnum.DY_LIVE_FANS, userMap, BusinessTypeEnum.REALTIME_LIVE.getCode().toString()));
            list.addAll(createQuarkTask(taskPO.getId(), TaskTypeEnum.DY_LIVE_SPECTATOR, userMap, BusinessTypeEnum.REALTIME_LIVE.getCode().toString()));
            list.addAll(createQuarkTask(taskPO.getId(), TaskTypeEnum.DY_LIVE_DATA, userMap, BusinessTypeEnum.REALTIME_LIVE.getCode().toString()));
            list.addAll(createQuarkTask(taskPO.getId(), TaskTypeEnum.DY_LIVE_LIST, userMap, BusinessTypeEnum.REALTIME_LIVE.getCode().toString()));
        } else if (BusinessTypeEnum.REALTIME_KOL.equals(businessTypeEnum) && !CollectionUtils.isEmpty(userMap)) {
            list.addAll(createDYKolQuarkTask(taskPO.getId(), TaskTypeEnum.DY_KOL, userMap, BusinessTypeEnum.REALTIME_KOL.getCode().toString(), get30dUser()));
            list.addAll(createDYKolQuarkTask(taskPO.getId(), TaskTypeEnum.DY_KOL, userMap, BusinessTypeEnum.REALTIME_KOL.getCode().toString(), get30dStar()));
            list.addAll(createDYKolQuarkTask(taskPO.getId(), TaskTypeEnum.DY_KOL, userMap, BusinessTypeEnum.REALTIME_KOL.getCode().toString(), get90dUser()));
        }


        //记录无kolId用户，后续创建
        List<TencentUserInfo> postList = partitioned.get(false);
        if (!CollectionUtils.isEmpty(postList)) {
            List<String> collect = postList.stream().map(TencentUserInfo::getUserId).collect(Collectors.toList());
            taskPO.setPostPosition(JSON.toJSONString(collect));
            tencentTaskMapper.updateById(taskPO);
        }
        if (!CollectionUtils.isEmpty(list)) {
            tencentQuarkTaskService.saveBatch(list);
        }
    }


    /**
     * 根据userId创建的任务
     *
     * @param tencentTaskId
     * @param taskType
     * @param userIds
     * @param businessType
     * @return
     */
    public List<TencentQuarkTask> createQuarkTask(Long tencentTaskId, TaskTypeEnum taskType, List<String> userIds, String businessType) {
        List<TencentQuarkTask> result = new ArrayList<>(userIds.size());
        String taskName = quarkTaskService.getTaskName(taskType, userIds);
        Long taskId = quarkTaskService.createTask(taskType, userIds, taskName);

        userIds.forEach(userId -> {
            TencentQuarkTask tencentQuarkTask = new TencentQuarkTask();
            tencentQuarkTask.setUserId(userId);
            tencentQuarkTask.setPlatform(taskType.getPlatformCode());
            tencentQuarkTask.setTencentTaskId(tencentTaskId);
            tencentQuarkTask.setQuarkTaskType(taskType.getName());
            tencentQuarkTask.setQuarkTaskId(taskId);
            tencentQuarkTask.setQuarkTaskStatus(TaskStatusEnum.PROGRESS.name());
            tencentQuarkTask.setQuarkSubTaskId(userId);
            tencentQuarkTask.setQuarkTaskName(taskName);
            tencentQuarkTask.setBusinessType(businessType);
            result.add(tencentQuarkTask);
        });
        return result;
    }


    public List<TencentQuarkTask> createDYKolQuarkTask(Long tencentTaskId, TaskTypeEnum taskType, Map<String, String> userMap, String businessType, QuarkTaskConditionDto conditionDto) {
        List<TencentQuarkTask> result = new ArrayList<>(userMap.size());
        List<String> subTaskId = new ArrayList<>(userMap.values());

        String taskName = quarkTaskService.getTaskName(taskType, subTaskId);
        Long taskId = quarkTaskService.createDYKOLTask(taskType, subTaskId, taskName, conditionDto);

        userMap.forEach((userId, kolId) -> {
            TencentQuarkTask tencentQuarkTask = new TencentQuarkTask();
            tencentQuarkTask.setUserId(userId);
            tencentQuarkTask.setKolId(kolId);
            tencentQuarkTask.setPlatform(taskType.getPlatformCode());
            tencentQuarkTask.setTencentTaskId(tencentTaskId);
            tencentQuarkTask.setQuarkTaskType(taskType.getName());
            tencentQuarkTask.setQuarkTaskId(taskId);
            tencentQuarkTask.setQuarkTaskStatus(TaskStatusEnum.PROGRESS.name());
            tencentQuarkTask.setQuarkSubTaskId(kolId);
            tencentQuarkTask.setQuarkTaskName(taskName);
            tencentQuarkTask.setBusinessType(businessType);
            result.add(tencentQuarkTask);
        });
        return result;
    }


    /**
     * 有些任务需要根据  kolId 创建
     *
     * @param tencentTaskId
     * @param taskType
     * @param userMap
     * @param businessType
     * @return
     */
    public List<TencentQuarkTask> createQuarkTask(Long tencentTaskId, TaskTypeEnum taskType, Map<String, String> userMap, String businessType) {
        List<TencentQuarkTask> result = new ArrayList<>(userMap.size());
        List<String> subTaskId = new ArrayList<>(userMap.values());

        String taskName = quarkTaskService.getTaskName(taskType, subTaskId);
        Long taskId = quarkTaskService.createTask(taskType, subTaskId, taskName);

        userMap.forEach((userId, kolId) -> {
            TencentQuarkTask tencentQuarkTask = new TencentQuarkTask();
            tencentQuarkTask.setUserId(userId);
            tencentQuarkTask.setKolId(kolId);
            tencentQuarkTask.setPlatform(taskType.getPlatformCode());
            tencentQuarkTask.setTencentTaskId(tencentTaskId);
            tencentQuarkTask.setQuarkTaskType(taskType.getName());
            tencentQuarkTask.setQuarkTaskId(taskId);
            tencentQuarkTask.setQuarkTaskStatus(TaskStatusEnum.PROGRESS.name());
            tencentQuarkTask.setQuarkSubTaskId(kolId);
            tencentQuarkTask.setQuarkTaskName(taskName);
            tencentQuarkTask.setBusinessType(businessType);
            result.add(tencentQuarkTask);
        });
        return result;
    }


    /**
     * 查询直播任务
     *
     * @param request
     * @return
     */
    public TencentTaskResponse queryLiveTask(TencentTaskQueryRequest request) {
        TencentTaskResponse result = new TencentTaskResponse();
        TencentTaskPO taskPO = tencentTaskMapper.selectById(request.getTaskId());
        result.setTaskStatus(taskPO.getTaskStatus());
        result.setPlatform(taskPO.getPlatform());
        //未完成
        if (TencentTaskStatusEnum.RUNNING.getCode().equals(taskPO.getTaskStatus())) {
            return result;
        }

        Map<String, TencentUserInfoDto> userResult = new HashMap<>(16);

        List<String> userIds = request.getIds();

        LambdaQueryWrapper<TencentQuarkTask> quarkTaskWrapper = new LambdaQueryWrapper<>();
        quarkTaskWrapper.eq(TencentQuarkTask::getTencentTaskId, taskPO.getId());
        quarkTaskWrapper.in(TencentQuarkTask::getUserId, userIds);
        List<TencentQuarkTask> list = tencentQuarkTaskService.list(quarkTaskWrapper);
        Map<String, List<TencentQuarkTask>> userMap = list.stream().collect(Collectors.groupingBy(TencentQuarkTask::getUserId));


        Map<String, Date> userDateMap = new HashMap<>(16);

        /**
         * 判断用户状态
         *      1.全部/部分任务 失败->
         *          1.1 ->查询用户信息
         *              1.1.1-> 用户存在    ->  STATUS:任务失败
         *              1.1.2-> 用户不存在   ->  STATUS:无效userId
         *      2.全部超时->    STATUS:超时
         *      3.有其中一个任务成功-> STATUS:成功
         */
        userIds.forEach(userId -> {
            TencentUserInfoDto dto = new TencentUserInfoDto();
            dto.setUserId(userId);
            String status;
            List<TencentQuarkTask> taskList = userMap.get(userId);
            if (taskList == null) {
                status = TencentTaskUserStatusEnum.FAIL.getCode();
            } else {
                if (taskList.stream().anyMatch(task -> TaskStatusEnum.FINISH.name().equals(task.getQuarkTaskStatus()))) {
                    status = TencentTaskUserStatusEnum.SUCCESS.getCode();

                    Optional<TencentQuarkTask> finishedTask = taskList.stream()
                            .filter(task -> TaskStatusEnum.FINISH.name().equals(task.getQuarkTaskStatus()))
                            .findAny();
                    TencentQuarkTask task = finishedTask.get();
                    userDateMap.put(userId, task.getUpdateTime());
                } else if (taskList.stream().allMatch(item -> TaskStatusEnum.CANCEL.name().equals(item.getQuarkTaskStatus()))) {
                    status = TencentTaskUserStatusEnum.TIMEOUT.getCode();
                } else if (taskList.stream().anyMatch(task -> TencentTaskUserStatusEnum.NOTFOUND.getCode().equals(task.getQuarkTaskStatus()))) {
                    status = TencentTaskUserStatusEnum.NOTFOUND.getCode();
                } else {
                    status = TencentTaskUserStatusEnum.FAIL.getCode();
                }
            }
            dto.setStatus(status);
            userResult.put(userId, dto);
        });


        List<String> successIds = userResult.values().stream()
                .filter(task -> TencentTaskUserStatusEnum.SUCCESS.getCode().equals(task.getStatus()))
                .map(TencentUserInfoDto::getUserId)
                .collect(Collectors.toList());

        Map<String, List<QuarkPostDto>> userPostMap = new HashMap<>();
        Map<String, QuarkUserEntity> quarkMap = quarkTaskService.queryUser(successIds, taskPO.getPlatform()).stream().collect(Collectors.toMap(QuarkUserEntity::getUserId, Function.identity()));
        List<QuarkPostDto> quarkPostDtos = quarkTaskService.queryPost(successIds, taskPO.getPlatform());
        if (!CollectionUtils.isEmpty(quarkPostDtos)) {
            userPostMap.putAll(quarkPostDtos.stream().collect(Collectors.groupingBy(QuarkPostDto::getUserId)));
        }

        successIds.forEach(userId -> {
            TencentUserInfoDto dto = userResult.get(userId);
            QuarkUserEntity quarkUserInfo = quarkMap.get(userId);
            if (BusinessTypeEnum.REALTIME_KOL.getCode().toString().equals(taskPO.getBusinessType())) {
                dto.setDetail(TencentConvert.QuarkUserInfoConvert(quarkUserInfo, taskPO.getPlatform(), DateUtil.formatLocalDateTime(userDateMap.get(userId)), userPostMap.get(userId)));
            } else if (BusinessTypeEnum.REALTIME_LIVE.getCode().toString().equals(taskPO.getBusinessType())) {
                dto.setDetail(TencentConvert.QuarkUserInfoConvert(quarkUserInfo, taskPO.getPlatform(), DateUtil.formatLocalDateTime(userDateMap.get(userId))));
            }
        });

        result.setUserList(new ArrayList<>(userResult.values()));
        return result;
    }


    public void fullDetail(String platform, List<String> userIds) {

    }

    public void perMinuteJOB() {
        LambdaQueryWrapper<TencentTaskPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TencentTaskPO::getTaskStatus, TencentTaskStatusEnum.RUNNING.getCode());
        List<TencentTaskPO> taskPOList = tencentTaskMapper.selectList(wrapper);
        taskPOList.forEach(taskPO -> processOne(taskPO));
    }

    private void processOne(TencentTaskPO taskPO) {
        //查询正在进行中的任务
        LambdaQueryWrapper<TencentQuarkTask> quarkTaskWrapper = new LambdaQueryWrapper<>();
        quarkTaskWrapper.eq(TencentQuarkTask::getTencentTaskId, taskPO.getId());
        quarkTaskWrapper.eq(TencentQuarkTask::getQuarkTaskStatus, TaskStatusEnum.PROGRESS.name());
        List<TencentQuarkTask> list = tencentQuarkTaskService.list(quarkTaskWrapper);

        //是否超时
        boolean isTimeout = isOverTenMinutes(taskPO.getCreatedTime());

        try {
            processQuarkStatus(list, isTimeout);
        } catch (Exception e) {
            log.error("更新夸克任务状态 ERROR ->{}", e.getMessage(), e);
        }

        //任务完成  超时 or  没有后续任务 and 进行中的任务为空
        if (isTimeout || (StringUtils.isEmpty(taskPO.getPostPosition()) && CollectionUtil.isEmpty(list))) {
            taskPO.setTaskStatus(TencentTaskStatusEnum.FINISH.getCode());
            tencentTaskMapper.updateById(taskPO);
            return;
        }

        //需要创建后置任务
        processCreateNext(taskPO);
    }


    /**
     * 更新任务状态
     * 超时取消
     *
     * @param taskList
     */
    private void processQuarkStatus(List<TencentQuarkTask> taskList, boolean isTimeout) {
        Set<Long> taskIdSet = taskList.stream().map(TencentQuarkTask::getQuarkTaskId).collect(Collectors.toSet());
        Map<Long, Map<String, QuarkTaskDto>> taskResult = new HashMap<>();
        Set<Long> cancelList = new HashSet<>();

        taskIdSet.forEach(quarkTaskId -> {
            Map<String, QuarkTaskDto> taskDetailMap = quarkTaskService.queryTask(quarkTaskId);
            taskResult.put(quarkTaskId, taskDetailMap);
        });

        taskList.forEach(task -> {
            task.setUpdateTime(new Date());
            Map<String, QuarkTaskDto> details = taskResult.get(task.getQuarkTaskId());
            if (details != null) {
                QuarkTaskDto quarkTaskDto = details.get(task.getQuarkSubTaskId());
                if (quarkTaskDto != null) {
                    task.setQuarkTaskStatus(quarkTaskDto.getStatus());
                    task.setQuarkErrorDetail(quarkTaskDto.getErrorMsg());
                }
            }

            //查询无效达人
            if (TaskStatusEnum.FAIL.name().equals(task.getQuarkTaskStatus())) {
                List<QuarkUserEntity> userInfos = quarkTaskService.queryUser(Lists.newArrayList(task.getUserId()), task.getPlatform());
                if (CollectionUtil.isEmpty(userInfos)) {
                    task.setQuarkTaskStatus(TencentTaskUserStatusEnum.NOTFOUND.getCode());
                }
            }

            //任务超时
            if (TaskStatusEnum.PROGRESS.name().equals(task.getQuarkTaskStatus()) && isTimeout) {
                cancelList.add(task.getQuarkTaskId());
                task.setQuarkTaskStatus(TaskStatusEnum.CANCEL.name());
            }
        });

        //超时任务取消
        if (!CollectionUtils.isEmpty(cancelList)) {
            quarkTaskService.cancelTask(new ArrayList<>(cancelList));
        }
        tencentQuarkTaskService.saveOrUpdateBatch(taskList);
    }


    /**
     * 创建后置任务
     */
    private void processCreateNext(TencentTaskPO taskPO) {
        if (StringUtils.isEmpty(taskPO.getPostPosition())) {
            return;
        }

        //需要创建后置任务的userId
        List<String> postUserId = JSON.parseArray(taskPO.getPostPosition(), String.class);

        if (CollectionUtils.isEmpty(postUserId)) {
            return;
        }

        //查询用户信息Info
        LambdaQueryWrapper<TencentUserInfo> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.in(TencentUserInfo::getUserId, postUserId);
        List<TencentUserInfo> userList = tencentUserInfoService.list(userWrapper);
        Map<String, TencentUserInfo> userMap = userList.stream()
                .collect(Collectors.toMap(
                        TencentUserInfo::getUserId, // 键：userId 字段
                        Function.identity()         // 值：对象本身
                ));


        List<TencentUserInfo> hasKol = new ArrayList<>(userList.size());
        HashMap<String, String> userKolMap = new HashMap<>();

        //查询kolId
        List<QuarkUserEntity> quarkUserInfoList = quarkTaskService.queryUser(postUserId, taskPO.getPlatform());
        quarkUserInfoList.forEach(info -> {
            String kolId = info.getKolId();
            if (StringUtils.isNotEmpty(kolId)) {
                String userId = info.getUserId();
                TencentUserInfo tencentUserInfo = userMap.get(userId);
                tencentUserInfo.setKolId(kolId);
                hasKol.add(tencentUserInfo);
                userKolMap.put(userId, kolId);
            }
        });

        if (CollectionUtil.isEmpty(hasKol)) {
            return;
        }

        //更新用户信息
        tencentUserInfoService.saveOrUpdateBatch(hasKol);
        //创建后置任务
        List<TencentQuarkTask> createList = new ArrayList<>();

        if (BusinessTypeEnum.REALTIME_LIVE.getCode().toString().equals(taskPO.getBusinessType()) && !CollectionUtils.isEmpty(userKolMap)) {
            createList.addAll(createQuarkTask(taskPO.getId(), TaskTypeEnum.DY_LIVE_FANS, userKolMap, BusinessTypeEnum.REALTIME_LIVE.getCode().toString()));
            createList.addAll(createQuarkTask(taskPO.getId(), TaskTypeEnum.DY_LIVE_SPECTATOR, userKolMap, BusinessTypeEnum.REALTIME_LIVE.getCode().toString()));
            createList.addAll(createQuarkTask(taskPO.getId(), TaskTypeEnum.DY_LIVE_DATA, userKolMap, BusinessTypeEnum.REALTIME_LIVE.getCode().toString()));
            createList.addAll(createQuarkTask(taskPO.getId(), TaskTypeEnum.DY_LIVE_LIST, userKolMap, BusinessTypeEnum.REALTIME_LIVE.getCode().toString()));
        } else if (BusinessTypeEnum.REALTIME_KOL.getCode().toString().equals(taskPO.getBusinessType()) && !CollectionUtils.isEmpty(userKolMap)) {
            createList.addAll(createDYKolQuarkTask(taskPO.getId(), TaskTypeEnum.DY_KOL, userKolMap, BusinessTypeEnum.REALTIME_KOL.getCode().toString(), get30dUser()));
            createList.addAll(createDYKolQuarkTask(taskPO.getId(), TaskTypeEnum.DY_KOL, userKolMap, BusinessTypeEnum.REALTIME_KOL.getCode().toString(), get30dStar()));
            createList.addAll(createDYKolQuarkTask(taskPO.getId(), TaskTypeEnum.DY_KOL, userKolMap, BusinessTypeEnum.REALTIME_KOL.getCode().toString(), get90dUser()));
        }
        if (!CollectionUtils.isEmpty(createList)) {
            tencentQuarkTaskService.saveBatch(createList);
        }

        //更新待创建列表
        postUserId.removeAll(userKolMap.keySet());
        taskPO.setPostPosition(JSON.toJSONString(postUserId));
        tencentTaskMapper.updateById(taskPO);
    }


    /**
     * 判断大于10分钟
     *
     * @param createdTime
     * @return
     */
    public static boolean isOverTenMinutes(Date createdTime) {
        Instant createdInstant = createdTime.toInstant();
        Instant nowInstant = Instant.now();
        Duration duration = Duration.between(createdInstant, nowInstant);
        return duration.toMinutes() > 10; // 直接比较分钟数
    }

    public String getTaskName(TaskTypeEnum taskType, List<String> ids) {
        return taskType.getDesc() + "-" + ids.get(0);
    }


    /**
     * 30天个人
     *
     * @return
     */
    private QuarkTaskConditionDto get30dUser() {
        QuarkTaskConditionDto dto = new QuarkTaskConditionDto();
        dto.setRange(2);
        dto.setType(1);
        dto.setOnly_assign(false);
        dto.setFlow_type(0);
        return dto;
    }

    /**
     * 90天个人
     *
     * @return
     */
    private QuarkTaskConditionDto get90dUser() {
        QuarkTaskConditionDto dto = new QuarkTaskConditionDto();
        dto.setRange(3);
        dto.setType(1);
        dto.setOnly_assign(false);
        dto.setFlow_type(0);
        return dto;
    }

    /**
     * 30天星图
     *
     * @return
     */
    private QuarkTaskConditionDto get30dStar() {
        QuarkTaskConditionDto dto = new QuarkTaskConditionDto();
        dto.setRange(2);
        dto.setType(2);
        dto.setOnly_assign(false);
        dto.setFlow_type(0);
        return dto;
    }


}
