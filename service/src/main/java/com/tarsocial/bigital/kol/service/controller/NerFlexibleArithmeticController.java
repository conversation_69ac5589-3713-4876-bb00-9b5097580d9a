package com.tarsocial.bigital.kol.service.controller;

import com.tarsocial.bigital.kol.common.domain.entity.NerFlexibleEntity;
import com.tarsocial.bigital.kol.common.domain.request.NerFlexibleCalculateRequest;
import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.service.service.NerFlexibleService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;


@RestController
@AllArgsConstructor
@RequestMapping("/ner/flexible")
public class NerFlexibleArithmeticController {

    private NerFlexibleService nerFlexibleService;

    @RequestMapping("/calculate")
    public BaseResponse<Boolean> calculate(@RequestParam("file") MultipartFile file, NerFlexibleCalculateRequest req) {
        nerFlexibleService.calculate(file, req);
        return new BaseResponse<>(true);
    }

    @GetMapping("/list")
    public BaseResponse<List<NerFlexibleEntity>> list() {
        return new BaseResponse<>(nerFlexibleService.list());
    }

    @GetMapping("/dict")
    public BaseResponse<Map<String, List<String>>> dict() {
        return new BaseResponse<>(nerFlexibleService.dict());
    }
}
