package com.tarsocial.bigital.kol.service.controller.tencent.ieg;

import com.tarsocial.bigital.kol.common.domain.dto.tencent.PublicPriceUserDto;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.PublicPriceTaskCreateRequest;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.PublicPriceTaskQueryRequest;
import com.tarsocial.bigital.kol.common.domain.response.BaseResponse;
import com.tarsocial.bigital.kol.service.service.ieg.IegProcessService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 刊例价实时
 */
@RestController
@RequestMapping("/api/ieg/live/price")
public class PublicPriceLiveController {

    @Resource
    private IegProcessService iegProcessService;


    @PostMapping("/task/create")
    public BaseResponse<Long> publicPriceCreateTask(@RequestBody PublicPriceTaskCreateRequest request) {
        return new BaseResponse<>(iegProcessService.publicPriceCreateTask(request));
    }

    @PostMapping("/task/query")
    public BaseResponse<List<PublicPriceUserDto>> queryUser(@RequestBody PublicPriceTaskQueryRequest request) {
        return new BaseResponse<>(iegProcessService.queryUser(request.getTaskId()));
    }

}
