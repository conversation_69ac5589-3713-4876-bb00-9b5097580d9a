package com.tarsocial.bigital.kol.service.config;

import co.elastic.clients.elasticsearch.ElasticsearchAsyncClient;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * gs es
 *
 * <AUTHOR>
 * @since 2024/4/12
 */
@AllArgsConstructor
@Configuration
public class GsEsConfiguration {

    @Resource
    private final GsEsProperties properties;

    @Bean(name = "pgElasticsearchTransport")
    public ElasticsearchTransport elasticsearchTransport() {
        // https://www.elastic.co/guide/en/elasticsearch/client/java-api-client/7.17/_basic_authentication.html
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(properties.getUsername(), properties.getPassword()));

        // Create the low-level client
        RestClient restClient = RestClient
                .builder(new HttpHost(properties.getHost()))
                .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
                .setRequestConfigCallback(b -> b
                        .setConnectTimeout(5000)
                        .setSocketTimeout(300000) // 300秒
                )
                .build();

        // Create the transport with a Jackson mapper
        ObjectMapper objectMapper = MiddleEsConfiguration.handleEsConf();


        return new RestClientTransport(restClient, new JacksonJsonpMapper(objectMapper));
    }

    @Bean(name = "pgElasticsearchClient")
    public ElasticsearchClient elasticsearchClient(@Qualifier("pgElasticsearchTransport") ElasticsearchTransport transport) {
        // And create the API client
        return new ElasticsearchClient(transport);
    }

    @Bean(name = "pgElasticsearchAsyncClient")
    public ElasticsearchAsyncClient elasticsearchAsyncClient(@Qualifier("pgElasticsearchTransport") ElasticsearchTransport transport) {
        return new ElasticsearchAsyncClient(transport);
    }

    @Bean(destroyMethod = "close", name = "clientHighLevel")
    public RestHighLevelClient initRestClientQuark() {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(properties.getUsername(), properties.getPassword()));
        RestClientBuilder builder = RestClient.builder(HttpHost.create(properties.getHost()))
                .setRequestConfigCallback(requestConfigBuilder ->
                        requestConfigBuilder.setConnectTimeout(5000)
                                .setSocketTimeout(300000)
                                .setConnectionRequestTimeout(10000)
                ).setHttpClientConfigCallback(httpClientBuilder -> {
                    httpClientBuilder.disableAuthCaching();
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                });

        return new RestHighLevelClient(builder);
    }

}
