package com.tarsocial.bigital.kol.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/4/12
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dag")
public class QuarkProperties {

    private String apiUrl;

    private String organizationId;

    private String username;

    private String password;

    private String authUrl;


}
