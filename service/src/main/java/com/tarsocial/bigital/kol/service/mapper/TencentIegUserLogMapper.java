package com.tarsocial.bigital.kol.service.mapper;

import com.tarsocial.bigital.kol.common.domain.entity.tencent.ieg.TencentIegUserLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TencentIegUserLogMapper extends com.baomidou.mybatisplus.core.mapper.BaseMapper<TencentIegUserLog> {
    /**
     * 批量更新
     *
     * @param list 数据集合
     * @return 更新数
     */
    int batchSave(@Param("list") List<TencentIegUserLog> list);

    /**
     * 更新用户状态
     *
     * @param log log
     * @return int
     */
    int updateTaskStatus(@Param("log") TencentIegUserLog log);
}
