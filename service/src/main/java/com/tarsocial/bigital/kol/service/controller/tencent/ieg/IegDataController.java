package com.tarsocial.bigital.kol.service.controller.tencent.ieg;


import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.SendUserIdRequest;
import com.tarsocial.bigital.kol.common.domain.request.tencent.ieg.StarUserRequest;
import com.tarsocial.bigital.kol.common.domain.response.ieg.IegBaseResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/ieg")
public class IegDataController {

    @PostMapping("/send_user_ids")
    public IegBaseResponse<Object> sendUserIds(@RequestBody SendUserIdRequest request) {
        // 提交任务
        return new IegBaseResponse<>(null);
    }

    @PostMapping("/get_star_user")
    public IegBaseResponse<Object> getStarUser(@RequestBody StarUserRequest request) {
        // 提交任务
        return new IegBaseResponse<>(null);
    }
}
