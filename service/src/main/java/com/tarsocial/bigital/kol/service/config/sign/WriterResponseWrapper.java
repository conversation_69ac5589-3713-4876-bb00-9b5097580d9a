package com.tarsocial.bigital.kol.service.config.sign;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;

@Slf4j
public class WriterResponseWrapper extends HttpServletResponseWrapper {

    public ServletOutputStream output;

    public WriterResponseWrapper(HttpServletResponse response) {
        super(response);
        try {
            this.output = response.getOutputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        return new PrintWriter(new OutputStreamWriter(this.getOutputStream()));
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        return new ServletOutputStream() {
            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setWriteListener(WriteListener listener) {
                log.info(listener.toString());
            }

            @Override
            public void write(int b) throws IOException {
                output.write(b);
            }
        };
    }
}

