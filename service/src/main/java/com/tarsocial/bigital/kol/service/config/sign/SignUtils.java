package com.tarsocial.bigital.kol.service.config.sign;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class SignUtils {

    private SignUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static SignEntity dislodge(HttpServletRequest request) {
        SignEntity sign = new SignEntity();
        String appId = request.getHeader(SignEntity.Fields.appId);
        sign.setAppId(Objects.nonNull(appId) ? Long.parseLong(appId) : null);
        sign.setSign(request.getHeader(SignEntity.Fields.sign));
        if (!sign.usedSignature()) {
            return sign;
        }
        sign.setNonce(request.getHeader(SignEntity.Fields.nonce));
        sign.setTimestamp(request.getHeader(SignEntity.Fields.timestamp));
        sign.setSignModel(SignModel.SHA_256);
        try {
            Map<String, String[]> serial = serial(readMap(request.getParameterMap()), readBody(request));
            log.info("params info ：{}", JSON.toJSONString(serial));
            sign.setParams(serial);
        } catch (IOException e) {
            throw new ServiceException("read request body exception ");
        }
        return sign;
    }

    public static Map<String, String[]> serial(Map<String, String[]> params, String body) {
        if (CollectionUtils.isEmpty(params)) {
            params = new HashMap<>(1);
        }
        if (StringUtils.isNotEmpty(body)) {
            params.put("body", new String[]{body});
        }
        return params;
    }

    public static Map<String, String[]> readMap(Map<String, String[]> params) {
        return new HashMap<>(params);
    }

    public static String readBody(HttpServletRequest request) throws IOException {
        ReadRequestWrapper wrapper = new ReadRequestWrapper(request);
        if (StringUtils.isEmpty(wrapper.body)) {
            return null;
        }
        return wrapper.body.replace(" ", "");
    }

    public static String readBody(ReadRequestWrapper request) throws IOException {
        ReadRequestWrapper wrapper = new ReadRequestWrapper(request);
        if (StringUtils.isEmpty(wrapper.body)) {
            return null;
        }
        return wrapper.body.replace(" ", "");
    }

}
