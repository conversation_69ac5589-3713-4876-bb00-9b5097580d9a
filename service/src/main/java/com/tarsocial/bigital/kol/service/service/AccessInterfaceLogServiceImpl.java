package com.tarsocial.bigital.kol.service.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tarsocial.bigital.kol.common.domain.entity.AccessInterfaceLog;
import com.tarsocial.bigital.kol.service.config.sign.ReadRequestWrapper;
import com.tarsocial.bigital.kol.service.mapper.AccessInterfaceLogMapper;
import com.tarsocial.bigital.kol.service.util.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;


@Slf4j
@Service
public class AccessInterfaceLogServiceImpl extends ServiceImpl<AccessInterfaceLogMapper, AccessInterfaceLog> implements AccessInterfaceLogService {

    @Async
    public AccessInterfaceLog record(HttpServletRequest request, HttpServletResponse response, Exception ex) {
        try {

            AccessInterfaceLog log = AccessInterfaceLog.log(request.getHeader("appClient")
                    , request.getPathInfo()
                    , request.getRequestURI()
                    , request.getRequestURL().toString()
                    , RequestUtils.getIpAddress(request));
            if (request instanceof ReadRequestWrapper) {
                log.setBody(((ReadRequestWrapper) request).body);
            }
            String tenantName;
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest req = attributes.getRequest();
                log.setUser(req.getHeader("user-name"));
            }

            log.accessTime((Long) request.getAttribute(RequestUtils.accessRecordTime))
                    .accessResultType(String.valueOf(response.getStatus()))
                    .times()
                    .setCreateTime(new Date());

            try {
                this.save(log);
            } catch (Exception e) {
                //ignore
                e.printStackTrace();
            }
            return log;
        } catch (Exception e) {
            log.error("record access interface log error", e);
            return null;
        }
    }

}
