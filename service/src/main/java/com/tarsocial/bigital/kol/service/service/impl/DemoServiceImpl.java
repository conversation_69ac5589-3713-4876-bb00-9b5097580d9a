package com.tarsocial.bigital.kol.service.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.tarsocial.bigital.kol.common.domain.dto.DemoDto;
import com.tarsocial.bigital.kol.common.domain.request.DemoRequest;
import com.tarsocial.bigital.kol.service.service.DemoService;
import com.tarsocial.bigital.kol.service.util.QueryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@Service
public class DemoServiceImpl implements DemoService {

    private static final String INDEX_NAME = "kg_gs2_sigma_*";

    @Qualifier("elasticsearchClient")
    @Resource
    private ElasticsearchClient client;

    @Override
    public DemoDto demo(DemoRequest search) {
        DemoDto result = new DemoDto();
        SearchRequest searchRequest = SearchRequest.of(s -> s
                .index(INDEX_NAME)
                .query(q -> q.bool(q2 -> q2.must(QueryUtil.term("kw_id", search.getId())))));
        log.info("getDoc SearchRequest :{}", searchRequest.toString());

        try {
            SearchResponse<Object> searchResponse = client.search(searchRequest, Object.class);
            List<Hit<Object>> hits = searchResponse.hits().hits();
            result.setStr(hits.get(0).source().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
