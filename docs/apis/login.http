### 登陆
POST http://{{host}}/login
Content-Type: application/json

{
  "method": "PASSPORT",
  "password": "TucbQxrulpyJ9UsPPeoJPgyEBOIteypSL1POON25Sff+xmmZP2MjeW+6p6f5l48lluZbtxkci9qh9mpeJA0cPEYHIqr3AEignn2UmKF9UR6qTbgxj/lP8ejS/OWr6bTsA34zp6RRzkIl374HdH5JdZ1dE0cA+VDcULNlhy24Fns=",
  "username": "<EMAIL>"
}

> {%
    client.global.set("JWT_TOKEN", response.body.data.accessToken);
%}

### 获取用户信息
GET http://{{host}}/common/auth/user
Content-Type: application/json
Authorization: Bearer {{JWT_TOKEN}}
X-Pg-Token: {{JWT_TOKEN}}
