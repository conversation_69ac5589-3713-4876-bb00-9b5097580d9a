###
POST {{host}}/getToken
Accept: application/json
Content-Type: application/json

{
  "grant_type": "client_credentials",
  "client_id": "r2WkFTMUCsfN8yJh",
  "client_secret": "w73B6KuNgl0pnAejYEG8vfPsWbHrTqhU"
}


> {%
    client.global.set("MENGNIU_TOKEN", response.body.data.accessToken);
%}

###
POST {{host}}/mengniu/getTodayHot
Accept: application/json
Content-Type: application/json

{
  "token": "{{MENGNIU_TOKEN}}",
  "hotDate": "2025-03-14"
}

###
POST http://{{host}}/mengniu/getHotspotDetail
Accept: application/json
Content-Type: application/json

{
  "token": "{{MENGNIU_TOKEN}}",
  "hotDate": "2025-03-12",
  "hotName": "饿了么12元无门槛红包活动及外卖骑手故事"
}


###
#POST http://{{host}}/mengniu/saveFinalResult
#Accept: application/json
#Content-Type: application/json
#Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************.p0W5hwouhO5hLjDXRwhtj1yI-jyfO659ZA5BQah9pAa6NuPKrRREINIK_PXsDWasispRY-GDFhSbraUMw_hUfw
#
#{
#  "token": "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************.p0W5hwouhO5hLjDXRwhtj1yI-jyfO659ZA5BQah9pAa6NuPKrRREINIK_PXsDWasispRY-GDFhSbraUMw_hUfw",
#  "hotDate": "2025-03-01"
#}
