FROM harbor.tarsocial.com/base-images/maven:3.8.6-openjdk-8-slim as builder

LABEL Author="SKYNE"

LABEL Date="2022.12.20"

LABEL Desc="Java run time on jdk8"

COPY . /opt/build

WORKDIR /opt/build

RUN mvn package -am -DskipTests -T 4

FROM harbor.tarsocial.com/base-images/openjdk:8-slim

COPY --from=builder  /opt/build/service/target/*.jar /wwwroot/www/app.jar

WORKDIR /wwwroot/www/

RUN chown -R nobody:root /wwwroot/


USER root

EXPOSE 8080

CMD [ "java", "-jar", "app.jar", "--server.port=8080", "--spring.profiles.active=BRANCH"]