<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.13</version> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.tarsocial.bigital</groupId>
    <artifactId>transmission</artifactId>
    <version>0.0.1</version>
    <packaging>pom</packaging>
    <name>transmission</name>
    <description>Golden Signal 2.0 Gateway and Service</description>

    <properties>
        <java.version>1.8</java.version>
        <spring-cloud.version>2021.0.3</spring-cloud.version>
        <mybatis-starter.version>3.0.1</mybatis-starter.version>
        <mybatis-plus.version>*******</mybatis-plus.version>

        <nimbusds.version>9.31</nimbusds.version>
        <javassist.version>3.29.2-GA</javassist.version>
        <elasticsearch.version>7.17.16</elasticsearch.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <fastjson-version>2.0.34</fastjson-version>
        <jsoup-version>1.14.3</jsoup-version>
    </properties>



    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>tarsocial-releases</id>
            <name>release Repository</name>
            <url>https://nexus.tarsocial.com/repository/bigital-teams/</url>
        </repository>
        <snapshotRepository>
            <id>tarsocial-snapshots</id>
            <name>snapshot Repository</name>
            <url>https://nexus.tarsocial.com/repository/bigital-teams/</url>
        </snapshotRepository>
    </distributionManagement>


    <repositories>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        <repository>
            <id>tarsocial-repository-public</id>
            <name>Bigital Repository</name>
            <url>https://nexus.tarsocial.com/repository/maven-public/</url>
            <releases>
                <updatePolicy>never</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
            <layout>default</layout>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>common</module>
<!--        <module>kol-gateway</module>-->
        <module>service</module>
    </modules>

</project>
